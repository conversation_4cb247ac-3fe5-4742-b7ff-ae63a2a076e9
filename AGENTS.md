# Pearl Project Agent Guidelines

## Build/Lint/Test Commands

### Backend (FastAPI)
```bash
docker-compose exec backend uv run pytest                    # Run all tests
docker-compose exec backend uv run pytest path/to/test.py   # Run single test file
docker-compose exec backend uv run pytest -k "test_name"    # Run specific test
docker-compose exec backend uv run ruff check               # Lint check
docker-compose exec backend uv run ruff format              # Format code
docker-compose exec backend uv run mypy app                 # Type check
```

### Frontend (Next.js/React Native)
```bash
docker-compose exec frontend pnpm lint                       # Lint all packages
docker-compose exec frontend pnpm lint:fix                   # Fix linting issues
docker-compose exec frontend pnpm format:fix                # Format code
docker-compose exec frontend pnpm typecheck                 # Type check
```

## Code Style Guidelines

### Python (Backend)
- Use Ruff for linting/formatting (line length: 88, target: Python 3.12)
- Follow PEP257 docstring conventions
- Prefer absolute imports over relative imports
- Use type hints for all function signatures
- Async/await patterns for database operations with SQLAlchemy
- Dependency injection with FastAPI's Depends()

### TypeScript (Frontend)
- Functional components only, no classes
- Named exports preferred
- Descriptive boolean names (isLoading, hasError)
- Directory names: lowercase-with-dashes
- File structure: exports → subcomponents → helpers → types
- Use React 19 features and Next.js 15 App Router
- Tailwind CSS for styling