# Pearl Application - Combined .gitignore
# This file combines patterns for Python (backend) and Node.js (frontend)

# =============================================================================
# ENVIRONMENT & SECRETS
# =============================================================================
.env
.env.*
!.env.example

# =============================================================================
# OPERATING SYSTEM
# =============================================================================
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux
*~

# =============================================================================
# IDEs & EDITORS
# =============================================================================
# IntelliJ / PyCharm
.idea/
*.iml
out/
gen/

# VS Code
.vscode/
.cursor/

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc

# =============================================================================
# PYTHON (Backend)
# =============================================================================
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff
instance/
.webassets-cache

# Scrapy stuff
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry (keep lock file for reproducibility)
# poetry.lock

# pdm
.pdm.toml
.pdm-python
.pdm-build/

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Virtual environments
.venv/
venv/
env/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# =============================================================================
# NODE.JS (Frontend)
# =============================================================================
# Dependencies
**/node_modules/
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# PNPM
.pnpm-store/
**/.pnpm-store/
.pnpm-debug.log*

# Testing
coverage/

# Turbo (monorepo)
.turbo/

# Next.js
.next/
next-env.d.ts

# Nitro
.nitro/
.output/

# Production builds
/build
dist/

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Cache
.cache
node-compile-cache/
**/node-compile-cache/
.eslintcache
.stylelintcache

# =============================================================================
# MOBILE (React Native / Expo)
# =============================================================================
# Expo
expo-env.d.ts
.expo/

# Mobile builds
*.app
*.tar.gz
*.apk
*.aab
*.ipa
*.zip
Payload/

# Mobile certificates and keys
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*

# Mobile build directories
web-build/
ios/
android/
.sentry/

# Tamagui (UI library)
tamagui-web.css
.tamagui/

# =============================================================================
# DOCKER & CONTAINERS
# =============================================================================
# Docker volumes and data
docker-compose.override.yml

# =============================================================================
# DOCUMENTATION & NOTES
# =============================================================================
# Claude AI assistant files
CLAUDE.md

# Temporary files
*.tmp
*.temp

# Certificates
*.pem

# =============================================================================
# PROJECT SPECIFIC
# =============================================================================
# Add any Pearl-specific ignores here
