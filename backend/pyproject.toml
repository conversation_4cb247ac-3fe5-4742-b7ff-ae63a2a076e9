[project]
name = "pearl-backend"
version = "0.1.0"
description = ""
authors = [{ name = "Your Name", email = "<EMAIL>" }]
readme = "README.md"
requires-python = ">=3.12,<3.13"
dependencies = [
    "fastapi[standard]>=0.115.12,<0.116.0",
    "sqladmin[full]>=0.20.1,<0.21.0",
    "typer>=0.15.2,<0.16.0",
    "langgraph>=0.4.8",
    "slack-sdk>=3.35.0,<4.0.0",
    "simple-salesforce>=1.12.6,<2.0.0",
    "lxml>=5.3.1,<6.0.0",
    "ipython>=9.0.2,<10.0.0",
    "sqlalchemy[asyncio]>=2.0.41",
    "alembic>=1.15.1,<2.0.0",
    "psycopg2-binary>=2.9.10,<3.0.0",
    "pydantic-settings>=2.8.1,<3.0.0",
    "bcrypt>=4.3.0,<5.0.0",
    "langchain-google-vertexai>=2.0.19,<3.0.0",
    "langchain-google-genai>=2.1.2,<3.0.0",
    "langchain-openai>=0.3.12,<0.4.0",
    "python-dotenv>=1.1.0,<2.0.0",
    "pgvector>=0.4.0,<0.5.0",
    "langgraph-checkpoint-postgres>=2.0.19,<3.0.0",
    "asgi-lifespan>=2.1.0,<3.0.0",
    "authlib>=1.5.2,<2.0.0",
    "numpy<2.0",
    "jinja2>=3.1.6",
    "customerio>=2.1",
    "langchain-linkup>=0.1.5",
    "langfuse==3.0.2",
    "langchain>=0.3.25",
    "asyncpg>=0.30.0",
    "langgraph-supervisor>=0.0.27",
    "langchain-core>=0.3.65",
    "google-cloud-storage>=2.18.0,<3.0.0",
    "aiohttp>=3.12.13",
    "openai>=1.88.0",
    "mistralai>=1.8.2",
    "google-api-python-client>=2.174.0",
    "google-api-python-client-stubs>=1.29.0",
    "hubspot-api-client>=12.0.0",
]

[project.optional-dependencies]
dev = [
    "ruff>=0.11.1",
    "pre-commit>=4.2.0",
    "mypy>=1.15.0",
    "ipdb>=0.13.13",
    "httpx>=0.28.1",
    "pytest>=8.3.5",
    "pytest-mock>=3.14.0",
    "pytest-anyio>=0.0.0",
    "types-pytz>=2025.1.0.20250318,<2026.0.0.0",
    "types-wtforms>=3.2.1.20250401,<*******",
    "types-authlib>=1.5.0.20250416,<*******",
]

[[tool.uv.index]]
name = "pytorch"
url = "https://download.pytorch.org/whl/cpu"
explicit = true

[tool.ruff]
# :warning: don't use :arrow_heading_down:, it's not encouraged by ruff
# lint.extend-select = []
# lint.extend-ignore = []
line-length = 88
target-version = "py312"
# https://beta.ruff.rs/docs/rules/#flake8-simplify-sim
lint.select = [
    "A",   # flake8-builtins: https://beta.ruff.rs/docs/rules/#flake8-builtins-a
    "ARG", # flake8-unused-arguments https://docs.astral.sh/ruff/rules/#flake8-unused-arguments-arg
    "B",   # flake8-bugbear: https://beta.ruff.rs/docs/rules/#flake8-bugbear-b
    "C4",  # flake8-comprehensions: https://beta.ruff.rs/docs/rules/#flake8-comprehensions-c4,
    "D",   # pydocstyle https://docs.astral.sh/ruff/rules/#pydocstyle-d
    # Add rules to the pep257 convention's defaults
    "D417",    # undocumented-param	Missing argument description in the docstring for {definition}: {name}
    "E",       # pycodestyle: https://beta.ruff.rs/docs/rules/#pycodestyle-e-w
    "ERA",     # eradicate: https://beta.ruff.rs/docs/rules/#flake8-tidy-imports-tid
    "F",       # Pyflakes: https://beta.ruff.rs/docs/rules/#pyflakes-f
    "FURB",    # flake8-return https://docs.astral.sh/ruff/rules/#flake8-return-furb
    "G",       # flake8-logging-format: https://beta.ruff.rs/docs/rules/#flake8-logging-format-g
    "I",       # isort: tony/09-12-Introduce_empty_fixit_config,
    "INP",     # flake8-no-pep420 https://docs.astral.sh/ruff/rules/#flake8-no-pep420-inp
    "ISC",     # flake8-implicit-str-concat https://docs.astral.sh/ruff/rules/#flake8-implicit-str-concat-isc
    "PGH003",  # blanket-type-ignor: ehttps://docs.astral.sh/ruff/rules/blanket-type-ignore/
    "PGH004",  # forces to explicit all noqa: https://docs.astral.sh/ruff/rules/blanket-noqa/
    "PGH005",  # invalid mock access: https://docs.astral.sh/ruff/rules/invalid-mock-access/
    "PLC0414", # useless-import-alias https://docs.astral.sh/ruff/rules/useless-import-alias/
    "PLE1300", # bad-str-strip-call: https://beta.ruff.rs/docs/rules/bad-str-strip-call/
    "PLE1307", # bad-string-format-type: https://beta.ruff.rs/docs/rules/bad-string-format-type/
    "PT",      # flake8-pytest-style: https://docs.astral.sh/ruff/rules/#flake8-pytest-style-pt
    "RUF007",  # zip-instead-of-pairwise: https://docs.astral.sh/ruff/rules/zip-instead-of-pairwise/
    "RUF013",  # implicit-optional: https://docs.astral.sh/ruff/rules/implicit-optional/
    "RUF016",  # invalid-index-type: https://docs.astral.sh/ruff/rules/invalid-index-type/
    "RUF017",  # quadratic-list-summation: https://docs.astral.sh/ruff/rules/quadratic-list-summation/
    "RUF019",  # unnecessary-key-check: https://docs.astral.sh/ruff/rules/unnecessary-key-check/
    "RUF020",  # never-union: https://docs.astral.sh/ruff/rules/never-union/
    "RUF024",  # mutable-fromkeys-value: https://docs.astral.sh/ruff/rules/mutable-fromkeys-value/
    "RUF030",  # assert-with-print-message: https://docs.astral.sh/ruff/rules/assert-with-print-message/
    "RUF100",  # unused-noqa: https://docs.astral.sh/ruff/rules/unused-noqa/
    "RUF101",  # redirected-noqa: https://docs.astral.sh/ruff/rules/redirected-noqa/
    "RUF200",  # invalid-pyproject-toml: https://docs.astral.sh/ruff/rules/invalid-pyproject-toml/
    "S",       # bandit: https://beta.ruff.rs/docs/rules/#flake8-bandit-s,
    "SIM",     # flake8-simplify https://docs.astral.sh/ruff/rules/#flake8-simplify-sim
    "T100",    # flake8-debugger: https://docs.astral.sh/ruff/rules/debugger/
    "T20",     # flak8-print https://docs.astral.sh/ruff/rules/#flake8-pie-pie
    "TC",      # flake8-type-checking https://docs.astral.sh/ruff/rules/#flake8-type-checking-tc
    "TID",     # flake8-tidy-imports: https://beta.ruff.rs/docs/rules/#flake8-tidy-imports-tid
    "UP",      # pyupgrade https://docs.astral.sh/ruff/rules/#pyupgrade-up
    "W",       # pycodestyle: https://beta.ruff.rs/docs/rules/#pycodestyle-e-w
]
lint.ignore = [
    # Ignored because of ruff formatting
    "E501", # E501: Line too long
    # Ignored because of SQLAlchemy
    "E711", # E711: Comparison to none should be 'if cond is none:' -> we need equality tests with SQLA
    "E712", # E712: Comparison to True should be 'if cond is True:' or 'if cond:' -> we need equality tests with SQLA
    "C416", # C416: Unnecessary comprehension -> Can break with ChunkedIterators without a comprehension
    # Remove rules from the pep257 convention's defaults
    # - Don't require docstrings on modules and packages for now
    "D100", # undocumented-public-module	Missing docstring in public module
    "D101", # undocumented-public-class
    "D102", # undocumented-public-method
    "D103", # undocumented-public-function      Missing docstring in public function
    "D104", # undocumented-public-package	    Missing docstring in public package
    "D105", # undocumented-magic-method	        Missing docstring in magic method
    "D106", # undocumented-public-nested-class	Missing docstring in public nested class
    "D107", # undocumented-public-init	        Missing docstring in __init__
    # - These rules are desirable for consistency of style but either have no fix, are too strict for now, or are too hard to noqa automatically
    "D200", # fits-on-one-line	One-line docstring should fit on one line
    "D205", # blank-line-after-summary	1 blank line required between summary line and description
    "D400", # ends-in-period	First line should end with a period
    "D401", # non-imperative-mood	First line of docstring should be in imperative mood: "{first_line}"
    # Because we don't think it brings any value, or it's not clearer
    "C401",   # C401: Unnecessary generator - rewrite as a set comprehension.
    "C402",   # C402: Unnecessary generator - rewrite as a dict comprehension.
    "C408",   # C408: Unnecessary (dict/list/tuple) call - rewrite as a literal.
    "G004",   # G004: Logging statements should not use f"..." for their first argument (only in Python 3.6+)
    "SIM103", # Not activating it, https://alanhealth.slack.com/archives/C19FZEB41/p1723013031633959
    "SIM105", # Not activating it, I feel knowing what does `contextlib.suppress({exception})` does is more complex than a try-except-pass
    "SIM110", # Not activating it, I feel it's more complex to understand the usage of an any rather than a for-loop and if
    "SIM210", # Not activating it, I don't think it brings clarity
    "SIM211", # Not activating it, I don't think it brings clarity either
    "SIM212", # Not activating it, I don't think it brings clarity either either
    "UP028",  #	Not activating it, I don't think it brings clarity, yield-in-for-loop	Replace yield over for loop with yield from	:heavy_check_mark: :hammer_and_wrench:
    # <start> TODO: to activate one at a time as they are nice
    "UP007", #	non-pep604-annotation	Use X | Y for type annotations	:heavy_check_mark: :hammer_and_wrench:
    "UP031", # https://docs.astral.sh/ruff/rules/printf-string-formatting/
    "E741",  # E741: Do not use variables named 'I', 'O', or 'l'
    # </end>
    # <start> TODO: to activate one at a time as they _could_ be nice
    "SIM102", # collapsible-if	Use a single if statement instead of nested if statements	:heavy_check_mark: :hammer_and_wrench:
    "SIM108", # if-else-block-instead-of-if-exp	Use ternary operator {contents} instead of if-else-block	:heavy_check_mark: :hammer_and_wrench:
    "SIM113", # enumerate-for-loop	Use enumerate() for index variable {index} in for loop	:heavy_check_mark: :hammer_and_wrench:
    "SIM114", # if-with-same-arms	Combine if branches using logical or operator	:heavy_check_mark: :hammer_and_wrench:
    "SIM115", # open-file-with-context-handler	Use context handler for opening files	:heavy_check_mark: :hammer_and_wrench:
    "SIM116", # if-else-block-instead-of-dict-lookup	Use a dictionary instead of consecutive if statements	:heavy_check_mark: :hammer_and_wrench:
    "SIM401", # if-else-block-instead-of-dict-get	Use {contents} instead of an if block	:heavy_check_mark: :hammer_and_wrench:
    "SIM910", # dict-get-with-none-default	Use {expected} instead of {actual}	:heavy_check_mark: :hammer_and_wrench:
    "B904",   # https://docs.astral.sh/ruff/rules/raise-without-from-inside-except/
    "B026",   # https://docs.astral.sh/ruff/rules/star-arg-unpacking-after-keyword-arg/
    "S101",   # https://docs.astral.sh/ruff/rules/assert/
    "S105",   # https://docs.astral.sh/ruff/rules/hardcoded-password-string/
    "B008",   # FastAPI injection using Depends() function args
    "B905",   # added when changing the target version to python 3.11, https://docs.astral.sh/ruff/rules/zip-without-explicit-strict/
    "PT011",  # https://docs.astral.sh/ruff/rules/pytest-raises-too-broad/
    # </end>
    # <start> TODO: to activate one at a time as they are nice, but are buggy for now
    "TC001", # 	typing-only-first-party-import	Move application import {} into a type-checking block	:heavy_check_mark: :hammer_and_wrench:
    "TC002", # 	typing-only-third-party-import	Move third-party import {} into a type-checking block	:heavy_check_mark: :hammer_and_wrench:
    "TC003", # 	typing-only-standard-library-import	Move standard library import {} into a type-checking block	:heavy_check_mark: :hammer_and_wrench:
    # </end>
    "T201", # print-found
]
exclude = [
    "migrations/**",  # Ignore everything in the migrations folder
    "check_merge.py",
]

[tool.ruff.lint.pydocstyle]
convention = "pep257"

[tool.ruff.lint.isort]
combine-as-imports = true

[tool.ruff.lint.flake8-builtins]
builtins-ignorelist = ["id"]

[tool.ruff.lint.flake8-tidy-imports]
ban-relative-imports = "all"

[tool.mypy]
python_version = "3.12"
follow_imports = "normal"
exclude = "migrations"

# Some modules don't include a __init__.py file so enabling namespace packages here
namespace_packages = true

[[tool.mypy.overrides]]
module = ["pgvector.*", "customerio.*", "google.cloud.storage.*", "hubspot.*"]
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = [
    "transformers.*",
    "torch.*",
    "huggingface_hub.*",
    "langfuse.*",
    "opentelemetry.*",
]
ignore_missing_imports = true
follow_imports = "skip"

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q"
testpaths = ["app/**/tests"]
filterwarnings = ["ignore:PydanticDeprecatedSince20:DeprecationWarning"]
