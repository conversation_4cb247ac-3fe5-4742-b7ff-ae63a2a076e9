"""add_summary_column_to_crm_account_access

Revision ID: c336b1b165cd
Revises: a319b42cf776
Create Date: 2025-07-22 12:04:32.056391

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c336b1b165cd'
down_revision: Union[str, None] = 'a319b42cf776'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('crm_account_access', sa.Column('summary', sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('crm_account_access', 'summary')
    # ### end Alembic commands ###
