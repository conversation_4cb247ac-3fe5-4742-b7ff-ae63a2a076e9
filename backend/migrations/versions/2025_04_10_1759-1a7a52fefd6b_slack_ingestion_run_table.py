"""slack_ingestion_run table

Revision ID: 1a7a52fefd6b
Revises: fa45b4e0f9c6
Create Date: 2025-04-10 17:59:21.608968

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1a7a52fefd6b'
down_revision: Union[str, None] = 'fa45b4e0f9c6'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('slack_ingestion_run',
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('channel_id', sa.String(), nullable=False),
    sa.Column('slice_from_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('slice_to_time', sa.DateTime(timezone=True), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('messages_processed', sa.Integer(), nullable=False),
    sa.Column('inserts', sa.Integer(), nullable=False),
    sa.Column('updates', sa.Integer(), nullable=False),
    sa.Column('deletes', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('slack_ingestion_run')
    # ### end Alembic commands ###
