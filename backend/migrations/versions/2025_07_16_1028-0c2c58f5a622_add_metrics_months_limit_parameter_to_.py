"""Add metrics_months_limit parameter to salesforce_field_mapping

Revision ID: 0c2c58f5a622
Revises: 326e8bfada42
Create Date: 2025-07-16 10:28:34.563053

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0c2c58f5a622'
down_revision: Union[str, None] = '326e8bfada42'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('salesforce_field_mapping', sa.Column('metrics_months_limit', sa.Integer(), nullable=False, server_default='3'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('salesforce_field_mapping', 'metrics_months_limit')
    # ### end Alembic commands ###
