"""add organization owner

Revision ID: 07dbe10458b4
Revises: 5e4d9ed88162
Create Date: 2025-04-01 13:31:15.974150

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '07dbe10458b4'
down_revision: Union[str, None] = '5e4d9ed88162'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('organization', sa.Column('owner_id', sa.UUID(), nullable=False))
    op.create_index(op.f('ix_organization_owner_id'), 'organization', ['owner_id'], unique=False)
    op.create_foreign_key(None, 'organization', 'user', ['owner_id'], ['id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'organization', type_='foreignkey')
    op.drop_index(op.f('ix_organization_owner_id'), table_name='organization')
    op.drop_column('organization', 'owner_id')
    # ### end Alembic commands ###
