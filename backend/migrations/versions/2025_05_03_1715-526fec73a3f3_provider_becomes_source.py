"""provider becomes source

Revision ID: 526fec73a3f3
Revises: 229d37c869d8
Create Date: 2025-05-03 17:15:39.838623

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '526fec73a3f3'
down_revision: Union[str, None] = '229d37c869d8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.drop_constraint('uq_crm_access_user_account', 'crm_account_access', type_='unique')
    op.drop_constraint('uix_environment_provider', 'integration_config', type_='unique')

    op.alter_column('crm_account_access', 'provider', new_column_name='source')
    op.alter_column('integration_config', 'provider', new_column_name='source')

    op.create_unique_constraint('uq_crm_access_user_account', 'crm_account_access',
                                ['tenant_id', 'source', 'crm_user_id', 'crm_account_id'])
    op.create_unique_constraint('uix_environment_source', 'integration_config',
                                ['environment_id', 'source'])



def downgrade() -> None:
    op.drop_constraint('uix_environment_source', 'integration_config', type_='unique')
    op.drop_constraint('uq_crm_access_user_account', 'crm_account_access', type_='unique')

    op.alter_column('integration_config', 'source', new_column_name='provider')
    op.alter_column('crm_account_access', 'source', new_column_name='provider')

    op.create_unique_constraint('uix_environment_provider', 'integration_config',
                                ['environment_id', 'provider'])
    op.create_unique_constraint('uq_crm_access_user_account', 'crm_account_access',
                                ['tenant_id', 'provider', 'crm_user_id', 'crm_account_id'])
