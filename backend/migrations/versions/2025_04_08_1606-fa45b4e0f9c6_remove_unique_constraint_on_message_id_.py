"""remove unique constraint on message_id+channel

Revision ID: fa45b4e0f9c6
Revises: eb6e173646cd
Create Date: 2025-04-08 16:06:09.302697

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fa45b4e0f9c6'
down_revision: Union[str, None] = 'eb6e173646cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_channel_message', 'messaging_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_message', 'messaging_raw_data', ['tenant_id', 'message_id'])
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint('uq_tenant_message', 'messaging_raw_data', type_='unique')
    op.create_unique_constraint('uq_tenant_channel_message', 'messaging_raw_data', ['tenant_id', 'channel_id', 'message_id'])
    # ### end Alembic commands ###
