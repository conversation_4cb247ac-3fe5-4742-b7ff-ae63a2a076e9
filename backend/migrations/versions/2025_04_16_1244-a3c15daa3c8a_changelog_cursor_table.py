"""changelog_cursor table

Revision ID: a3c15daa3c8a
Revises: 6626a567fbca
Create Date: 2025-04-16 12:44:37.274613

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a3c15daa3c8a'
down_revision: Union[str, None] = '6626a567fbca'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('changelog_cursor',
    sa.Column('cursor_id', sa.String(length=255), nullable=False),
    sa.Column('cursor_position', sa.BigInteger(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.<PERSON>umn('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'cursor_id', name='uq_tenant_cursor_id')
    )
    op.create_index(op.f('ix_changelog_cursor_cursor_id'), 'changelog_cursor', ['cursor_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_changelog_cursor_cursor_id'), table_name='changelog_cursor')
    op.drop_table('changelog_cursor')
    # ### end Alembic commands ###
