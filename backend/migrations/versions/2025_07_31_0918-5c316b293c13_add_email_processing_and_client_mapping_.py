"""Add email processing and client mapping tables

Revision ID: 5c316b293c13
Revises: c336b1b165cd
Create Date: 2025-07-31 09:18:09.494018

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '5c316b293c13'
down_revision: Union[str, None] = 'c336b1b165cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_client_mapping',
    sa.Column('crm_account_id', sa.String(), nullable=False),
    sa.Column('domain', sa.String(), nullable=True),
    sa.Column('email_address', sa.String(), nullable=True),
    sa.Column('contact_name_pattern', sa.String(), nullable=True),
    sa.Column('mapping_type', sa.String(), nullable=False),
    sa.Column('confidence_score', sa.Float(), nullable=False),
    sa.Column('created_by_user', sa.Boolean(), nullable=False),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'crm_account_id', 'mapping_type', 'contact_name_pattern', name='uq_email_mapping_contact_name'),
    sa.UniqueConstraint('tenant_id', 'crm_account_id', 'mapping_type', 'domain', name='uq_email_mapping_domain'),
    sa.UniqueConstraint('tenant_id', 'crm_account_id', 'mapping_type', 'email_address', name='uq_email_mapping_address')
    )
    op.create_index(op.f('ix_email_client_mapping_crm_account_id'), 'email_client_mapping', ['crm_account_id'], unique=False)
    op.create_table('email_processing_run',
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('crm_account_id', sa.String(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('emails_processed', sa.Integer(), nullable=False),
    sa.Column('emails_filtered', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('sync_window_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('sync_window_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('email_processing_run')
    op.drop_index(op.f('ix_email_client_mapping_crm_account_id'), table_name='email_client_mapping')
    op.drop_table('email_client_mapping')
    # ### end Alembic commands ###
