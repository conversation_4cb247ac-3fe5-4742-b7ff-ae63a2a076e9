"""Add is_admin

Revision ID: bd144046d1a2
Revises: 4d5203653a61
Create Date: 2025-06-02 15:00:37.906488

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "bd144046d1a2"
down_revision: Union[str, None] = "4d5203653a61"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "organization_member", sa.<PERSON>umn("is_admin", sa.<PERSON>(), nullable=True)
    )
    op.execute("UPDATE organization_member SET is_admin = FALSE WHERE is_admin IS NULL")
    op.alter_column(
        "organization_member", "is_admin", existing_type=sa.<PERSON>(), nullable=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("organization_member", "is_admin")
    # ### end Alembic commands ###
