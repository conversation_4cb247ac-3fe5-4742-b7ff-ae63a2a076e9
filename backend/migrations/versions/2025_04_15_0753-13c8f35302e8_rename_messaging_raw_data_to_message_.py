"""rename messaging_raw_data to message_raw_data

Revision ID: 13c8f35302e8
Revises: f9a019822324
Create Date: 2025-04-15 07:53:14.462187

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '13c8f35302e8'
down_revision: Union[str, None] = 'f9a019822324'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    op.rename_table('messaging_raw_data', 'message_raw_data')
    op.execute('ALTER INDEX messaging_raw_data_pkey RENAME TO message_raw_data_pkey')

def downgrade() -> None:
    op.rename_table('message_raw_data', 'messaging_raw_data')
    op.execute('ALTER INDEX message_raw_data_pkey RENAME TO messaging_raw_data_pkey')
