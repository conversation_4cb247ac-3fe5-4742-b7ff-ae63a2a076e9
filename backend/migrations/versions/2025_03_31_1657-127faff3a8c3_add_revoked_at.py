"""Add revoked_at

Revision ID: 127faff3a8c3
Revises: 3d6c2743e1f8
Create Date: 2025-03-31 16:57:13.055669

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '127faff3a8c3'
down_revision: Union[str, None] = '3d6c2743e1f8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('refresh_token', sa.Column('revoked_at', sa.DateTime(timezone=True), nullable=True))
    op.create_index(op.f('ix_refresh_token_token'), 'refresh_token', ['token'], unique=False)
    op.create_index(op.f('ix_refresh_token_user_id'), 'refresh_token', ['user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_refresh_token_user_id'), table_name='refresh_token')
    op.drop_index(op.f('ix_refresh_token_token'), table_name='refresh_token')
    op.drop_column('refresh_token', 'revoked_at')
    # ### end Alembic commands ###
