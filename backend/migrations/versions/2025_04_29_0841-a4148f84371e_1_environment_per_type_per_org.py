"""1 environment per type per org

Revision ID: a4148f84371e
Revises: 343d249d7425
Create Date: 2025-04-29 08:41:10.902598

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a4148f84371e'
down_revision: Union[str, None] = '343d249d7425'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_unique_constraint('uix_organization_type', 'environment', ['organization_id', 'type'])
    op.drop_column('environment', 'name')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.add_column('environment', sa.Column('name', sa.VARCHAR(), nullable=True))
    op.execute("UPDATE environment SET name = type::text")
    op.alter_column('environment', 'name', nullable=False)
    op.drop_constraint('uix_organization_type', 'environment', type_='unique')
