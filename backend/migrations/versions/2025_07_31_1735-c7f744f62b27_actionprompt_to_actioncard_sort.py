"""actionprompt to actioncard, sort

Revision ID: c7f744f62b27
Revises: 5c316b293c13
Create Date: 2025-07-31 17:35:36.806243

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'c7f744f62b27'
down_revision: Union[str, None] = '5c316b293c13'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_cards',
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('description', sa.Text(), nullable=False),
    sa.Column('prompt_content', sa.Text(), nullable=False),
    sa.Column('sort_order', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('organization_member_id', sa.UUID(), nullable=False),
    sa.Column('environment_id', sa.UUID(), nullable=False),
    sa.Column('crm_account_id', sa.String(length=50), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['environment_id'], ['environment.id'], ),
    sa.ForeignKeyConstraint(['organization_member_id'], ['organization_member.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('title', 'description', 'environment_id', 'organization_member_id', 'crm_account_id', name='uq_action_card_scope')
    )
    op.create_index(op.f('ix_action_cards_environment_id'), 'action_cards', ['environment_id'], unique=False)
    op.create_index(op.f('ix_action_cards_organization_member_id'), 'action_cards', ['organization_member_id'], unique=False)
    op.create_index(op.f('ix_action_cards_title'), 'action_cards', ['title'], unique=False)
    # Drop indexes if they exist (some may not exist on production)
    connection = op.get_bind()
    
    # Check and drop ix_action_prompts_environment_id if it exists
    env_index_exists = connection.execute(sa.text("""
        SELECT EXISTS (SELECT 1 FROM pg_indexes 
                      WHERE indexname = 'ix_action_prompts_environment_id' 
                      AND tablename = 'action_prompts')
    """)).scalar()
    if env_index_exists:
        op.drop_index(op.f('ix_action_prompts_environment_id'), table_name='action_prompts')
    
    # Check and drop ix_action_prompts_organization_member_id if it exists  
    member_index_exists = connection.execute(sa.text("""
        SELECT EXISTS (SELECT 1 FROM pg_indexes 
                      WHERE indexname = 'ix_action_prompts_organization_member_id' 
                      AND tablename = 'action_prompts')
    """)).scalar()
    if member_index_exists:
        op.drop_index(op.f('ix_action_prompts_organization_member_id'), table_name='action_prompts')
    
    # Drop title index (this should exist)
    op.drop_index(op.f('ix_action_prompts_title'), table_name='action_prompts')
    op.drop_table('action_prompts')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('action_prompts',
    sa.Column('title', sa.VARCHAR(length=200), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('prompt_content', sa.TEXT(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('sort_order', sa.INTEGER(), server_default=sa.text('9999'), autoincrement=False, nullable=False),
    sa.Column('organization_member_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('environment_id', sa.UUID(), autoincrement=False, nullable=True),
    sa.Column('crm_account_id', sa.VARCHAR(length=50), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['environment_id'], ['environment.id'], name=op.f('action_prompts_environment_id_fkey')),
    sa.ForeignKeyConstraint(['organization_member_id'], ['organization_member.id'], name=op.f('action_prompts_organization_member_id_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('action_cards_pkey')),
    sa.UniqueConstraint('title', 'description', 'environment_id', 'organization_member_id', 'crm_account_id', name=op.f('uq_action_prompt_scope'), postgresql_include=[], postgresql_nulls_not_distinct=False)
    )
    op.create_index(op.f('ix_action_prompts_title'), 'action_prompts', ['title'], unique=False)
    op.create_index(op.f('ix_action_prompts_organization_member_id'), 'action_prompts', ['organization_member_id'], unique=False)
    op.create_index(op.f('ix_action_prompts_environment_id'), 'action_prompts', ['environment_id'], unique=False)
    op.drop_index(op.f('ix_action_cards_title'), table_name='action_cards')
    op.drop_index(op.f('ix_action_cards_organization_member_id'), table_name='action_cards')
    op.drop_index(op.f('ix_action_cards_environment_id'), table_name='action_cards')
    op.drop_table('action_cards')
    # ### end Alembic commands ###
