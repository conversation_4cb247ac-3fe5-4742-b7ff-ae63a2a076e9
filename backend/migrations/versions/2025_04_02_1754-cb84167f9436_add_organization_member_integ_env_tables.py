"""add organization [member,integ,env] tables

Revision ID: cb84167f9436
Revises: 07dbe10458b4
Create Date: 2025-04-02 17:54:06.492230

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'cb84167f9436'
down_revision: Union[str, None] = '07dbe10458b4'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('environment',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('type', sa.Enum('sandbox', 'prod', name='environmenttype'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organization.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_environment_organization_id'), 'environment', ['organization_id'], unique=False)
    op.create_table('organization_member',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organization.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'organization_id', name='uix_user_organization')
    )
    op.create_index(op.f('ix_organization_member_organization_id'), 'organization_member', ['organization_id'], unique=False)
    op.create_index(op.f('ix_organization_member_user_id'), 'organization_member', ['user_id'], unique=False)
    op.create_table('integration_config',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('environment_id', sa.UUID(), nullable=False),
    sa.Column('provider', sa.Enum('slack', 'salesforce', name='integrationprovider'), nullable=False),
    sa.Column('config', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['environment_id'], ['environment.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('environment_id', 'provider', name='uix_environment_provider')
    )
    op.create_index(op.f('ix_integration_config_environment_id'), 'integration_config', ['environment_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_integration_config_environment_id'), table_name='integration_config')
    op.drop_table('integration_config')
    op.drop_index(op.f('ix_organization_member_user_id'), table_name='organization_member')
    op.drop_index(op.f('ix_organization_member_organization_id'), table_name='organization_member')
    op.drop_table('organization_member')
    op.drop_index(op.f('ix_environment_organization_id'), table_name='environment')
    op.drop_table('environment')
    # ### end Alembic commands ###

    # manual additions
    op.execute("DROP TYPE IF EXISTS environmenttype;")
    op.execute("DROP TYPE IF EXISTS integrationprovider;")

