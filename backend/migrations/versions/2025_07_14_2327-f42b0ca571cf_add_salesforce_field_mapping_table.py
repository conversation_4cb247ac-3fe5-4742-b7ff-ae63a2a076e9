"""add_salesforce_field_mapping_table

Revision ID: f42b0ca571cf
Revises: 1865c559f6e1
Create Date: 2025-07-14 23:27:08.527497

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f42b0ca571cf'
down_revision: Union[str, None] = '1865c559f6e1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('salesforce_field_mapping',
    sa.Column('organization_id', sa.UUID(), nullable=False),
    sa.Column('opportunity_amount_field', sa.String(length=255), nullable=False),
    sa.Column('opportunity_stage_field', sa.String(length=255), nullable=False),
    sa.Column('opportunity_owner_field', sa.String(length=255), nullable=False),
    sa.Column('opportunity_probability_field', sa.String(length=255), nullable=False),
    sa.Column('closed_won_stage_pattern', sa.String(length=255), nullable=False),
    sa.Column('closed_lost_stage_pattern', sa.String(length=255), nullable=False),
    sa.Column('forecast_probability_multiplier', sa.Float(), nullable=False),
    sa.Column('use_probability_field', sa.Boolean(), nullable=False),
    sa.Column('quota_object', sa.String(length=255), nullable=True),
    sa.Column('quota_amount_field', sa.String(length=255), nullable=True),
    sa.Column('quota_user_field', sa.String(length=255), nullable=True),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.ForeignKeyConstraint(['organization_id'], ['organization.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('organization_id', name='uq_salesforce_field_mapping_organization_id')
    )
    op.create_index(op.f('ix_salesforce_field_mapping_organization_id'), 'salesforce_field_mapping', ['organization_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_salesforce_field_mapping_organization_id'), table_name='salesforce_field_mapping')
    op.drop_table('salesforce_field_mapping')
    # ### end Alembic commands ###
