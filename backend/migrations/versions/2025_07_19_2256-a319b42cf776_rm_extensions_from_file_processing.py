"""rm extensions from file processing

Revision ID: a319b42cf776
Revises: 0c2c58f5a622
Create Date: 2025-07-19 22:56:05.914707

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'a319b42cf776'
down_revision: Union[str, None] = '0c2c58f5a622'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('file_processing_run', 'extensions')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('file_processing_run', sa.Column('extensions', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
