"""Remove password from user table

Revision ID: 6b7652df1c61
Revises: a5bfba36c1c8
Create Date: 2025-04-29 20:04:47.916628

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6b7652df1c61'
down_revision: Union[str, None] = 'a5bfba36c1c8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    op.drop_column('user', 'hashed_password')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    op.add_column('user', sa.Column('hashed_password', sa.VARCHAR(length=1024), autoincrement=False))
    # Add dummy hashed password to existing records
    conn = op.get_bind()
    conn.execute(
        sa.text(
            "UPDATE user SET hashed_password = 'dummy_hashed_password' WHERE hashed_password IS NULL"
        )
    )
    op.alter_column('user', 'hashed_password', nullable=False)
    # ### end Alembic commands ###
