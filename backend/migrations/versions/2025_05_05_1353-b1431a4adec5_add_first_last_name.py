"""Add first/last name

Revision ID: b1431a4adec5
Revises: dcc43c55720b
Create Date: 2025-05-05 13:53:22.514146

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b1431a4adec5'
down_revision: Union[str, None] = 'dcc43c55720b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('user', sa.Column('first_name', sa.String(), nullable=True))
    op.add_column('user', sa.Column('last_name', sa.String(), nullable=True))
    op.execute(
        """
        UPDATE "user" SET first_name = 'first_name' WHERE first_name IS NULL;
        UPDATE "user" SET last_name = 'last_name' WHERE last_name IS NULL;
        """
    )
    op.alter_column('user', 'first_name', nullable=False)
    op.alter_column('user', 'last_name', nullable=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('user', 'last_name')
    op.drop_column('user', 'first_name')
    # ### end Alembic commands ###
