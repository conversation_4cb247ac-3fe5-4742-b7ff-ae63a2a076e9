"""add message_raw_data.parent_id

Revision ID: 6d0803886cc5
Revises: f8508aa3b7cb
Create Date: 2025-04-20 07:50:17.361729

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6d0803886cc5'
down_revision: Union[str, None] = 'f8508aa3b7cb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('message_raw_data', sa.Column('parent_id', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('message_raw_data', 'parent_id')
    # ### end Alembic commands ###
