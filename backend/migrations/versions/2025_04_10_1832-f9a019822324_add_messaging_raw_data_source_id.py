"""add messaging_raw_data.source_id

Revision ID: f9a019822324
Revises: 1a7a52fefd6b
Create Date: 2025-04-10 18:32:17.393268

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f9a019822324'
down_revision: Union[str, None] = '1a7a52fefd6b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('messaging_raw_data', sa.Column('source_id', sa.Text(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('messaging_raw_data', 'source_id')
    # ### end Alembic commands ###
