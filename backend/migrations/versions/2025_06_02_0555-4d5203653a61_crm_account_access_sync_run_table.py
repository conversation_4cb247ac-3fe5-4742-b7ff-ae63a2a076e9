"""crm_account_access_sync_run table

Revision ID: 4d5203653a61
Revises: e8194b2e8ed8
Create Date: 2025-06-02 05:55:10.195372

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '4d5203653a61'
down_revision: Union[str, None] = 'e8194b2e8ed8'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('crm_account_access_sync_run',
    sa.Column('source', sa.String(), nullable=False),
    sa.Column('crm_user_id', sa.String(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('new_access_count', sa.Integer(), nullable=False),
    sa.Column('old_access_count', sa.Integer(), nullable=False),
    sa.Column('error_message', sa.String(length=1024), nullable=True),
    sa.Column('run_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('run_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.drop_table('salesforce_access_sync_run')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('salesforce_access_sync_run',
    sa.Column('salesforce_user_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('status', sa.VARCHAR(length=20), autoincrement=False, nullable=False),
    sa.Column('new_access_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('old_access_count', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('error_message', sa.VARCHAR(length=1024), autoincrement=False, nullable=True),
    sa.Column('run_start', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('run_end', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.Column('tenant_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('salesforce_access_sync_run_pkey'))
    )
    op.drop_table('crm_account_access_sync_run')
    # ### end Alembic commands ###
