"""document store

Revision ID: f8508aa3b7cb
Revises: 27cb62287803
Create Date: 2025-04-18 14:56:24.721128

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f8508aa3b7cb'
down_revision: Union[str, None] = '27cb62287803'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('document',
    sa.Column('document_id', sa.String(), nullable=False),
    sa.Column('source_id', sa.String(), nullable=False),
    sa.Column('content', sa.Text(), nullable=False),
    sa.Column('tags', sa.ARRAY(sa.String()), nullable=True),
    sa.Column('tenant_id', sa.UUID(), nullable=False),
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_tags', 'document', ['tags'], unique=False, postgresql_using='gin')
    op.create_index('idx_tenant_document_id', 'document', ['tenant_id', 'document_id'], unique=False)
    op.create_index('idx_tenant_source_id', 'document', ['tenant_id', 'source_id'], unique=False)
    op.create_index(op.f('ix_document_document_id'), 'document', ['document_id'], unique=False)
    op.create_index(op.f('ix_document_source_id'), 'document', ['source_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_document_source_id'), table_name='document')
    op.drop_index(op.f('ix_document_document_id'), table_name='document')
    op.drop_index('idx_tenant_source_id', table_name='document')
    op.drop_index('idx_tenant_document_id', table_name='document')
    op.drop_index('idx_tags', table_name='document', postgresql_using='gin')
    op.drop_table('document')
    # ### end Alembic commands ###
