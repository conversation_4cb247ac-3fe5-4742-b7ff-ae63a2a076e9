"""add Organization model

Revision ID: 47e2472762f5
Revises: dbaab4bdc10f
Create Date: 2025-03-25 17:35:08.694022

"""

from collections.abc import Sequence
from typing import Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "47e2472762f5"
down_revision: Union[str, None] = "dbaab4bdc10f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "organizations",
        sa.Column("id", sa.UUID(), nullable=False),
        sa.<PERSON>umn("name", sa.String(), nullable=False),
        sa.Column("domain", sa.String(), nullable=True),
        sa.<PERSON>umn("is_active", sa.<PERSON>(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("organizations")
    # ### end Alembic commands ###
