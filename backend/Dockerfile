# Multi-stage Dockerfile for Pearl Backend
FROM python:3.12.8-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    UV_CACHE_DIR=/tmp/uv_cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    build-essential \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

WORKDIR /app

# Copy uv files
COPY pyproject.toml uv.lock ./

# Development stage
FROM base as development

# Don't create venv during build for development - let docker-compose handle it
# This avoids conflicts with volume mounts

# Copy source code
COPY . .

EXPOSE 8000

CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM base as production

# Install only production dependencies
RUN uv sync --no-dev && rm -rf $UV_CACHE_DIR

# Copy source code
COPY . .

# Create non-root user
RUN groupadd -r pearl && useradd -r -g pearl pearl
RUN chown -R pearl:pearl /app
USER pearl

EXPOSE 8000

CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
