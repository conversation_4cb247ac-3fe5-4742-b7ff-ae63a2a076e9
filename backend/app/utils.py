from urllib.parse import urlparse


def extract_domain_from_url(url: str) -> str | None:
    if not url:
        return None

    try:
        # Add scheme if missing to help urlparse
        parsed_url = url if "://" in url else f"http://{url}"
        parsed = urlparse(parsed_url)

        if not parsed.netloc:
            return None

        # Remove www. prefix if present
        domain = parsed.netloc.lower()
        domain = domain.removeprefix("www.")

        return domain
    except Exception:
        return None
