import pytest
from langchain_core.messages import AIMessageChunk, ToolMessage
from langgraph.types import Command, Interrupt

from app.agentic.graph.stream_encoder import DataStreamProtocolEncoder
from app.agentic.graph.stream_output_processor import StreamOutputProcessor

processor = StreamOutputProcessor(DataStreamProtocolEncoder())


@pytest.mark.anyio
async def test_stream_output_processor_AIMessage():
    message = AIMessageChunk(
        content="Hello, world!",
        tool_calls=[{"name": "test_tool", "id": "123", "args": {"key": "value"}}],
    )

    output = [o async for o in processor.process(message)]

    assert output == [
        '0:"Hello, world!"\n',
        '9:{"toolName": "test_tool", "toolCallId": "123", "args": {"key": "value"}}\n',
    ]


@pytest.mark.anyio
async def test_stream_output_processor_AIMessage__empty_text_with_tool_call():
    message = AIMessageChunk(
        content="",
        tool_calls=[{"name": "test_tool", "id": "123", "args": {"key": "value"}}],
    )

    output = [o async for o in processor.process(message)]

    assert output == [
        '9:{"toolName": "test_tool", "toolCallId": "123", "args": {"key": "value"}}\n',
    ]


@pytest.mark.anyio
async def test_stream_output_processor_ToolMessage():
    message = ToolMessage(
        name="test_tool",
        tool_call_id="123",
        content="Tool result content",
    )

    output = [o async for o in processor.process(message)]

    assert output == [
        'a:{"toolName": "test_tool", "toolCallId": "123", "result": "Tool result content"}\n'
    ]


@pytest.mark.anyio
async def test_stream_output_processor_InterruptHandler():
    interrupt = Interrupt(value="Test interrupt", resumable=True, ns="test_ns")

    output = [o async for o in processor.process(interrupt)]

    assert output == [
        '2:[{"value": "Test interrupt", "resumable": true, "ns": "test_ns"}]\n'
    ]


@pytest.mark.anyio
async def test_stream_output_processor_CommandHandler():
    command = Command(update={"action": "test_action"})

    output = [o async for o in processor.process(command)]

    assert output == ['0:{"action": "test_action"}\n']
