import pytest
from langchain_core.messages import AIMessage, HumanMessage, ToolMessage
from langgraph.checkpoint.memory import InMemorySaver
from langgraph.checkpoint.postgres import CheckpointTuple

from app.agentic.graph.chat_history_repository import ChatHistoryRepository
from app.agentic.schemas import (
    ThreadMessage,
)


@pytest.fixture
def chat_history_repository():
    return ChatHistoryRepository(checkpointer=InMemorySaver())


def test_parse_historical_message_data_with_ai_message(chat_history_repository):
    ai_message = AIMessage(id="1234", content="AI response")

    result = chat_history_repository._parse_historical_message_data(ai_message)

    assert result == ThreadMessage(
        id="1234",
        role="assistant",
        content="AI response",
    )


def test_parse_historical_message_data_with_human_message(chat_history_repository):
    human_message = HumanMessage(id="5678", content="Human question")

    result = chat_history_repository._parse_historical_message_data(human_message)
    assert result == ThreadMessage(
        id="5678",
        role="user",
        content="Human question",
    )


def test_get_paginated_messages(chat_history_repository):
    messages = [HumanMessage(content=f"Message {i}") for i in range(10)]

    page1 = chat_history_repository._get_paginated_messages(messages, page=1, size=3)
    assert len(page1) == 3
    assert page1[0].content == "Message 0"
    assert page1[2].content == "Message 2"

    page2 = chat_history_repository._get_paginated_messages(messages, page=2, size=3)
    assert len(page2) == 3
    assert page2[0].content == "Message 3"
    assert page2[2].content == "Message 5"

    page4 = chat_history_repository._get_paginated_messages(messages, page=4, size=3)
    assert len(page4) == 1
    assert page4[0].content == "Message 9"


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages(mocker, chat_history_repository):
    thread_id = "test-thread-id"

    mock_messages = [
        HumanMessage(content="Test question"),
        AIMessage(content="Test response"),
    ]

    mock_checkpoint = mocker.Mock(spec=CheckpointTuple)
    mock_checkpoint.checkpoint = {"channel_values": {"messages": mock_messages}}

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        chat_history_repository,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_messages,
    )

    result = await chat_history_repository._get_latest_checkpoint_messages(thread_id)

    assert result == mock_messages


@pytest.mark.anyio
async def test_get_latest_checkpoint_messages_no_checkpoint(
    chat_history_repository, mocker
):
    thread_id = "test-thread-id"

    async def mock_get_no_messages(_thread_id):
        return None

    mocker.patch.object(
        chat_history_repository,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await chat_history_repository._get_latest_checkpoint_messages(thread_id)

    assert result is None


@pytest.mark.anyio
async def test_get_historical_messages(chat_history_repository, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 2

    mock_messages = [
        HumanMessage(id="1", content="Hello"),
        AIMessage(id="2", content="Hi there!"),
        ToolMessage(
            id="3", content="Tool result", name="test_tool", tool_call_id="call_123"
        ),
    ]

    async def mock_get_messages(_thread_id):
        return mock_messages

    mocker.patch.object(
        chat_history_repository,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_messages,
    )

    result = await chat_history_repository.get_historical_messages(
        thread_id, page, size
    )

    assert result is not None
    assert result.pagination.thread_id == thread_id
    assert result.pagination.current_page == page
    assert result.pagination.page_size == size
    assert result.pagination.total_messages == 3
    assert result.pagination.total_pages == 2

    assert len(result.messages) == 2
    assert result.messages[0].role == "user"
    assert result.messages[0].content == "Hello"

    assert result.messages[1].role == "assistant"
    assert result.messages[1].content == "Hi there!"

    chat_history_repository._get_latest_checkpoint_messages.assert_called_once_with(
        thread_id
    )


@pytest.mark.anyio
async def test_get_historical_messages_no_messages(chat_history_repository, mocker):
    thread_id = "test-thread-id"
    page = 1
    size = 5

    async def mock_get_no_messages(_thread_id):
        return []

    mocker.patch.object(
        chat_history_repository,
        "_get_latest_checkpoint_messages",
        side_effect=mock_get_no_messages,
    )

    result = await chat_history_repository.get_historical_messages(
        thread_id, page, size
    )

    assert result.messages == []
    assert result.pagination.thread_id == thread_id
    assert result.pagination.page_size == size
    assert result.pagination.total_messages == 0
    assert result.pagination.total_pages == 0
    chat_history_repository._get_latest_checkpoint_messages.assert_called_once_with(
        thread_id
    )
