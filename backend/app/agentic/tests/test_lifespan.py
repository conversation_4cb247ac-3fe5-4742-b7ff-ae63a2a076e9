from unittest.mock import Async<PERSON>ock

import pytest
from fastapi import <PERSON><PERSON><PERSON>
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg_pool import Async<PERSON>onnectionPool

from app.agentic.lifespan import lifespan


@pytest.fixture
def mock_app():
    app = FastAPI()
    app.state.checkpoint_db_pool = None
    app.state.graph_manager = None
    return app


@pytest.fixture
def mock_pool_instance(mocker):
    instance = mocker.AsyncMock(spec=AsyncConnectionPool)
    instance.open = AsyncMock()
    instance.close = AsyncMock()
    return instance


@pytest.fixture
def mock_pool_class(mocker, mock_pool_instance):
    return mocker.patch(
        "app.agentic.lifespan.AsyncConnectionPool",
        return_value=mock_pool_instance,
    )


@pytest.fixture
def mock_checkpointer_instance(mocker):
    instance = mocker.AsyncMock(spec=AsyncPostgresSaver)
    instance.setup = AsyncMock()
    return instance


@pytest.fixture
def mock_checkpointer_class(mocker, mock_checkpointer_instance):
    return mocker.patch(
        "app.agentic.lifespan.AsyncPostgresSaver",
        return_value=mock_checkpointer_instance,
    )


@pytest.mark.anyio
async def test_lifespan_setup_and_teardown(
    mock_app,
    mock_pool_class,
    mock_pool_instance,
    mock_checkpointer_class,
    mock_checkpointer_instance,
):
    async with lifespan(mock_app):
        assert mock_pool_class is not None
        mock_pool_instance.open.assert_called_once()

        mock_checkpointer_class.assert_called_once_with(mock_pool_instance)
        mock_checkpointer_instance.setup.assert_called_once()

        assert mock_app.state.checkpoint_db_pool is mock_pool_instance
        assert mock_app.state.graph_checkpointer is mock_checkpointer_instance

    mock_pool_instance.close.assert_called_once()
    assert mock_app.state.checkpoint_db_pool is None
    assert mock_app.state.graph_checkpointer is None
