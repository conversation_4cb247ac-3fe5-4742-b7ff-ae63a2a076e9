from collections.abc import Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.agentic.models import (
    ActionCard,
    OrganizationMemberThread,
)
from app.agentic.schemas import ActionListItem
from app.common.orm.base_async_repository import BaseAsyncRepository


class OrganizationMemberThreadRepository(BaseAsyncRepository[OrganizationMemberThread]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, OrganizationMemberThread)

    async def get_by_org_member_and_crm_account(
        self, org_member_id: UUID, crm_account_id: str
    ) -> list[OrganizationMemberThread] | None:
        res = await self._get_by_attrs(
            organization_member_id=org_member_id, crm_account_id=crm_account_id
        )
        return [r for r in res] if res else None

    async def get_by_org_member_without_crm_account(
        self, org_member_id: UUID
    ) -> list[OrganizationMemberThread] | None:
        res = await self._get_by_attrs(
            organization_member_id=org_member_id, crm_account_id=""
        )
        res_none = await self._get_by_attrs(
            organization_member_id=org_member_id,
            crm_account_id=None,
        )

        all_results = list(res) + list(res_none)
        return [r for r in all_results] if all_results else None

    async def get_by_thread_id(self, thread_id: str) -> OrganizationMemberThread | None:
        res = await self._get_by_attrs(thread_id=thread_id)
        return res[0] if res else None

    async def delete_by_thread_id(self, thread_id: str) -> None:
        res = await self._get_by_attrs(thread_id=thread_id)
        if res:
            await self.delete(res[0].id)
            await self.db_session.commit()

    async def set_thread_crm_account_id(
        self, thread_id: str, crm_account_id: str
    ) -> None:
        res = await self._get_by_attrs(thread_id=thread_id)
        if res:
            res[0].crm_account_id = crm_account_id
            await self.db_session.commit()

    async def set_thread_name(self, thread_id: str, thread_name: str) -> None:
        res = await self._get_by_attrs(thread_id=thread_id)
        if res:
            res[0].thread_name = thread_name
            await self.db_session.commit()


class ActionCardRepository(BaseAsyncRepository[ActionCard]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, ActionCard)

    async def get_all_active_by_org_member(
        self, org_member_id: UUID
    ) -> list[ActionListItem]:
        actions = await self._get_by_attrs(
            is_active=True, organization_member_id=org_member_id
        )
        return self._convert_to_action_list_items(actions)

    async def get_all_active_by_crm_account(
        self, org_member_id: UUID, crm_account_id: str
    ) -> list[ActionListItem]:
        actions = await self._get_by_attrs(
            is_active=True,
            organization_member_id=org_member_id,
            crm_account_id=crm_account_id,
        )
        return self._convert_to_action_list_items(actions)

    def _convert_to_action_list_items(
        self, actions: Sequence[ActionCard]
    ) -> list[ActionListItem]:
        if not actions:
            return []

        sorted_actions = sorted(actions, key=lambda x: x.sort_order)
        return [
            ActionListItem(
                title=action.title,
                description=action.description,
                prompt=action.prompt_content,
                sort_order=action.sort_order,
            )
            for action in sorted_actions
        ]
