from collections.abc import AsyncIterator, Sequence
from typing import Any
from uuid import UUID

from langchain_core.messages import BaseMessage
from langchain_core.runnables import RunnableConfig
from langfuse import Langfuse
from langfuse.langchain import Callback<PERSON>andler
from langgraph.checkpoint.base import Base<PERSON>heckpointSaver
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Command, Interrupt

from app.agentic.graph.chat_history_repository import ChatHistoryRepository
from app.agentic.graph.graph import GraphFactory, GraphType
from app.agentic.graph.stream_encoder import DataStreamProtocolEncoder
from app.agentic.graph.stream_output_processor import StreamOutputProcessor
from app.agentic.schemas import (
    FrontendToolCallResult,
    ThreadHistoryResponse,
)
from app.workspace.integrations.user_integrations import UserIntegrations


class GraphManager:
    def __init__(
        self,
        graph: CompiledStateGraph,
        langfuse_callback_handler: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        chat_history_repository: ChatHistoryRepository,
    ):
        self.graph = graph
        self.langfuse_callback_handler = langfuse_callback_handler
        self.stream_output_processor = StreamOutputProcessor(
            DataStreamProtocolEncoder()
        )
        self.chat_history_repository = chat_history_repository

    def _get_config(self, thread_id: str, checkpoint_ns: str = "") -> RunnableConfig:
        callbacks = [self.langfuse_callback_handler]
        configurable = {
            "thread_id": thread_id,
            "checkpoint_ns": checkpoint_ns,
        }

        return RunnableConfig(configurable=configurable, callbacks=callbacks)

    async def invoke_graph(self, graph_input: dict[str, Any]) -> dict[str, Any]:
        config = self._get_config(graph_input["thread_id"])
        return await self.graph.ainvoke(input=graph_input, config=config)

    async def stream_graph(
        self,
        messages: Sequence[BaseMessage],
        crm_account_id: str,
        thread_id: str,
        org_id: UUID,
        user_id: UUID,
        resume: FrontendToolCallResult | None = None,
    ) -> AsyncIterator[str]:
        config = self._get_config(thread_id)

        graph_input: dict[str, Any] | Command
        if resume is not None:
            graph_input = Command(resume=resume)
        else:
            graph_input = {
                "messages": messages,
                "crm_account_id": crm_account_id,
                "thread_id": thread_id,
                "org_id": org_id,
                "user_id": user_id,
            }

        async for mode, output in self.graph.astream(
            input=graph_input,
            config=config,
            stream_mode=[
                "messages",  # stream LLM messages
                "updates",  # stream updates including interrupts
            ],
        ):
            chunk = None
            if mode == "messages":
                assert isinstance(output, tuple)
                assert isinstance(output[0], BaseMessage | Command)
                chunk = output[0]
            elif (
                mode == "updates"
                and isinstance(output, dict)
                and "__interrupt__" in output
            ):
                chunk = output["__interrupt__"][0]
                assert isinstance(chunk, Interrupt)

            if chunk:
                async for output in self.stream_output_processor.process(chunk):
                    yield output

    async def get_historical_messages(
        self, thread_id: str, page: int, size: int
    ) -> ThreadHistoryResponse:
        return await self.chat_history_repository.get_historical_messages(
            thread_id=thread_id, page=page, size=size
        )


async def create_graph_manager(
    graph_type: GraphType,
    user_id: UUID,
    user_integrations: UserIntegrations,
    langfuse_client: Langfuse,
    langfuse_callback_handler: CallbackHandler,
    graph_checkpointer: BaseCheckpointSaver,
) -> GraphManager:
    graph_factory = GraphFactory(
        user_id=user_id,
        user_integrations=user_integrations,
        langfuse_client=langfuse_client,
    )

    graph_definition = await graph_factory.create(graph_type=graph_type)
    compiled_graph = graph_definition.compile(checkpointer=graph_checkpointer)

    return GraphManager(
        graph=compiled_graph,
        langfuse_callback_handler=langfuse_callback_handler,
        chat_history_repository=ChatHistoryRepository(checkpointer=graph_checkpointer),
    )
