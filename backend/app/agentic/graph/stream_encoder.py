import json
from abc import ABC, abstractmethod
from collections.abc import Sequence
from enum import Enum
from typing import Any


class IStreamEncoder(ABC):
    @classmethod
    @abstractmethod
    def encode_ai_message(cls, content: str | list[str | dict]) -> str:
        pass

    @classmethod
    @abstractmethod
    def encode_tool_call(
        cls, tool_name: str, tool_call_id: str | None, args: dict[str, Any]
    ) -> str:
        pass

    @classmethod
    @abstractmethod
    def encode_tool_result(
        cls, tool_name: str | None, tool_call_id: str | None, result: dict | list | str
    ) -> str:
        pass

    @classmethod
    @abstractmethod
    def encode_interrupt(
        cls, value: dict | list | str, resumable: bool, ns: Sequence[str] | None
    ) -> str:
        pass

    @classmethod
    @abstractmethod
    def encode_command(cls, update: Any | None) -> str:
        pass


class DataStreamProtocolTypeID(str, Enum):
    TEXT_PART = "0"
    TOOL_CALL = "9"
    TOOL_RESULT = "a"
    DATA = "2"


class DataStreamProtocolEncoder(IStreamEncoder):
    """
    Encoder for the data stream protocol from Vercel.
    See https://ai-sdk.dev/docs/ai-sdk-ui/stream-protocol#data-stream-protocol
    """

    @staticmethod
    def _encode(type_id: str, content: str | dict | list) -> str:
        return f"{type_id}:{json.dumps(content)}\n"

    @classmethod
    def encode_ai_message(cls, content):
        return cls._encode(DataStreamProtocolTypeID.TEXT_PART.value, content)

    @classmethod
    def encode_tool_call(cls, tool_name, tool_call_id, args):
        return cls._encode(
            DataStreamProtocolTypeID.TOOL_CALL.value,
            {"toolName": tool_name, "toolCallId": tool_call_id, "args": args},
        )

    @classmethod
    def encode_tool_result(cls, tool_name, tool_call_id, result):
        return cls._encode(
            DataStreamProtocolTypeID.TOOL_RESULT.value,
            {
                "toolName": tool_name,
                "toolCallId": tool_call_id,
                "result": result,
            },
        )

    @classmethod
    def encode_interrupt(cls, value, resumable, ns):
        return cls._encode(
            DataStreamProtocolTypeID.DATA.value,
            [{"value": value, "resumable": resumable, "ns": ns}],
        )

    @classmethod
    def encode_command(cls, update) -> str:
        return cls._encode(DataStreamProtocolTypeID.TEXT_PART.value, update)
