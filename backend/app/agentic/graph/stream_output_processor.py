from collections.abc import AsyncIterator

from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langgraph.types import Command, Interrupt

from app.agentic.graph.stream_encoder import IStreamEncoder

OutputMessage = BaseMessage | Interrupt | Command


class StreamOutputProcessor:
    def __init__(self, encoder: IStreamEncoder):
        self.encoder = encoder

    async def process(self, output: OutputMessage) -> AsyncIterator[str]:
        match output:
            case AIMessageChunk():
                if output.content:
                    yield self.encoder.encode_ai_message(output.content)
                if hasattr(output, "tool_calls") and output.tool_calls:
                    for tool_call in output.tool_calls:
                        yield self.encoder.encode_tool_call(
                            tool_name=tool_call["name"],
                            tool_call_id=tool_call["id"],
                            args=tool_call["args"],
                        )
            case ToolMessage():
                yield self.encoder.encode_tool_result(
                    tool_name=output.name,
                    tool_call_id=output.tool_call_id,
                    result=output.content,
                )
            case Interrupt():
                yield self.encoder.encode_interrupt(
                    value=output.value, resumable=output.resumable, ns=output.ns
                )
            case Command():
                yield self.encoder.encode_command(output.update)
