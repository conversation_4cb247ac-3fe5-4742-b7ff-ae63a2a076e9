from typing import Any
from uuid import <PERSON>UI<PERSON>

from pydantic import BaseModel

from app.agentic.context.base import IToolBuilder
from app.agentic.context.schemas import (
    EmbedEmailMessage,
    ListEmailLabels,
    ListEmailThreads,
    ProcessAccountEmails,
    ReadEmailThread,
    SearchEmailsByContext,
    ToolDefinition,
)
from app.core.database import AsyncSessionLocal
from app.integrations.schemas import CRMAccountData, EmailMessage, EmailThread
from app.integrations.utils import convert_to_account, convert_to_contact
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
    create_user_integrations,
)


class EmailToolBuilder(IToolBuilder):
    EMAIL_METHODS: list[tuple[str, str, type[BaseModel], bool]] = [
        (
            "list_email_labels",
            "List email labels with optional filtering",
            ListEmailLabels,
            False,
        ),
        (
            "list_email_threads",
            "List email threads with filters and search options",
            ListEmailThreads,
            False,
        ),
        (
            "read_email_thread",
            "Read a specific email thread by ID",
            ReadEmailThread,
            False,
        ),
        (
            "search_emails_by_context",
            "Search emails using contextual filters like company, client, domain, or subject keywords",
            SearchEmailsByContext,
            False,
        ),
        (
            "embed_email_message",
            "Process and embed a specific email message for CRM account context",
            EmbedEmailMessage,
            False,
        ),
        (
            "process_account_emails",
            "Process all emails for a specific CRM account",
            ProcessAccountEmails,
            False,
        ),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.environment_id = user_integrations.environment.id

    async def build_tools(self) -> list[ToolDefinition]:
        delegator = _Delegator(self.user_id, self.environment_id)

        return [
            ToolDefinition(
                name=method_name,
                coroutine=getattr(delegator, method_name),
                description=description,
                args_schema=schema_class,
                requires_human_review=requires_human_review,
            )
            for method_name, description, schema_class, requires_human_review in self.EMAIL_METHODS
        ]


class _Delegator:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def list_email_labels(
        self, name_filter: str | None = None
    ) -> list[dict[str, Any]]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.list_filtered_labels(name_filter=name_filter)

    async def list_email_threads(
        self,
        query: str | None = None,
        company_filter: str | None = None,
        client_filter: str | None = None,
        unread_only: bool = False,
        max_results: int = 50,
    ) -> list[EmailThread]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.list_threads_with_filters(
                query=query,
                company_filter=company_filter,
                client_filter=client_filter,
                unread_only=unread_only,
                max_results=max_results,
            )

    async def read_email_thread(
        self, thread_id: str, max_messages: int | None = None
    ) -> EmailThread:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.get_thread_with_limit(
                thread_id=thread_id, max_messages=max_messages
            )

    async def search_emails_by_context(
        self,
        company_name: str | None = None,
        client_name: str | None = None,
        domain: str | None = None,
        subject_keywords: str | None = None,
        date_range_days: int | None = 30,
        max_results: int = 20,
    ) -> list[EmailMessage]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            return await email.search_emails_by_context(
                company_name=company_name if company_name else None,
                client_name=client_name if client_name else None,
                domain=domain if domain else None,
                subject_keywords=subject_keywords if subject_keywords else None,
                date_range_days=date_range_days if date_range_days else None,
                max_results=max_results,
            )

    async def embed_email_message(
        self,
        message_id: str,
        crm_account_id: str,
        force_reprocess: bool = False,
    ) -> dict[str, Any]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            # Get the email message first
            message = await email.get_message(message_id)

            # Get CRM integration
            crm = await user_integrations.crm()

            if not crm:
                raise RuntimeError(
                    f"No CRM integration available for user {self.user_id}"
                )

            # Fetch CRM account data
            crm_account_data = None
            if crm_account_id:
                # Get CRM data and convert to typed schemas
                raw_account_data = await crm.get_account(crm_account_id)
                raw_contacts_data = await crm.list_contacts_by_account(
                    crm_account_id, limit=50
                )

                account = convert_to_account(raw_account_data)
                contacts = [
                    convert_to_contact(contact) for contact in raw_contacts_data
                ]

                crm_account_data = CRMAccountData(
                    account_id=crm_account_id, account=account, contacts=contacts
                )

            # Create process runner with CRM account data
            process_runner = await email.create_process_runner(crm_account_data)

            success = await process_runner.process_single_email(
                email=message,
                crm_account_id=crm_account_id,
                force_reprocess=force_reprocess,
            )

            return {
                "success": success,
                "message_id": message_id,
                "crm_account_id": crm_account_id,
                "processed": success,
            }

    async def process_account_emails(
        self,
        crm_account_id: str,
        lookback_days: int = 365,
        max_emails: int = 1000,
    ) -> dict[str, Any]:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            email = await user_integrations.email()
            if not email:
                raise RuntimeError(f"No email integration for user {self.user_id}")

            crm = await user_integrations.crm()
            if not crm:
                raise RuntimeError(f"No CRM integration for user {self.user_id}")

            return await email.process_account_emails(
                crm_account_id=crm_account_id,
                crm_handle=crm,
                lookback_days=lookback_days,
                max_emails=max_emails,
            )
