import datetime
from uuid import U<PERSON><PERSON>

from app.agentic.context.base import IToolBuilder
from app.agentic.context.schemas import (
    GetUpcomingCalendarEvents,
    ToolDefinition,
)
from app.core.database import AsyncSessionLocal
from app.workspace.integrations.user_integrations import (
    UserIntegrations,
    create_user_integrations,
)


class CalendarToolBuilder(IToolBuilder):
    CALENDAR_METHODS: list[tuple[str, str, type, bool]] = [
        (
            "get_upcoming_week_calendar_events",
            "Get upcoming calendar events for the next week including today from Salesforce. This helps you understand what meetings and events are scheduled for a specific account. You must provide the crm_account_id parameter - use the account ID from the current conversation context.",
            GetUpcomingCalendarEvents,
            False,
        ),
    ]

    def __init__(self, user_id: UUID, user_integrations: UserIntegrations):
        self.user_id = user_id
        self.user_integrations = user_integrations
        self.environment_id = user_integrations.environment.id

    async def build_tools(self) -> list[ToolDefinition]:
        delegator = _Delegator(self.user_id, self.environment_id)

        return [
            ToolDefinition(
                name=method_name,
                coroutine=getattr(delegator, method_name),
                description=description,
                args_schema=schema_class,
                requires_human_review=requires_human_review,
            )
            for method_name, description, schema_class, requires_human_review in self.CALENDAR_METHODS
        ]


class _Delegator:
    def __init__(self, user_id: UUID, environment_id: UUID):
        self.user_id = user_id
        self.environment_id = environment_id

    async def get_upcoming_week_calendar_events(
        self, crm_account_id: str, max_results: int = 50
    ) -> dict:
        async with AsyncSessionLocal() as session:
            user_integrations = await create_user_integrations(
                user_id=self.user_id,
                environment_id=self.environment_id,
                db_session=session,
            )

            crm = await user_integrations.crm()
            if not crm:
                raise RuntimeError(f"No CRM integration for user {self.user_id}")

            now = datetime.datetime.now(datetime.UTC)
            end_time = now + datetime.timedelta(days=7)

            # Get events from Salesforce for the specified account
            try:
                # Fetch events related to the account with all fields
                events = await crm.list_events_by_account(
                    account_id=crm_account_id,
                    limit=max_results,
                    fields="ALL",
                )

                # Filter events to only include upcoming ones within the next week
                upcoming_events = []
                for event in events:
                    # Parse the event start time
                    start_datetime_str = event.get("StartDateTime")
                    if start_datetime_str:
                        try:
                            # Handle different datetime formats from Salesforce
                            if "T" in start_datetime_str:
                                # Handle ISO format with Z suffix
                                if start_datetime_str.endswith("Z"):
                                    start_datetime_str = (
                                        start_datetime_str[:-1] + "+00:00"
                                    )
                                start_datetime = datetime.datetime.fromisoformat(
                                    start_datetime_str
                                )
                            else:
                                # Handle date-only format
                                start_date = datetime.datetime.strptime(
                                    start_datetime_str, "%Y-%m-%d"
                                )
                                start_datetime = start_date.replace(tzinfo=datetime.UTC)

                            # Check if event is within our time range
                            if now <= start_datetime <= end_time:
                                upcoming_events.append(event)
                        except (ValueError, TypeError):
                            # Skip events with invalid datetime formats
                            continue

                # Sort events by start time
                upcoming_events.sort(key=lambda x: x.get("StartDateTime", ""))

                return {
                    "crm_account_id": crm_account_id,
                    "events": upcoming_events,
                    "total_events": len(upcoming_events),
                    "time_range": {
                        "start": now.isoformat(),
                        "end": end_time.isoformat(),
                    },
                    "data_source": "Salesforce",
                }

            except Exception as e:
                return {
                    "crm_account_id": crm_account_id,
                    "events": [],
                    "total_events": 0,
                    "error": f"Failed to fetch events from Salesforce: {str(e)}",
                    "time_range": {
                        "start": now.isoformat(),
                        "end": end_time.isoformat(),
                    },
                }
