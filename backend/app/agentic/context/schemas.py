from collections.abc import Awaitable, Callable
from typing import Any

from pydantic import BaseModel, ConfigDict, Field

from app.integrations.types import IntegrationSource


class ToolDefinition(BaseModel):
    name: str
    description: str
    coroutine: Callable[..., Awaitable[Any]]
    args_schema: type[BaseModel]
    requires_human_review: bool = False

    model_config = ConfigDict(arbitrary_types_allowed=True)


class OrgConfig(BaseModel):
    integrations: dict[IntegrationSource, dict]

    model_config = ConfigDict(arbitrary_types_allowed=True)


class GetOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to retrieve."
    )


class UpdateOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to update."
    )
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the opportunity."
    )


class ListOpportunitiesByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list opportunities for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of opportunities to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of opportunities to skip before starting to return results.",
        ge=0,
    )


class SearchOpportunities(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Name': 'Client', 'StageName': 'Prospecting'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of opportunities to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of opportunities to skip before starting to return results.",
        ge=0,
    )


class GetAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to retrieve.")


class UpdateAccount(BaseModel):
    account_id: str = Field(description="The Salesforce ID of the account to update.")
    fields: dict[str, Any] = Field(
        description="Dictionary of fields to update on the account."
    )


class SearchAccounts(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Name': 'Company', 'Industry': 'Technology'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of accounts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of accounts to skip before starting to return results.",
        ge=0,
    )


class GetContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to retrieve.")


class CreateContact(BaseModel):
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the contact with."
    )


class UpdateContact(BaseModel):
    contact_id: str = Field(description="The Salesforce ID of the contact to update.")
    contact_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the contact."
    )


class ListContactsByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list contacts for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class GetTask(BaseModel):
    task_id: str = Field(description="The Salesforce ID of the task to retrieve.")


class CreateTask(BaseModel):
    task_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the task with."
    )


class UpdateTask(BaseModel):
    task_id: str = Field(description="The Salesforce ID of the task to update.")
    task_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the task."
    )


class ListTasksByContact(BaseModel):
    contact_id: str = Field(
        description="The Salesforce ID of the contact to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class GetEvent(BaseModel):
    event_id: str = Field(description="The Salesforce ID of the event to retrieve.")


class CreateEvent(BaseModel):
    event_data: dict[str, Any] = Field(
        description="Dictionary of fields to create the event with."
    )


class UpdateEvent(BaseModel):
    event_id: str = Field(description="The Salesforce ID of the event to update.")
    event_data: dict[str, Any] = Field(
        description="Dictionary of fields to update on the event."
    )


class ListEventsByContact(BaseModel):
    contact_id: str = Field(
        description="The Salesforce ID of the contact to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class GetCurrentUserTerritory(BaseModel):
    """Get the current user's sales territory - no parameters needed"""

    pass


class ListEmailLabels(BaseModel):
    """List email labels with optional filtering"""

    name_filter: str = Field(
        default="", description="Optional filter to search labels by name"
    )


class ListEmailThreads(BaseModel):
    """List email threads with intelligent filtering"""

    query: str = Field(
        default="",
        description="Search query for threads (e.g., 'from:<EMAIL>', 'subject:meeting', 'acme corp')",
    )
    max_results: int = Field(
        default=50, description="Maximum number of threads to return", ge=1, le=100
    )
    include_spam_trash: bool = Field(
        default=False, description="Whether to include spam and trash threads"
    )
    company_filter: str = Field(
        default="", description="Filter threads by company name or domain"
    )
    client_filter: str = Field(
        default="", description="Filter threads by client name or email"
    )
    unread_only: bool = Field(default=False, description="Only show unread threads")


class ReadEmailThread(BaseModel):
    """Read the content of an email thread"""

    thread_id: str = Field(description="The ID of the email thread to read")
    max_messages: int = Field(
        default=10,
        description="Maximum number of messages to return from the thread",
        ge=1,
        le=50,
    )


class SearchEmailsByContext(BaseModel):
    """Search emails by company/client context"""

    company_name: str = Field(
        default="", description="Company name to search for in emails"
    )
    client_name: str = Field(
        default="", description="Client name to search for in emails"
    )
    domain: str = Field(default="", description="Email domain to search for")
    subject_keywords: str = Field(
        default="", description="Keywords to search for in subject line"
    )
    date_range_days: int = Field(
        default=30, description="Number of days back to search", ge=1, le=365
    )
    max_results: int = Field(
        default=20, description="Maximum number of results to return", ge=1, le=100
    )


class EmbedEmailMessage(BaseModel):
    message_id: str = Field(description="The email message ID to embed for AI context")
    crm_account_id: str = Field(
        description="The CRM account ID to associate this email with"
    )
    force_reprocess: bool = Field(
        default=False,
        description="Whether to reprocess the email if it's already embedded",
    )


class ProcessAccountEmails(BaseModel):
    crm_account_id: str = Field(description="The CRM account ID to process emails for")
    lookback_days: int = Field(
        default=365,
        description="Number of days to look back for emails",
        ge=1,
        le=3650,
    )
    max_emails: int = Field(
        default=1000,
        description="Maximum number of emails to process",
        ge=1,
        le=10000,
    )


class ListEventsByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class ListEventsByOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to list events for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of events to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of events to skip before starting to return results.",
        ge=0,
    )


class ListTasksByAccount(BaseModel):
    account_id: str = Field(
        description="The Salesforce ID of the account to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class ListTasksByOpportunity(BaseModel):
    opportunity_id: str = Field(
        description="The Salesforce ID of the opportunity to list tasks for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of tasks to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of tasks to skip before starting to return results.",
        ge=0,
    )


class SearchContacts(BaseModel):
    search_criteria: dict[str, Any] = Field(
        description="Dictionary of search criteria (e.g., {'Email': '<EMAIL>', 'FirstName': 'John'})."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of contacts to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of contacts to skip before starting to return results.",
        ge=0,
    )


class ListAccountAccess(BaseModel):
    crm_user_id: str = Field(
        description="The Salesforce ID of the user to list account access for."
    )
    limit: int = Field(
        default=100,
        description="Maximum number of account access records to return.",
        ge=1,
        le=1000,
    )
    offset: int = Field(
        default=0,
        description="Number of account access records to skip before starting to return results.",
        ge=0,
    )


class RetrieveDocuments(BaseModel):
    query: str = Field(description="The query to retrieve documents from the database.")


class GetUpcomingCalendarEvents(BaseModel):
    crm_account_id: str = Field(
        description="The Salesforce account ID to filter events for. This should be the account ID from the current conversation context.",
    )
    max_results: int = Field(
        default=50,
        description="Maximum number of events to return.",
        ge=1,
        le=250,
    )
