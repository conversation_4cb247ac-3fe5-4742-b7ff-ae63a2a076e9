from fastapi import <PERSON><PERSON><PERSON><PERSON>, Query, status
from starlette.responses import StreamingResponse

from app.agentic.dependencies import AgentServiceDep
from app.agentic.schemas import (
    AccountSummaryRequest,
    AccountSummaryResponse,
    ActionListItem,
    ChatRequest,
    ThreadCrmAccountUpdate,
    ThreadHistoryResponse,
    ThreadNameUpdate,
    ThreadsRead,
)

router = APIRouter()


@router.post("/chat_stream", name="chat_stream")
async def chat_stream(
    request: ChatRequest,
    agent_service: AgentServiceDep,
):
    chat_stream_iterator = await agent_service.process_message_stream(request)

    response = StreamingResponse(chat_stream_iterator, media_type="text/event-stream")
    response.headers["x-vercel-ai-data-stream"] = "v1"
    return response


@router.get(
    "/accounts/{account_id}/threads",
    name="account_threads",
    response_model=ThreadsRead | None,
)
async def get_accounts_threads(
    account_id: str,
    agent_service: AgentServiceDep,
):
    return await agent_service.get_threads_by_org_member_and_crm_account(account_id)


@router.get(
    "/threads",
    name="threads",
    response_model=ThreadsRead | None,
)
async def get_threads(
    agent_service: AgentServiceDep,
):
    return await agent_service.get_threads_by_org_member_without_crm_account()


@router.get(
    "/threads/{thread_id}/messages",
    name="thread_history",
    response_model=ThreadHistoryResponse,
)
async def get_thread_history(
    thread_id: str,
    agent_service: AgentServiceDep,
    page: int = Query(1, ge=1, description="Page number for message history"),
    size: int = Query(20, ge=1, le=100, description="Number of messages per page"),
):
    return await agent_service.get_thread_history(thread_id, page, size)


@router.delete(
    "/threads/{thread_id}",
    name="delete_thread",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def delete_thread(
    thread_id: str,
    agent_service: AgentServiceDep,
):
    await agent_service.delete_thread(thread_id)
    return None


@router.put(
    "/threads/{thread_id}/name",
    name="update_thread_name",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def update_thread_name(
    thread_id: str,
    agent_service: AgentServiceDep,
    request: ThreadNameUpdate,
):
    await agent_service.update_thread_name(thread_id, request.name)
    return None


@router.put(
    "/threads/{thread_id}/crm_account_id",
    name="update_thread_crm_account_id",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def update_thread_crm_account_id(
    thread_id: str,
    agent_service: AgentServiceDep,
    request: ThreadCrmAccountUpdate,
):
    await agent_service.update_thread_crm_account_id(thread_id, request.crm_account_id)
    return None


@router.get("/actions", name="actions", response_model=list[ActionListItem])
async def get_actions(
    agent_service: AgentServiceDep,
    crm_account_id: str | None = Query(None, description="CRM account ID"),
):
    return await agent_service.get_actions(crm_account_id=crm_account_id)


# Exposed via an endpoint for now but might change in the future when running within an async workflow on Bigquery data
@router.post(
    "/accounts/{account_id}/summary",
    name="create_account_summary",
    status_code=status.HTTP_202_ACCEPTED,
)
async def create_account_summary(
    agent_service: AgentServiceDep,
    request: AccountSummaryRequest,
):
    await agent_service.create_account_summary(request)
    return {
        "message": "Account summary creation initiated",
        "account_id": request.crm_account_id,
    }


@router.get(
    "/accounts/{account_id}/summary",
    name="get_account_summary",
    response_model=AccountSummaryResponse,
)
async def get_account_summary(
    account_id: str,
    agent_service: AgentServiceDep,
):
    return await agent_service.get_account_summary(account_id)
