from contextlib import asynccontextmanager

from fastapi import FastAPI
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from psycopg.rows import dict_row
from psycopg_pool import AsyncConnectionPool

from app.core.config import config


@asynccontextmanager
async def lifespan(app: FastAPI):
    try:
        pool = AsyncConnectionPool(
            conninfo=str(config.database.database_url),
            min_size=1,
            max_size=3,
            kwargs={"row_factory": dict_row, "autocommit": True},
            open=False,
        )

        await pool.open()
        graph_checkpointer = AsyncPostgresSaver(pool)  # type: ignore[arg-type]
        await graph_checkpointer.setup()

        app.state.checkpoint_db_pool = pool
        app.state.graph_checkpointer = graph_checkpointer

        yield

    finally:
        if pool:
            await pool.close()

        app.state.checkpoint_db_pool = None
        app.state.graph_checkpointer = None
