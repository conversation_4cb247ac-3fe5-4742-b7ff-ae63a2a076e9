from app.admin import BaseModelView
from app.agentic.models import (
    ActionCard,
    OrganizationMemberThread,
)


class _AgenticModelView(BaseModelView):
    category = "Agentic"


class OrganizationMemberThreadAdmin(_AgenticModelView, model=OrganizationMemberThread):
    autodiscover_order = 10
    name = "Organization Member Thread"
    name_plural = "Organization Member Threads"
    column_list = [
        OrganizationMemberThread.thread_id,
        OrganizationMemberThread.organization_member_id,
        OrganizationMemberThread.environment_id,
        OrganizationMemberThread.crm_account_id,
    ]


class ActionCardAdmin(_AgenticModelView, model=ActionCard):
    autodiscover_order = 10
    name = "Action Card"
    name_plural = "Action Cards"
    column_list = [
        ActionCard.title,
        ActionCard.sort_order,
        ActionCard.is_active,
    ]
