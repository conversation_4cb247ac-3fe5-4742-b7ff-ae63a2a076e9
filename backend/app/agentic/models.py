import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.core.database import BaseModel
from app.workspace.models import (
    Environment,
    OrganizationMember,
)


class OrganizationMemberThread(BaseModel):
    __tablename__ = "organization_member_threads"

    thread_id: Mapped[str] = mapped_column(
        Text, nullable=False, index=True, unique=True
    )
    thread_name: Mapped[str] = mapped_column(String(255), nullable=True)
    organization_member_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization_member.id"),
        nullable=False,
        index=True,
    )
    environment_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("environment.id"),
        nullable=False,
    )
    crm_account_id: Mapped[str | None] = mapped_column(String(50), nullable=True)

    organization_member: Mapped[OrganizationMember] = relationship("OrganizationMember")
    environment: Mapped[Environment] = relationship("Environment")

    def __repr__(self):
        return (
            f"<OrganizationMemberThread(id='{self.id}', thread_id='{self.thread_id}')>"
        )

    __table_args__ = (
        UniqueConstraint(
            "thread_id",
            "organization_member_id",
            "environment_id",
            name="uq_thread_organization_member_environment",
        ),
    )


class ActionCard(BaseModel):
    __tablename__ = "action_cards"

    title: Mapped[str] = mapped_column(String(200), nullable=False, index=True)
    description: Mapped[str] = mapped_column(Text, nullable=False)
    prompt_content: Mapped[str] = mapped_column(Text, nullable=False)
    sort_order: Mapped[int] = mapped_column(
        Integer, default=9999
    )  # Lower values display first
    is_active: Mapped[bool] = mapped_column(default=True)

    organization_member_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("organization_member.id"),
        nullable=False,
        index=True,
    )
    environment_id: Mapped[uuid.UUID | None] = mapped_column(
        UUID(as_uuid=True), ForeignKey("environment.id"), nullable=False, index=True
    )
    crm_account_id: Mapped[str | None] = mapped_column(String(50), nullable=True)

    # Relationships
    organization_member: Mapped[OrganizationMember] = relationship("OrganizationMember")
    environment: Mapped[Environment] = relationship("Environment")

    def __repr__(self):
        return f"<ActionCard(id='{self.id}', title='{self.title}', sort_order={self.sort_order})>"

    __table_args__ = (
        UniqueConstraint(
            "title",
            "description",
            "environment_id",
            "organization_member_id",
            "crm_account_id",
            name="uq_action_card_scope",
        ),
    )
