import uuid
from collections.abc import Sequence

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.workspace.models import OrganizationMember


class OrganizationMemberRepository(BaseAsyncRepository[OrganizationMember]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, OrganizationMember)

    async def get_by_organization_id(
        self, org_id: uuid.UUID
    ) -> Sequence[OrganizationMember]:
        return await self._get_by_attrs(organization_id=org_id)

    async def get_by_user_id(self, user_id: uuid.UUID) -> OrganizationMember | None:
        res = await self._get_by_attrs(user_id=user_id)
        return res[0] if res else None
