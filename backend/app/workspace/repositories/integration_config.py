from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.integrations.types import IntegrationSource
from app.workspace.models import (
    Environment,
    IntegrationConfig,
    IntegrationUser,
    Organization,
)
from app.workspace.types import EnvironmentType


class IntegrationConfigRepository(BaseAsyncRepository[IntegrationConfig]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, IntegrationConfig)

    async def get_all_by_org_id(
        self, org_id: UUID, env_type: EnvironmentType | None = None
    ) -> list[IntegrationConfig]:
        stmt = (
            select(IntegrationConfig)
            .join(IntegrationConfig.environment)
            .join(Environment.organization)
            .where(IntegrationConfig.is_active.is_(True), Organization.id == org_id)
        )

        if env_type is not None:
            stmt = stmt.where(Environment.type == env_type)

        result = await self.db_session.execute(stmt)

        return list(result.scalars().all())

    async def get_by_org_and_source(
        self,
        org_id: UUID,
        source: IntegrationSource,
        env_type: EnvironmentType | None = None,
    ) -> IntegrationConfig | None:
        configs = await self.get_all_by_org_id(org_id, env_type)
        for config in configs:
            if config.source == source:
                return config
        return None

    async def get_email_integration_user_for_tenant(
        self, tenant_id: UUID
    ) -> tuple[UUID | None, UUID | None]:
        stmt = (
            select(IntegrationUser.user_id, IntegrationConfig.environment_id)
            .join(
                IntegrationConfig,
                IntegrationUser.integration_config_id == IntegrationConfig.id,
            )
            .join(Environment, IntegrationConfig.environment_id == Environment.id)
            .where(
                Environment.id == tenant_id,
                IntegrationConfig.source == IntegrationSource.GMAIL,
                IntegrationConfig.is_active.is_(True),
            )
            .limit(1)
        )

        result = await self.db_session.execute(stmt)
        user_data = result.first()

        if user_data:
            return user_data[0], user_data[1]

        return None, None
