import uuid
from typing import cast

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.common.orm.base_async_repository import BaseAsyncRepository
from app.workspace.models.salesforce_field_mapping import SalesforceFieldMapping


class SalesforceFieldMappingRepository(BaseAsyncRepository[SalesforceFieldMapping]):
    def __init__(self, session: AsyncSession):
        super().__init__(session, SalesforceFieldMapping)

    async def get_by_organization_id(
        self, organization_id: uuid.UUID
    ) -> SalesforceFieldMapping | None:
        result = await self.db_session.execute(
            select(SalesforceFieldMapping).where(
                SalesforceFieldMapping.organization_id == organization_id
            )
        )
        return result.scalars().first()

    async def get_or_create_default(
        self, organization_id: uuid.UUID
    ) -> SalesforceFieldMapping:
        mapping = await self.get_by_organization_id(organization_id)
        if mapping:
            return mapping

        mapping = SalesforceFieldMapping(
            organization_id=organization_id,
            opportunity_amount_field="Amount",
            opportunity_stage_field="StageName",
            opportunity_owner_field="OwnerId",
            opportunity_probability_field="Probability",
            closed_won_stage_pattern="%Closed Won%",
            closed_lost_stage_pattern="%Closed Lost%",
            forecast_probability_multiplier=0.5,
            use_probability_field=False,
            quota_object="ForecastingQuota",
            quota_amount_field="QuotaAmount",
            quota_user_field="QuotaOwnerId",
        )

        self.db_session.add(mapping)
        await self.db_session.commit()
        await self.db_session.refresh(mapping)
        return cast("SalesforceFieldMapping", mapping)

    async def update_mapping(
        self, organization_id: uuid.UUID, **kwargs
    ) -> SalesforceFieldMapping:
        mapping = await self.get_or_create_default(organization_id)

        for key, value in kwargs.items():
            if hasattr(mapping, key):
                setattr(mapping, key, value)

        await self.db_session.commit()
        await self.db_session.refresh(mapping)
        return cast("SalesforceFieldMapping", mapping)
