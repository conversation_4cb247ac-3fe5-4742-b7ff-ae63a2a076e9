import datetime
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.workspace.services.calendar import CalendarService


@pytest.fixture
def mock_user_integrations():
    mock_integrations = Mock()
    mock_calendar = AsyncMock()
    mock_integrations.calendar = AsyncMock(return_value=mock_calendar)
    return mock_integrations, mock_calendar


@pytest.fixture
def calendar_service(mock_user_integrations):
    mock_integrations, _ = mock_user_integrations
    return CalendarService(mock_integrations)


class TestCalendarService:
    @pytest.mark.anyio
    async def test_list_calendars_success(
        self, calendar_service, mock_user_integrations
    ):
        _, mock_calendar = mock_user_integrations
        expected_calendars = [
            {"id": "primary", "name": "Primary Calendar"},
            {"id": "calendar2", "name": "Secondary Calendar"},
        ]
        mock_calendar.list_calendars.return_value = expected_calendars

        result = await calendar_service.list_calendars()

        assert result == expected_calendars
        mock_calendar.list_calendars.assert_called_once()

    @pytest.mark.anyio
    async def test_list_calendars_no_integration(self, mock_user_integrations):
        mock_integrations, _ = mock_user_integrations
        mock_integrations.calendar = AsyncMock(return_value=None)
        service = CalendarService(mock_integrations)

        result = await service.list_calendars()

        assert result == []

    @pytest.mark.anyio
    async def test_get_calendar_success(self, calendar_service, mock_user_integrations):
        _, mock_calendar = mock_user_integrations
        calendar_id = "primary"
        expected_calendar = {"id": calendar_id, "name": "Primary Calendar"}
        mock_calendar.get_calendar.return_value = expected_calendar

        result = await calendar_service.get_calendar(calendar_id)

        assert result == expected_calendar
        mock_calendar.get_calendar.assert_called_once_with(calendar_id)

    @pytest.mark.anyio
    async def test_get_calendar_no_integration(self, mock_user_integrations):
        mock_integrations, _ = mock_user_integrations
        mock_integrations.calendar = AsyncMock(return_value=None)
        service = CalendarService(mock_integrations)

        result = await service.get_calendar("primary")

        assert result == {}

    @pytest.mark.anyio
    async def test_list_events_success(self, calendar_service, mock_user_integrations):
        _, mock_calendar = mock_user_integrations
        calendar_id = "primary"
        start_date = datetime.datetime(2024, 1, 1, 0, 0, 0)
        end_date = datetime.datetime(2024, 1, 31, 23, 59, 59)
        expected_events = {
            "events": [
                {"id": "event1", "title": "Event 1"},
                {"id": "event2", "title": "Event 2"},
            ],
            "next_page_token": None,
        }
        mock_calendar.list_events.return_value = expected_events

        result = await calendar_service.list_events(
            calendar_id=calendar_id,
            start_date=start_date,
            end_date=end_date,
            max_results=100,
            single_events=True,
            order_by="startTime",
            show_deleted=False,
            page_token="token123",  # noqa: S106
        )

        assert result == expected_events
        mock_calendar.list_events.assert_called_once_with(
            calendar_id,
            start_date,
            end_date,
            100,
            True,
            "startTime",
            False,
            "token123",
        )

    @pytest.mark.anyio
    async def test_list_events_minimal_params(
        self, calendar_service, mock_user_integrations
    ):
        _, mock_calendar = mock_user_integrations
        calendar_id = "primary"
        expected_events = {"events": [], "next_page_token": None}
        mock_calendar.list_events.return_value = expected_events

        result = await calendar_service.list_events(calendar_id)

        assert result == expected_events
        mock_calendar.list_events.assert_called_once_with(
            calendar_id,
            None,
            None,
            250,
            True,
            "startTime",
            False,
            None,
        )

    @pytest.mark.anyio
    async def test_list_events_no_integration(self, mock_user_integrations):
        mock_integrations, _ = mock_user_integrations
        mock_integrations.calendar = AsyncMock(return_value=None)
        service = CalendarService(mock_integrations)

        result = await service.list_events("primary")

        assert result == {}

    @pytest.mark.anyio
    async def test_get_event_success(self, calendar_service, mock_user_integrations):
        _, mock_calendar = mock_user_integrations
        calendar_id = "primary"
        event_id = "event123"
        expected_event = {"id": event_id, "title": "Test Event"}
        mock_calendar.get_event.return_value = expected_event

        result = await calendar_service.get_event(calendar_id, event_id)

        assert result == expected_event
        mock_calendar.get_event.assert_called_once_with(calendar_id, event_id)

    @pytest.mark.anyio
    async def test_get_event_no_integration(self, mock_user_integrations):
        mock_integrations, _ = mock_user_integrations
        mock_integrations.calendar = AsyncMock(return_value=None)
        service = CalendarService(mock_integrations)

        result = await service.get_event("primary", "event123")

        assert result == {}

    @pytest.mark.anyio
    async def test_calendar_integration_called_for_each_method(
        self, mock_user_integrations
    ):
        mock_integrations, mock_calendar = mock_user_integrations
        service = CalendarService(mock_integrations)

        await service.list_calendars()
        await service.get_calendar("primary")
        await service.list_events("primary")
        await service.get_event("primary", "event123")

        assert mock_integrations.calendar.call_count == 4

    @pytest.mark.anyio
    async def test_list_events_with_custom_parameters(
        self, calendar_service, mock_user_integrations
    ):
        _, mock_calendar = mock_user_integrations
        calendar_id = "primary"
        start_date = datetime.datetime(2024, 1, 1, 10, 0, 0)
        end_date = datetime.datetime(2024, 1, 1, 18, 0, 0)

        expected_events = {"events": [{"id": "event1", "title": "Meeting"}]}
        mock_calendar.list_events.return_value = expected_events

        result = await calendar_service.list_events(
            calendar_id=calendar_id,
            start_date=start_date,
            end_date=end_date,
            max_results=50,
            single_events=False,
            order_by="updated",
            show_deleted=True,
            page_token="custom_token",  # noqa: S106
        )

        assert result == expected_events
        mock_calendar.list_events.assert_called_once_with(
            calendar_id,
            start_date,
            end_date,
            50,
            False,
            "updated",
            True,
            "custom_token",
        )
