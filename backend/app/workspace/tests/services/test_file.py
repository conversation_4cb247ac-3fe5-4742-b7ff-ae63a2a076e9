from datetime import datetime
from io import BytesIO

import pytest

from app.integrations.schemas import FileData
from app.workspace.exceptions import FileAlreadyExistsError, FileNameError
from app.workspace.integrations.user_integrations import UserIntegrations
from app.workspace.services.file import FileService


@pytest.fixture
def mock_user_integrations(mocker):
    return mocker.Mock(spec=UserIntegrations)


@pytest.fixture
def file_service(mock_user_integrations):
    return FileService(user_integrations=mock_user_integrations)


@pytest.fixture
def sample_file_data():
    return [
        FileData(
            id="file1",
            name="document.pdf",
            size=1024,
            time_created=datetime(2023, 1, 1, 12, 0, 0),
            last_modified=datetime(2023, 1, 2, 12, 0, 0),
            md5_hash="hash1",
            content_type="application/pdf",
        ),
        FileData(
            id="file2",
            name="presentation.pptx",
            size=2048,
            time_created=datetime(2023, 1, 2, 12, 0, 0),
            last_modified=datetime(2023, 1, 3, 12, 0, 0),
            md5_hash="hash2",
            content_type="application/vnd.openxmlformats-officedocument.presentationml.presentation",
        ),
        FileData(
            id="file3",
            name="spreadsheet.xlsx",
            size=512,
            time_created=datetime(2023, 1, 3, 12, 0, 0),
            last_modified=datetime(2023, 1, 1, 12, 0, 0),
            md5_hash="hash3",
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ),
    ]


@pytest.mark.anyio
async def test_get_team_documents_success(
    file_service: FileService,
    mock_user_integrations,
    sample_file_data: list[FileData],
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = sample_file_data
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    result = await file_service.get_team_documents()

    mock_file_handle.list_files.assert_called_once_with(
        container_name="pearl-org-test-org-123"
    )

    assert len(result) == 3

    assert result[0].id == "presentation.pptx"
    assert result[1].id == "document.pdf"
    assert result[2].id == "spreadsheet.xlsx"

    assert result[0].title == "presentation.pptx"
    assert result[0].format == "pptx"
    assert result[0].date_uploaded == datetime(2023, 1, 2, 12, 0, 0)


@pytest.mark.anyio
async def test_get_team_documents_no_file_integration(
    file_service: FileService,
    mock_user_integrations,
):
    mock_user_integrations.file.return_value = None

    result = await file_service.get_team_documents()

    assert result == []


@pytest.mark.anyio
async def test_delete_team_document_success(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.delete_file = mocker.AsyncMock()

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    await file_service.delete_team_document("document.pdf")

    mock_file_handle.delete_file.assert_called_once_with(
        container_name="pearl-org-test-org-123", file_name="document.pdf"
    )


@pytest.mark.anyio
async def test_delete_team_document_failure(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.delete_file.side_effect = Exception("File not found")

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    with pytest.raises(ValueError, match="Failed to delete document"):
        await file_service.delete_team_document("nonexistent.pdf")


@pytest.mark.anyio
async def test_upload_team_document_success(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.upload_file = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    file_obj = BytesIO(b"test content")
    result = await file_service.upload_team_document(file_obj, "test-file.pdf")

    mock_file_handle.upload_file.assert_called_once_with(
        "pearl-org-test-org-123", file_obj, "test-file.pdf", None
    )

    assert result.id == "test-file.pdf"
    assert result.title == "test-file.pdf"
    assert result.format == "pdf"


@pytest.mark.anyio
async def test_upload_team_document_duplicate_name(
    file_service: FileService,
    mock_user_integrations,
    sample_file_data: list[FileData],
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = sample_file_data
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    file_obj = BytesIO(b"test content")

    with pytest.raises(
        FileAlreadyExistsError,
        match="A file with the name 'document.pdf' already exists",
    ):
        await file_service.upload_team_document(file_obj, "document.pdf")


@pytest.mark.anyio
async def test_upload_team_document_no_name(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    file_obj = BytesIO(b"test content")

    with pytest.raises(
        FileNameError,
        match="File name is required",
    ):
        await file_service.upload_team_document(file_obj, None)


@pytest.mark.anyio
async def test_upload_team_document_name_with_spaces(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    file_obj = BytesIO(b"test content")

    with pytest.raises(
        FileNameError,
        match="File name is not valid. Remove any special characters or spaces.",
    ):
        await file_service.upload_team_document(file_obj, "test file.pdf")


@pytest.mark.anyio
async def test_upload_team_document_name_with_special_characters(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    file_obj = BytesIO(b"test content")

    with pytest.raises(
        FileNameError,
        match="File name is not valid. Remove any special characters or spaces.",
    ):
        await file_service.upload_team_document(file_obj, "<EMAIL>")


@pytest.mark.anyio
async def test_upload_team_document_valid_file_names(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    """Test that valid file name patterns are accepted."""
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True
    mock_file_handle.upload_file = mocker.AsyncMock()

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    valid_names = [
        "document.pdf",
        "my-file.txt",
        "data_backup.xlsx",
        "file123.docx",
        "report-2024.pdf",
        "user_data_2024-01-15.csv",
    ]

    for file_name in valid_names:
        file_obj = BytesIO(b"test content")
        result = await file_service.upload_team_document(file_obj, file_name)
        assert result.id == file_name
        assert result.title == file_name


@pytest.mark.anyio
async def test_upload_team_document_invalid_file_names(
    file_service: FileService,
    mock_user_integrations,
    mocker,
):
    """Test that invalid file name patterns are rejected."""
    mock_file_handle = mocker.AsyncMock()
    mock_file_handle.list_files.return_value = []
    mock_file_handle.bucket_exists.return_value = True

    mock_user_integrations.file.return_value = mock_file_handle
    mock_user_integrations.org_id = "test-org-123"

    invalid_names = [
        "test file.pdf",  # space
        "<EMAIL>",  # @ symbol
        ".hidden.txt",  # starts with dot
        "file..txt",  # consecutive dots
        "file with spaces.pdf",  # multiple spaces
        "file#hash.pdf",  # hash symbol
        "file(1).pdf",  # parentheses
        "file&data.pdf",  # ampersand
    ]

    for file_name in invalid_names:
        file_obj = BytesIO(b"test content")
        with pytest.raises(
            FileNameError,
            match="File name is not valid. Remove any special characters or spaces.",
        ):
            await file_service.upload_team_document(file_obj, file_name)
