import uuid

import pytest

from app.workspace.schemas import Account<PERSON><PERSON>, CRMAccountDetails, UserCRMInfo
from app.workspace.services.crm import CRMService


@pytest.mark.anyio
async def test_get_crm_with_valid_integration(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is not None
    assert isinstance(result, UserCRMInfo)
    assert result.crm_name == "salesforce"
    assert result.crm_user_id == "crm_user_id1"
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_crm_provider(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_crm_user_id(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.source = "salesforce"

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_crm_without_both_provider_and_user_id(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_crm()

    assert result is None
    mock_user_integrations.crm.assert_called_once()


@pytest.mark.anyio
async def test_get_accounts(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.list_account_access = mocker.AsyncMock(
        return_value=[
            {
                "Id": "1",
                "Name": "Account1",
            },
            {
                "Id": "2",
                "Name": "Account2",
            },
        ]
    )

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert len(result) == 2
    assert all(isinstance(account, AccountRead) for account in result)

    ids = [account.crm_id for account in result]
    assert "1" in ids
    assert "2" in ids

    names = [account.crm_name for account in result]
    assert "Account1" in names
    assert "Account2" in names

    mock_crm_provider.list_account_access.assert_called_once_with(
        crm_user_id="crm_user_id1"
    )


@pytest.mark.anyio
async def test_get_accounts_no_crm_provider(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert result == []


@pytest.mark.anyio
async def test_get_accounts_no_crm_user_id(mocker):
    mock_crm_provider = mocker.Mock()
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_accounts()

    assert result == []


@pytest.mark.anyio
async def test_sync_accounts(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_field_mapping_repo = mocker.Mock()
    user_id = uuid.uuid4()

    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    await service.sync_accounts()

    mock_user_integrations.sync_crm_accounts.assert_called_once()


@pytest.mark.anyio
async def test_get_account_details(mocker):
    mock_crm_provider = mocker.Mock()
    mock_crm_provider.get_account = mocker.AsyncMock(
        return_value={
            "Id": "account123",
            "Name": "Test Account",
            "NumberOfEmployees": 100,
            "AnnualRevenue": 1000000,
            "instance_url": "https://test.salesforce.com",
        }
    )
    mock_crm_provider.get_account_closed_won_revenue = mocker.AsyncMock(
        return_value=250000.0
    )

    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = mock_crm_provider
    mock_user_integrations.crm_user_id.return_value = "crm_user_id1"
    mock_user_integrations.org_id = uuid.uuid4()

    mock_field_mapping = mocker.Mock()
    mock_field_mapping.opportunity_amount_field = "Amount"
    mock_field_mapping.opportunity_stage_field = "StageName"
    mock_field_mapping.closed_won_stage_pattern = "%Closed Won%"

    mock_field_mapping_repo = mocker.Mock()
    mock_field_mapping_repo.get_or_create_default = mocker.AsyncMock(
        return_value=mock_field_mapping
    )

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_account_details("account123")

    assert isinstance(result, CRMAccountDetails)
    assert result.id == "account123"
    assert result.name == "Test Account"
    assert result.employees == 100
    assert result.total_revenue == 1000000.0
    assert result.existing_revenue == 250000.0
    assert (
        result.crm_url
        == "https://test.salesforce.com/lightning/r/Account/account123/view"
    )

    mock_crm_provider.get_account.assert_called_once_with(account_id="account123")
    mock_crm_provider.get_account_closed_won_revenue.assert_called_once_with(
        account_id="account123",
        field_mapping={
            "opportunity_amount_field": "Amount",
            "opportunity_stage_field": "StageName",
            "closed_won_stage_pattern": "%Closed Won%",
        },
    )
    mock_field_mapping_repo.get_or_create_default.assert_called_once_with(
        mock_user_integrations.org_id
    )


@pytest.mark.anyio
async def test_get_account_details_no_provider(mocker):
    mock_user_integrations = mocker.AsyncMock()
    mock_user_integrations.crm.return_value = None
    mock_user_integrations.crm_user_id.return_value = None

    mock_field_mapping_repo = mocker.Mock()

    user_id = uuid.uuid4()
    service = CRMService(
        user_id=user_id,
        user_integrations=mock_user_integrations,
        field_mapping_repo=mock_field_mapping_repo,
    )

    result = await service.get_account_details("account123")

    assert isinstance(result, CRMAccountDetails)
    assert result.id == ""
    assert result.name == ""
    assert result.employees == 0
    assert result.total_revenue == 0.0
    assert result.existing_revenue == 0.0
    assert result.crm_url == ""
