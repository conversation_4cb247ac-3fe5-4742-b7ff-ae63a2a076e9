import datetime
import uuid
from types import SimpleNamespace

import pytest

from app.workspace.services.organization import OrganizationService
from app.workspace.types import EnvironmentType


@pytest.mark.anyio
async def test_get_organizations_for_user_with_org(mocker):
    mock_org_repo = mocker.Mock()
    mock_env_repo = mocker.Mock()

    user_id = uuid.uuid4()
    now = datetime.datetime.now(datetime.UTC)

    org = SimpleNamespace(
        id=uuid.uuid4(),
        name="Test Org",
        domain="test.org",
        is_active=True,
        created_at=now,
        updated_at=now,
    )

    mock_org_repo.get_by_user_id = mocker.AsyncMock(return_value=org)

    service = OrganizationService(
        db_session=mocker.Mock(),
        org_repo=mock_org_repo,
        env_repo=mock_env_repo,
    )

    result = await service.get_user_organization(user_id)

    assert result.id == org.id
    assert result.name == "Test Org"

    mock_org_repo.get_by_user_id.assert_called_once_with(user_id)


@pytest.mark.anyio
async def test_get_organizations_for_user_without_org(mocker):
    mock_org_repo = mocker.Mock()
    mock_env_repo = mocker.Mock()

    user_id = uuid.uuid4()

    mock_org_repo.get_by_user_id = mocker.AsyncMock(return_value=None)

    service = OrganizationService(
        db_session=mocker.Mock(),
        org_repo=mock_org_repo,
        env_repo=mock_env_repo,
    )

    result = await service.get_user_organization(user_id)

    assert result is None

    mock_org_repo.get_by_user_id.assert_called_once_with(user_id)


@pytest.mark.anyio
async def test_get_env_with_environment(mocker):
    mock_org_repo = mocker.Mock()
    mock_env_repo = mocker.Mock()

    org_id = uuid.uuid4()
    now = datetime.datetime.now(datetime.UTC)
    env_type = EnvironmentType.SANDBOX

    env = SimpleNamespace(
        id=uuid.uuid4(),
        organization_id=org_id,
        type=env_type,
        created_at=now,
        updated_at=now,
    )

    mock_env_repo.get_by_org_id_and_type = mocker.AsyncMock(return_value=env)

    service = OrganizationService(
        db_session=mocker.Mock(),
        org_repo=mock_org_repo,
        env_repo=mock_env_repo,
    )

    result = await service.get_env(org_id, env_type)

    assert result.id == env.id
    assert result.organization_id == org_id
    assert result.type == EnvironmentType.SANDBOX

    mock_env_repo.get_by_org_id_and_type.assert_called_once_with(org_id, env_type)


@pytest.mark.anyio
async def test_get_env_without_environment(mocker):
    mock_org_repo = mocker.Mock()
    mock_env_repo = mocker.Mock()

    org_id = uuid.uuid4()
    env_type = EnvironmentType.PROD

    mock_env_repo.get_by_org_id_and_type = mocker.AsyncMock(return_value=None)

    service = OrganizationService(
        db_session=mocker.Mock(),
        org_repo=mock_org_repo,
        env_repo=mock_env_repo,
    )

    result = await service.get_env(org_id, env_type)

    assert result is None

    mock_env_repo.get_by_org_id_and_type.assert_called_once_with(org_id, env_type)
