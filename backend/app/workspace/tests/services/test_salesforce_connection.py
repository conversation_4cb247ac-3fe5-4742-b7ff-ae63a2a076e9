import uuid
from datetime import UTC, datetime, timedelta

import pytest

from app.common.oauth.flow_manager import OAuthFlowType
from app.integrations.types import IntegrationSource
from app.workspace.exceptions import (
    IntegrationConfigError,
    IntegrationCredentialsError,
    IntegrationTokenNotFoundError,
)
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.schemas import OrgEnvironment, SalesforceCredentials
from app.workspace.services.salesforce_connection import (
    SalesforceConnectionService,
    SalesforceTokenResponse,
)
from app.workspace.types import EnvironmentType


@pytest.fixture
def mock_environment():
    return OrgEnvironment(
        id=uuid.uuid4(),
        organization_id=uuid.uuid4(),
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )


@pytest.fixture
def service_mocks(mocker):
    db_session_mock = mocker.Mock()
    db_session_mock.commit = mocker.AsyncMock()
    db_session_mock.refresh = mocker.AsyncMock()

    return {
        "db_session": db_session_mock,
        "integration_user_repo": mocker.Mock(),
        "integration_cfg_repo": mocker.Mock(),
        "oauth_flow_manager": mocker.Mock(),
    }


@pytest.fixture
def salesforce_connection_service(service_mocks):
    service = SalesforceConnectionService(
        db_session=service_mocks["db_session"],
        integration_user_repo=service_mocks["integration_user_repo"],
        integration_cfg_repo=service_mocks["integration_cfg_repo"],
        auth_url="https://login.salesforce.com/services/oauth2/authorize",
        token_url="https://login.salesforce.com/services/oauth2/token",  # noqa: S106
        redirect_uri="https://app.example.com/oauth/callback",
        flow_type=OAuthFlowType.STANDARD,
    )
    service.oauth_flow_manager = service_mocks["oauth_flow_manager"]

    return service


@pytest.fixture
def test_data(mock_environment):
    user_id = uuid.uuid4()
    integration_config_id = uuid.uuid4()
    integration_user_id = uuid.uuid4()

    integration_config = IntegrationConfig()
    integration_config.id = integration_config_id
    integration_config.organization_id = mock_environment.organization_id
    integration_config.source = IntegrationSource.SALESFORCE
    integration_config.credentials = {
        "client_id": "fake_client_id",
        "client_secret": "fake_client_secret",
    }

    integration_user = IntegrationUser()
    integration_user.id = integration_user_id
    integration_user.user_id = user_id
    integration_user.integration_config_id = integration_config_id
    integration_user.external_user_id = "SF_USER_123"
    integration_user.external_org_id = "SF_ORG_456"
    integration_user.access_token = "fake_access_token"
    integration_user.refresh_token = "fake_refresh_token"
    integration_user.instance_url = "https://example.my.salesforce.com"
    integration_user.scope = "refresh_token full"
    integration_user.token_type = "Bearer"
    integration_user.expires_at = datetime.now(UTC) + timedelta(hours=1)
    integration_user.last_refreshed_at = datetime.now(UTC)

    return {
        "user_id": user_id,
        "environment": mock_environment,
        "integration_config_id": integration_config_id,
        "integration_user_id": integration_user_id,
        "integration_config": integration_config,
        "integration_user": integration_user,
    }


class TestSalesforceConnectionService:
    def test_integration_source_property(self, salesforce_connection_service):
        assert (
            salesforce_connection_service.integration_source
            == IntegrationSource.SALESFORCE
        )

    def test_default_scope_property(self, salesforce_connection_service):
        assert salesforce_connection_service.default_scope == "api refresh_token"

    def test_default_token_expiry_seconds_property(self, salesforce_connection_service):
        assert salesforce_connection_service.default_token_expiry_seconds == 7200

    def test_get_redirect_uri_for_token_exchange(self, salesforce_connection_service):
        assert (
            salesforce_connection_service._get_redirect_uri_for_token_exchange()
            == "https://app.example.com/oauth/callback"
        )

    @pytest.mark.anyio
    async def test_get_config_and_credentials_success(
        self, mocker, salesforce_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )

        (
            config,
            credentials,
        ) = await salesforce_connection_service._get_config_and_credentials(
            test_data["environment"]
        )

        assert config == test_data["integration_config"]
        assert isinstance(credentials, SalesforceCredentials)
        assert credentials.client_id == "fake_client_id"
        assert credentials.client_secret == "fake_client_secret"

        service_mocks[
            "integration_cfg_repo"
        ].get_by_org_and_source.assert_called_once_with(
            org_id=test_data["environment"].organization_id,
            source=IntegrationSource.SALESFORCE,
        )

    @pytest.mark.anyio
    async def test_get_config_and_credentials_no_config(
        self, mocker, salesforce_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=None
        )

        with pytest.raises(
            IntegrationConfigError, match="No Salesforce integration config found"
        ):
            await salesforce_connection_service._get_config_and_credentials(
                test_data["environment"]
            )

    def test_validate_credentials_success(self, salesforce_connection_service):
        credentials = SalesforceCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )

        salesforce_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_id(
        self, salesforce_connection_service
    ):
        credentials = SalesforceCredentials(
            client_id="",
            client_secret="test_client_secret",  # noqa: S106
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing Salesforce client_id and client_secret",
        ):
            salesforce_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_client_secret(
        self, salesforce_connection_service
    ):
        credentials = SalesforceCredentials(
            client_id="test_client_id", client_secret=""
        )

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing Salesforce client_id and client_secret",
        ):
            salesforce_connection_service._validate_credentials(credentials)

    def test_validate_credentials_missing_both(self, salesforce_connection_service):
        credentials = SalesforceCredentials(client_id="", client_secret="")

        with pytest.raises(
            IntegrationCredentialsError,
            match="Missing Salesforce client_id and client_secret",
        ):
            salesforce_connection_service._validate_credentials(credentials)

    @pytest.mark.anyio
    async def test_extract_user_info_from_token_success(
        self, salesforce_connection_service
    ):
        credentials = SalesforceCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        token_data = {
            "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
            "org_id": "SF_ORG_456",
        }

        result = await salesforce_connection_service._extract_user_info_from_token(
            token_data, credentials
        )

        assert result == {
            "external_user_id": "SF_USER_123",
            "external_org_id": "SF_ORG_456",
        }

    @pytest.mark.anyio
    async def test_extract_user_info_from_token_missing_data(
        self, salesforce_connection_service
    ):
        credentials = SalesforceCredentials(
            client_id="test_client_id",
            client_secret="test_client_secret",  # noqa: S106
        )
        token_data = {}

        with pytest.raises(IntegrationTokenNotFoundError):
            await salesforce_connection_service._extract_user_info_from_token(
                token_data, credentials
            )

    def test_create_token_response(self, salesforce_connection_service, test_data):
        expires_at = datetime.now(UTC) + timedelta(hours=2)

        result = salesforce_connection_service._create_token_response(
            test_data["integration_user"], expires_at
        )

        assert isinstance(result, SalesforceTokenResponse)
        assert result.external_user_id == "SF_USER_123"
        assert result.external_org_id == "SF_ORG_456"
        assert result.access_token == "fake_access_token"
        assert result.instance_url == "https://example.my.salesforce.com"
        assert result.expires_at == expires_at

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri(
        self, mocker, salesforce_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=test_data["integration_config"]
        )
        service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.return_value = "https://example.com/auth"

        result = await salesforce_connection_service.generate_oauth_authorization_uri(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
        )

        assert result == "https://example.com/auth"
        service_mocks[
            "integration_cfg_repo"
        ].get_by_org_and_source.assert_called_once_with(
            org_id=test_data["environment"].organization_id,
            source=IntegrationSource.SALESFORCE,
        )

        service_mocks[
            "oauth_flow_manager"
        ].generate_authorization_uri.assert_called_once()

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_missing_credentials(
        self, mocker, salesforce_connection_service, service_mocks, test_data
    ):
        config = test_data["integration_config"]
        config.credentials = {"client_id": "", "client_secret": ""}
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=config
        )

        with pytest.raises(IntegrationCredentialsError):
            await salesforce_connection_service.generate_oauth_authorization_uri(
                user_id=test_data["user_id"],
                environment=test_data["environment"],
            )

    @pytest.mark.anyio
    async def test_generate_oauth_authorization_uri_no_config(
        self, mocker, salesforce_connection_service, service_mocks, test_data
    ):
        service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
            return_value=None
        )

        with pytest.raises(IntegrationConfigError):
            await salesforce_connection_service.generate_oauth_authorization_uri(
                user_id=test_data["user_id"],
                environment=test_data["environment"],
            )


@pytest.mark.anyio
async def test_process_oauth_callback_new_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(return_value=None)

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "org_id": "SF_ORG_456",
        "access_token": "new_access_token",
        "refresh_token": "new_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    created_integration_user = IntegrationUser()
    created_integration_user.external_user_id = "SF_USER_123"
    created_integration_user.external_org_id = "SF_ORG_456"
    created_integration_user.access_token = "new_access_token"
    created_integration_user.refresh_token = "new_refresh_token"
    created_integration_user.instance_url = "https://example.my.salesforce.com"
    created_integration_user.scope = "refresh_token full"
    created_integration_user.token_type = "Bearer"
    created_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].create = mocker.AsyncMock(
        return_value=created_integration_user
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "new_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].create.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_update_existing_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )
    service_mocks[
        "integration_user_repo"
    ].get_by_user_and_integration = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    token_data = {
        "id": "https://login.salesforce.com/id/SF_ORG_456/SF_USER_123",
        "org_id": "SF_ORG_456",
        "access_token": "updated_access_token",
        "refresh_token": "updated_refresh_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
    }
    service_mocks["oauth_flow_manager"].exchange_code_for_token = mocker.AsyncMock(
        return_value=token_data
    )

    updated_integration_user = IntegrationUser()
    updated_integration_user.external_user_id = "SF_USER_123"
    updated_integration_user.external_org_id = "SF_ORG_456"
    updated_integration_user.access_token = "updated_access_token"
    updated_integration_user.refresh_token = "updated_refresh_token"
    updated_integration_user.instance_url = "https://example.my.salesforce.com"
    updated_integration_user.scope = "refresh_token full"
    updated_integration_user.token_type = "Bearer"
    updated_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=updated_integration_user
    )

    result = await salesforce_connection_service.process_oauth_callback(
        user_id=test_data["user_id"],
        environment=test_data["environment"],
        code="auth_code",
        state="state_value",
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "updated_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()


@pytest.mark.anyio
async def test_process_oauth_callback_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_process_oauth_callback_no_config(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationConfigError):
        await salesforce_connection_service.process_oauth_callback(
            user_id=test_data["user_id"],
            environment=test_data["environment"],
            code="auth_code",
            state="state_value",
        )


@pytest.mark.anyio
async def test_refresh_token(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=test_data["integration_config"]
    )

    token_data = {
        "access_token": "refreshed_access_token",
        "instance_url": "https://example.my.salesforce.com",
        "expires_in": 7200,
        "scope": "refresh_token full",
        "token_type": "Bearer",
    }
    service_mocks["oauth_flow_manager"].refresh_access_token = mocker.AsyncMock(
        return_value=token_data
    )

    refreshed_integration_user = IntegrationUser()
    refreshed_integration_user.external_user_id = "SF_USER_123"
    refreshed_integration_user.external_org_id = "SF_ORG_456"
    refreshed_integration_user.access_token = "refreshed_access_token"
    refreshed_integration_user.refresh_token = "fake_refresh_token"
    refreshed_integration_user.instance_url = "https://example.my.salesforce.com"
    refreshed_integration_user.scope = "refresh_token full"
    refreshed_integration_user.token_type = "Bearer"
    refreshed_integration_user.expires_at = datetime.now(UTC) + timedelta(hours=2)

    service_mocks["integration_user_repo"].update = mocker.AsyncMock(
        return_value=refreshed_integration_user
    )

    result = await salesforce_connection_service.refresh_access_token(
        integration_user_id=test_data["integration_user_id"],
        environment=test_data["environment"],
    )

    assert isinstance(result, SalesforceTokenResponse)
    assert result.external_user_id == "SF_USER_123"
    assert result.external_org_id == "SF_ORG_456"
    assert result.access_token == "refreshed_access_token"
    assert result.instance_url == "https://example.my.salesforce.com"

    service_mocks["integration_user_repo"].get_by_id.assert_called_once_with(
        test_data["integration_user_id"]
    )
    service_mocks["integration_user_repo"].update.assert_called_once()
    service_mocks["db_session"].commit.assert_called_once()
    service_mocks["db_session"].refresh.assert_called_once_with(
        refreshed_integration_user
    )


@pytest.mark.anyio
async def test_refresh_access_token_not_found(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=None
    )

    with pytest.raises(IntegrationTokenNotFoundError):
        await salesforce_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )


@pytest.mark.anyio
async def test_refresh_access_token_missing_credentials(
    mocker, salesforce_connection_service, service_mocks, test_data
):
    service_mocks["integration_user_repo"].get_by_id = mocker.AsyncMock(
        return_value=test_data["integration_user"]
    )

    config = test_data["integration_config"]
    config.credentials = {"client_id": "", "client_secret": ""}
    service_mocks["integration_cfg_repo"].get_by_org_and_source = mocker.AsyncMock(
        return_value=config
    )

    with pytest.raises(IntegrationCredentialsError):
        await salesforce_connection_service.refresh_access_token(
            integration_user_id=test_data["integration_user_id"],
            environment=test_data["environment"],
        )
