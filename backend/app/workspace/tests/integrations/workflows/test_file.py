import uuid
from unittest.mock import AsyncMock, MagicMock

import pytest

from app.integrations.types import IntegrationSource
from app.workspace.integrations.workflows.file import FileWorkflow


@pytest.fixture
def org_id():
    return uuid.uuid4()


@pytest.fixture
def mock_processing_result():
    return {"processed_files": 5, "deleted_documents": 2, "total_chunks": 150}


@pytest.fixture
def mock_db_session():
    return AsyncMock()


@pytest.fixture
def mock_file_handle():
    return AsyncMock()


@pytest.fixture
def mock_integration_factory(mock_file_handle):
    factory = MagicMock()
    factory.file.return_value = mock_file_handle
    return factory


@pytest.fixture
def workflow(org_id):
    return FileWorkflow(org_id=org_id, source=IntegrationSource.GCS)


@pytest.mark.anyio
async def test_start_processing_success(
    workflow, mock_processing_result, mock_integration_factory, mock_file_handle, mocker
):
    mocker.patch.object(workflow, "initialize", new_callable=AsyncMock)
    workflow.environment = MagicMock()
    workflow.integration_factory = mock_integration_factory
    mock_file_handle.start_processing.return_value = mock_processing_result

    bucket_names = ["bucket1", "bucket2"]

    result = await workflow.start_processing(bucket_names=bucket_names)

    assert result["status"] == "success"
    assert result["bucket_count"] == 2
    assert result["details"] == mock_processing_result

    mock_integration_factory.file.assert_called_once_with(source=IntegrationSource.GCS)
    mock_file_handle.start_processing.assert_called_once_with(bucket_names=bucket_names)


@pytest.mark.anyio
async def test_start_processing_initialization_failed(workflow, mocker):
    mocker.patch.object(workflow, "initialize", new_callable=AsyncMock)

    bucket_names = ["bucket1"]

    with pytest.raises(RuntimeError, match="Failed to initialize integration factory"):
        await workflow.start_processing(bucket_names=bucket_names)


@pytest.mark.anyio
async def test_close(workflow, mock_db_session):
    workflow.db_session = mock_db_session

    await workflow.close()

    mock_db_session.close.assert_called_once()


def test_is_source_supported():
    assert FileWorkflow._is_source_supported(IntegrationSource.GCS) is True
    assert FileWorkflow._is_source_supported(IntegrationSource.SLACK) is False
    assert FileWorkflow._is_source_supported(IntegrationSource.SALESFORCE) is False
