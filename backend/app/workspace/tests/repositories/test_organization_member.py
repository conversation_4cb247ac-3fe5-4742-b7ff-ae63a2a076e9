import uuid

import pytest

from app.auth.models import User
from app.workspace.models import Organization, OrganizationMember
from app.workspace.repositories.organization_member import OrganizationMemberRepository


async def setup_organization_with_members(async_db_session):
    owner_id = uuid.uuid4()
    member1_id = uuid.uuid4()
    member2_id = uuid.uuid4()
    nonmember_id = uuid.uuid4()
    org_id = uuid.uuid4()

    owner = User(
        id=owner_id,
        email="<EMAIL>",
        first_name="Owner",
        last_name="User",
    )
    member1 = User(
        id=member1_id,
        email="<EMAIL>",
        first_name="Member",
        last_name="One",
    )
    member2 = User(
        id=member2_id,
        email="<EMAIL>",
        first_name="Member",
        last_name="Two",
    )
    nonmember = User(
        id=nonmember_id,
        email="<EMAIL>",
        first_name="Non",
        last_name="Member",
    )
    async_db_session.add_all([owner, member1, member2, nonmember])
    await async_db_session.flush()

    org = Organization(
        id=org_id,
        name="Test Organization",
        domain="test-org.com",
        is_active=True,
        owner_id=owner_id,
    )
    async_db_session.add(org)
    await async_db_session.flush()

    org_member1 = OrganizationMember(
        user_id=member1_id,
        organization_id=org_id,
    )
    org_member2 = OrganizationMember(
        user_id=member2_id,
        organization_id=org_id,
    )
    async_db_session.add_all([org_member1, org_member2])
    await async_db_session.commit()

    return {
        "owner_id": owner_id,
        "member1_id": member1_id,
        "member2_id": member2_id,
        "nonmember_id": nonmember_id,
        "org_id": org_id,
    }


@pytest.mark.anyio
async def test_get_by_organization_id(async_db_session):
    setup = await setup_organization_with_members(async_db_session)
    repo = OrganizationMemberRepository(async_db_session)

    members = await repo.get_by_organization_id(setup["org_id"])

    assert len(members) == 2
    member_user_ids = [member.user_id for member in members]
    assert setup["member1_id"] in member_user_ids
    assert setup["member2_id"] in member_user_ids
    assert setup["owner_id"] not in member_user_ids


@pytest.mark.anyio
async def test_get_by_organization_id_no_members(async_db_session):
    owner_id = uuid.uuid4()
    empty_org_id = uuid.uuid4()

    owner = User(
        id=owner_id,
        email="<EMAIL>",
        first_name="Solo",
        last_name="Owner",
    )
    async_db_session.add(owner)
    await async_db_session.flush()

    empty_org = Organization(
        id=empty_org_id,
        name="Empty Organization",
        domain="empty-org.com",
        is_active=True,
        owner_id=owner_id,
    )
    async_db_session.add(empty_org)
    await async_db_session.commit()

    repo = OrganizationMemberRepository(async_db_session)

    members = await repo.get_by_organization_id(empty_org_id)

    assert len(members) == 0


@pytest.mark.anyio
async def test_get_by_user_id(async_db_session):
    setup = await setup_organization_with_members(async_db_session)
    repo = OrganizationMemberRepository(async_db_session)

    member = await repo.get_by_user_id(setup["member1_id"])
    assert member is not None
    assert member.user_id == setup["member1_id"]
    assert member.organization_id == setup["org_id"]

    non_member = await repo.get_by_user_id(setup["nonmember_id"])
    assert non_member is None

    owner_member = await repo.get_by_user_id(setup["owner_id"])
    assert owner_member is None
