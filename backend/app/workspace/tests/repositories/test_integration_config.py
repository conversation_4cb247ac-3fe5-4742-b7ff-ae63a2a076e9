import uuid
from datetime import UTC, datetime

import pytest

from app.auth.models import User
from app.integrations.types import IntegrationSource
from app.workspace.models import Environment, IntegrationConfig, Organization
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.types import EnvironmentType, IntegrationType


async def create_test_orgs(async_db_session):
    user_id = uuid.uuid4()
    user = User(
        id=user_id,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    async_db_session.add(user)

    org1_id = uuid.uuid4()
    org2_id = uuid.uuid4()

    org1 = Organization(
        id=org1_id,
        name="Test Organization 1",
        domain="test1.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    org2 = Organization(
        id=org2_id,
        name="Test Organization 2",
        domain="test2.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([org1, org2])
    await async_db_session.flush()

    dev_env1_id = uuid.uuid4()
    prod_env1_id = uuid.uuid4()
    dev_env2_id = uuid.uuid4()

    dev_env1 = Environment(
        id=dev_env1_id,
        organization_id=org1_id,
        type=EnvironmentType.SANDBOX,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    prod_env1 = Environment(
        id=prod_env1_id,
        organization_id=org1_id,
        type=EnvironmentType.PROD,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    dev_env2 = Environment(
        id=dev_env2_id,
        organization_id=org2_id,
        type=EnvironmentType.SANDBOX,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([dev_env1, prod_env1, dev_env2])
    await async_db_session.commit()

    return {
        "org1_id": org1_id,
        "org2_id": org2_id,
        "dev_env1_id": dev_env1_id,
        "prod_env1_id": prod_env1_id,
        "dev_env2_id": dev_env2_id,
    }


@pytest.mark.anyio
async def test_integration_config_get_all_by_org_id_multiple_orgs(async_db_session):
    test_orgs = await create_test_orgs(async_db_session)

    config1_id = uuid.uuid4()
    config2_id = uuid.uuid4()
    config3_id = uuid.uuid4()
    config4_id = uuid.uuid4()

    config1 = IntegrationConfig(
        id=config1_id,
        environment_id=test_orgs["dev_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config2 = IntegrationConfig(
        id=config2_id,
        environment_id=test_orgs["prod_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config3 = IntegrationConfig(
        id=config3_id,
        environment_id=test_orgs["dev_env2_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config4 = IntegrationConfig(
        id=config4_id,
        environment_id=test_orgs["dev_env1_id"],
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials={"slack_token": "inactive_key"},
        is_active=False,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([config1, config2, config3, config4])
    await async_db_session.commit()

    repo = IntegrationConfigRepository(async_db_session)

    configs_org1 = await repo.get_all_by_org_id(test_orgs["org1_id"])
    assert len(configs_org1) == 2
    config_ids = [config.id for config in configs_org1]
    assert config1_id in config_ids
    assert config2_id in config_ids
    assert config3_id not in config_ids
    assert config4_id not in config_ids

    configs_org2 = await repo.get_all_by_org_id(test_orgs["org2_id"])
    assert len(configs_org2) == 1
    assert configs_org2[0].id == config3_id


@pytest.mark.anyio
async def test_integration_config_get_all_by_org_id_with_different_sources(
    async_db_session,
):
    test_orgs = await create_test_orgs(async_db_session)

    config1_id = uuid.uuid4()
    config2_id = uuid.uuid4()

    config1 = IntegrationConfig(
        id=config1_id,
        environment_id=test_orgs["dev_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config2 = IntegrationConfig(
        id=config2_id,
        environment_id=test_orgs["prod_env1_id"],
        source=IntegrationSource.SLACK,
        integration_type=IntegrationType.MESSAGING,
        credentials={"slack_token": "slack_key"},
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([config1, config2])
    await async_db_session.commit()

    repo = IntegrationConfigRepository(async_db_session)

    configs = await repo.get_all_by_org_id(test_orgs["org1_id"])
    assert len(configs) == 2

    sources = {config.source for config in configs}
    assert IntegrationSource.SALESFORCE in sources
    assert IntegrationSource.SLACK in sources


@pytest.mark.anyio
async def test_integration_config_get_all_by_org_id_active_filter(async_db_session):
    test_orgs = await create_test_orgs(async_db_session)

    active_config_id = uuid.uuid4()
    inactive_config_id = uuid.uuid4()

    active_config = IntegrationConfig(
        id=active_config_id,
        environment_id=test_orgs["dev_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    inactive_config = IntegrationConfig(
        id=inactive_config_id,
        environment_id=test_orgs["dev_env2_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=False,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([active_config, inactive_config])
    await async_db_session.commit()

    repo = IntegrationConfigRepository(async_db_session)

    configs = await repo.get_all_by_org_id(test_orgs["org1_id"])

    assert len(configs) == 1
    assert configs[0].id == active_config_id
    assert configs[0].is_active is True


@pytest.mark.anyio
async def test_integration_config_get_all_by_org_id_empty(async_db_session):
    user_id = uuid.uuid4()
    user = User(
        id=user_id,
        email="<EMAIL>",
        first_name="Test",
        last_name="User",
    )
    async_db_session.add(user)

    empty_org_id = uuid.uuid4()
    empty_org = Organization(
        id=empty_org_id,
        name="Empty Organization",
        domain="empty.com",
        is_active=True,
        owner_id=user_id,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add(empty_org)
    await async_db_session.commit()

    repo = IntegrationConfigRepository(async_db_session)

    configs = await repo.get_all_by_org_id(empty_org_id)

    assert len(configs) == 0
    assert configs == []


@pytest.mark.anyio
async def test_integration_config_get_all_by_org_id_env_type_filter(async_db_session):
    test_orgs = await create_test_orgs(async_db_session)

    config_sandbox_id = uuid.uuid4()
    config_prod_id = uuid.uuid4()

    config_sandbox = IntegrationConfig(
        id=config_sandbox_id,
        environment_id=test_orgs["dev_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    config_prod = IntegrationConfig(
        id=config_prod_id,
        environment_id=test_orgs["prod_env1_id"],
        source=IntegrationSource.SALESFORCE,
        integration_type=IntegrationType.CRM,
        credentials={
            "username": "<EMAIL>",
            "client_id": "client_id",
            "client_secret": "client_secret",
        },
        is_active=True,
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    async_db_session.add_all([config_sandbox, config_prod])
    await async_db_session.commit()

    repo = IntegrationConfigRepository(async_db_session)

    sandbox_configs = await repo.get_all_by_org_id(
        test_orgs["org1_id"], env_type=EnvironmentType.SANDBOX
    )
    assert len(sandbox_configs) == 1
    assert sandbox_configs[0].id == config_sandbox_id

    prod_configs = await repo.get_all_by_org_id(
        test_orgs["org1_id"], env_type=EnvironmentType.PROD
    )
    assert len(prod_configs) == 1
    assert prod_configs[0].id == config_prod_id

    all_configs = await repo.get_all_by_org_id(test_orgs["org1_id"])
    assert len(all_configs) == 2
    config_ids = [config.id for config in all_configs]
    assert config_sandbox_id in config_ids
    assert config_prod_id in config_ids
