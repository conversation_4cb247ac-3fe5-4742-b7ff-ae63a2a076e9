from uuid import uuid4

import pytest

from app.main import app
from app.workspace.schemas import OrganizationMemberProfile


@pytest.mark.anyio
async def test_get_member_profile(async_client, override_organization_team_service):
    org_member_id = uuid4()
    user_id = uuid4()
    organization_id = uuid4()
    override_organization_team_service.get_team_member_profile.return_value = (
        OrganizationMemberProfile(
            id=org_member_id,
            user_id=user_id,
            organization_id=organization_id,
            first_name="<PERSON>",
            last_name="<PERSON><PERSON>",
            email="<EMAIL>",
            is_admin=True,
        )
    )

    response = await async_client.get(
        app.url_path_for("get_member_profile"),
    )

    assert response.status_code == 200
    assert response.json() == {
        "id": str(org_member_id),
        "user_id": str(user_id),
        "organization_id": str(organization_id),
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "email": "<EMAIL>",
        "is_admin": True,
    }
