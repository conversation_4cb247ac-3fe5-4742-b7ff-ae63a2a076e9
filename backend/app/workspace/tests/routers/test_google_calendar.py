from uuid import UUID

import pytest

from app.main import app
from app.workspace.dependencies import (
    get_google_connection_service,
    get_user_env,
)
from app.workspace.types import EnvironmentType


@pytest.fixture
def override_user_env(mocker):
    mock_env = mocker.Mock()
    mock_env.id = UUID("*************-2222-2222-************")
    mock_env.organization_id = UUID("********-3333-3333-3333-********3333")
    mock_env.type = EnvironmentType.PROD
    app.dependency_overrides[get_user_env] = lambda: mock_env
    yield mock_env
    app.dependency_overrides.pop(get_user_env)


@pytest.fixture
def override_google_calendar_service_auth_url(mocker):
    mock_service = mocker.Mock()
    mock_service.generate_oauth_authorization_uri = mocker.AsyncMock(
        return_value="https://accounts.google.com/oauth/authorize?test=true"
    )
    app.dependency_overrides[get_google_connection_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_google_connection_service)


@pytest.fixture
def override_google_calendar_service_callback(mocker):
    class MockToken:
        @property
        def external_user_id(self):
            return "google_user_123"

        @property
        def access_token(self):
            return "mock_access_token"

    mock_service = mocker.Mock()
    mock_service.process_oauth_callback = mocker.AsyncMock(return_value=MockToken())

    app.dependency_overrides[get_google_connection_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_google_connection_service)


@pytest.fixture
def override_google_calendar_service_remove_connection(mocker):
    mock_service = mocker.Mock()
    mock_service.remove_connection = mocker.AsyncMock(return_value=None)

    app.dependency_overrides[get_google_connection_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_google_connection_service)


@pytest.mark.anyio
async def test_get_google_calendar_auth_url(
    async_client, test_app, override_google_calendar_service_auth_url, override_user_env
):
    url = test_app.url_path_for("get_google_calendar_auth_url")
    response = await async_client.get(url)

    assert response.status_code == 200, response.text
    data = response.json()
    assert "auth_url" in data
    assert data["auth_url"] == "https://accounts.google.com/oauth/authorize?test=true"

    override_google_calendar_service_auth_url.generate_oauth_authorization_uri.assert_called_once()

    call_args = override_google_calendar_service_auth_url.generate_oauth_authorization_uri.call_args[
        1
    ]
    assert call_args["environment"] == override_user_env
    assert call_args["user_id"] == "mocked_user_id"


@pytest.mark.anyio
async def test_process_google_calendar_callback(
    async_client, test_app, override_google_calendar_service_callback, override_user_env
):
    url = test_app.url_path_for("process_google_calendar_callback")
    response = await async_client.get(f"{url}?code=test_code&state=test_state")

    assert response.status_code == 200, response.text
    data = response.json()
    assert "message" in data
    assert data["message"] == "OAuth succeed"
    assert data["calendar_user_id"] == "google_user_123"
    assert data["user_id"] == "mocked_user_id"

    assert override_google_calendar_service_callback.process_oauth_callback.called
    assert override_user_env.id == UUID("*************-2222-2222-************")


@pytest.mark.anyio
async def test_remove_google_calendar_connection(
    async_client,
    test_app,
    override_google_calendar_service_remove_connection,
    override_user_env,
):
    url = test_app.url_path_for("remove_google_calendar_connection")
    response = await async_client.delete(url)

    assert response.status_code == 200, response.text
    data = response.json()
    assert "message" in data
    assert data["message"] == "Google Calendar connection removed successfully"

    assert override_google_calendar_service_remove_connection.remove_connection.called
    assert override_user_env.id == UUID("*************-2222-2222-************")
