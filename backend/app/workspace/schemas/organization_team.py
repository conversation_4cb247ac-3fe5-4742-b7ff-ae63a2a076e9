from uuid import UUID

from pydantic import BaseModel, ConfigDict


class OrganizationMemberRead(BaseModel):
    id: UUID
    user_id: UUID
    organization_id: UUID
    is_admin: bool

    model_config = ConfigDict(from_attributes=True)


class OrganizationMemberProfile(OrganizationMemberRead):
    first_name: str
    last_name: str
    email: str


class OrganizationMemberProfileList(BaseModel):
    members: list[OrganizationMemberProfile]
