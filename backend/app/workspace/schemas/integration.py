from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict

from app.integrations.types import IntegrationSource
from app.workspace.types import IntegrationType


class IntegrationConfigRead(BaseModel):
    id: UUID
    source: IntegrationSource
    integration_type: IntegrationType
    settings: dict
    credentials: dict
    is_active: bool

    model_config = ConfigDict(from_attributes=True)


class IntegrationInfo(BaseModel):
    id: str
    source: IntegrationSource
    integration_type: IntegrationType
    name: str
    description: str
    is_active: bool


class IntegrationsListResponse(BaseModel):
    active_integrations: list[IntegrationInfo]
    available_integrations: list[IntegrationInfo]


class BaseCredentials(BaseModel):
    model_config = ConfigDict(extra="forbid")


class BaseOAuthCredentials(BaseCredentials):
    client_id: str
    client_secret: str


class BaseSettings(BaseModel):
    model_config = ConfigDict(extra="forbid")


class SalesforceCredentials(BaseOAuthCredentials):
    # compatibility with current config
    username: str | None = None
    password: str | None = None
    security_token: str | None = None


class SalesforceSettings(BaseSettings):
    pass


# todo: eventually make it subtype of BaseOAuthCredentials if possible
class SlackCredentials(BaseCredentials):
    slack_token: str


class SlackSettings(BaseSettings):
    pass


class GoogleOAuthCredentials(BaseOAuthCredentials):
    token_uri: str | None = None
    scopes: list[str] | None = None


class GoogleOAuthTokenResponse(BaseModel):
    external_user_id: str
    access_token: str
    refresh_token: str
    expires_at: datetime


class HubSpotCredentials(BaseOAuthCredentials):
    pass


class HubSpotSettings(BaseSettings):
    pass


class HubSpotTokenResponse(BaseModel):
    external_user_id: str
    external_org_id: str
    access_token: str
    expires_at: datetime


class GoogleCalendarSettings(BaseSettings):
    pass


class GmailSettings(BaseSettings):
    pass
