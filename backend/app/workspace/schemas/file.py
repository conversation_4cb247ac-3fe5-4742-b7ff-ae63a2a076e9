from datetime import datetime

from pydantic import BaseModel, field_serializer


class TeamDocument(BaseModel):
    id: str
    title: str
    format: str
    date_uploaded: datetime

    @field_serializer("date_uploaded")
    def serialize_date(self, value: datetime) -> str:
        return value.isoformat()


class UploadTeamDocumentResponse(BaseModel):
    id: str
    title: str
    format: str
    date_uploaded: str
