import uuid

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates

from app.common.orm.types import StringEnum
from app.core.database import BaseModel
from app.integrations.types import IntegrationSource
from app.workspace.models.environment import Environment
from app.workspace.types import IntegrationType
from app.workspace.validators.integration import validate_credentials, validate_settings


class IntegrationConfig(BaseModel):
    __tablename__ = "integration_config"

    environment_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        ForeignKey("environment.id"),
        nullable=False,
        index=True,
    )
    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource),
        nullable=False,
    )
    integration_type: Mapped[IntegrationType] = mapped_column(
        StringEnum(IntegrationType),
        nullable=False,
    )

    settings: Mapped[dict] = mapped_column(JSON, nullable=True)
    credentials: Mapped[dict] = mapped_column(JSON, nullable=True)

    is_active: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True)

    environment: Mapped[Environment] = relationship("Environment")

    def __repr__(self):
        return f"<IntegrationConfig(id='{self.id}', source='{self.source}')>"

    @validates("credentials")
    def validate_credentials(self, _key: str, value: dict) -> dict:
        if not value or not hasattr(self, "source") or self.source is None:
            return value

        return validate_credentials(self.source, value)

    @validates("settings")
    def validate_settings(self, _key: str, value: dict) -> dict:
        if not value or not hasattr(self, "source") or self.source is None:
            return value

        return validate_settings(self.source, value)

    __table_args__ = (
        UniqueConstraint("environment_id", "source", name="uix_environment_source"),
    )
