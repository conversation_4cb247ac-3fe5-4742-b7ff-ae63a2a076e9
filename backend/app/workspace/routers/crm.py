from fastapi import APIRouter

from app.workspace.dependencies import CRMServiceDep
from app.workspace.schemas import AccountRead, CRMAccountDetails
from app.workspace.schemas.crm import Metrics, UserCRMInfo

router = APIRouter()


@router.get(
    "/user_crm",
    response_model=UserCRMInfo | None,
    name="get_user_crm",
)
async def get_user_crm(service: CRMServiceDep):
    return await service.get_crm()


@router.get(
    "/accounts",
    response_model=list[AccountRead],
    name="get_accounts",
)
async def get_accounts(service: CRMServiceDep):
    return await service.get_accounts()


@router.get(
    "/sync_accounts",
    response_model=None,
    name="sync_accounts",
)
async def sync_accounts(service: CRMServiceDep):
    return await service.sync_accounts()


@router.get(
    "/metrics",
    response_model=Metrics,
    name="get_metrics",
)
async def get_metrics(service: CRMServiceDep):
    return await service.get_metrics()


@router.get(
    "/account/{account_id}/details",
    response_model=CRMAccountDetails,
    name="get_account_details",
)
async def get_account_details(service: CRMServiceDep, account_id: str):
    return await service.get_account_details(account_id)
