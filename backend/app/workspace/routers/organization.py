from fastapi import APIRouter

from app.auth.dependencies import AuthenticatedUserIdDep
from app.workspace.dependencies import OrganizationTeamServiceDep
from app.workspace.schemas import (
    OrganizationMemberProfile,
)

router = APIRouter()


@router.get(
    "/member_profiles/me",
    response_model=OrganizationMemberProfile,
    name="get_member_profile",
)
async def get_member_profile(
    user_id: AuthenticatedUserIdDep, service: OrganizationTeamServiceDep
):
    return await service.get_team_member_profile(user_id)
