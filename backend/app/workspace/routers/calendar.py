from datetime import datetime

from fastapi import APIRouter

from app.workspace.dependencies import CalendarServiceDep

router = APIRouter()


@router.get("/calendars", name="list_calendars")
async def list_calendars(service: CalendarServiceDep):
    return await service.list_calendars()


@router.get("/calendars/{calendar_id}", name="get_calendar")
async def get_calendar(calendar_id: str, service: CalendarServiceDep):
    return await service.get_calendar(calendar_id)


@router.get("/calendars/{calendar_id}/events", name="list_events")
async def list_events(
    calendar_id: str,
    service: CalendarServiceDep,
    start_date: datetime | None = None,
    end_date: datetime | None = None,
):
    return await service.list_events(calendar_id, start_date, end_date)


@router.get("/calendars/{calendar_id}/events/{event_id}", name="get_event")
async def get_event(calendar_id: str, event_id: str, service: CalendarServiceDep):
    return await service.get_event(calendar_id, event_id)
