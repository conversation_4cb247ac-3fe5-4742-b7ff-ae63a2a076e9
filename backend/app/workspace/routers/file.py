from fastapi import APIRouter

from app.workspace.dependencies import FileServiceDep
from app.workspace.schemas.file import TeamDocument

router = APIRouter()


@router.get("/team-documents", response_model=list[TeamDocument])
async def get_team_documents(
    file_service: FileServiceDep,
) -> list[TeamDocument]:
    """Get all team documents from file storage."""
    return await file_service.get_team_documents()
