from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.integrations.events.handler import integration_event_handler
from app.integrations.events.types import IntegrationEvent, IntegrationEventType
from app.integrations.types import IntegrationSource
from app.workspace.dependencies import (
    GoogleConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/google/gmail/auth-url")
async def get_gmail_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
        dynamic_integration_source=IntegrationSource.GMAIL,
    )

    return {"auth_url": oauth_uri}


@router.get("/google/gmail/callback")
async def process_gmail_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    gmail_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
        dynamic_integration_source=IntegrationSource.GMAIL,
    )

    # Emit email connected event
    try:
        event = IntegrationEvent(
            event_type=IntegrationEventType.EMAIL_CONNECTED,
            tenant_id=user_env.id,
            user_id=user_id,
            integration_type=IntegrationSource.GMAIL.value,
            metadata={
                "environment_id": str(user_env.id),
            },
        )
        await integration_event_handler.emit_event(event)
        logger.info(f"Emitted email connected event for user {user_id}")
    except Exception as e:
        logger.warning(f"Failed to emit email connected event: {e}")

    return {
        "message": "OAuth succeed",
        "gmail_user_id": gmail_token.external_user_id,
        "user_id": str(user_id),
    }


@router.delete("/google/gmail/connection")
async def remove_gmail_connection(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: GoogleConnectionServiceDep,
):
    await service.remove_connection(
        user_id=user_id,
        environment=user_env,
        dynamic_integration_source=IntegrationSource.GMAIL,
    )
