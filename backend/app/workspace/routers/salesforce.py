from fastapi import APIRouter, Query

from app.auth.dependencies import AuthenticatedUserIdDep
from app.common.helpers.logger import get_logger
from app.integrations.events.handler import integration_event_handler
from app.integrations.events.types import IntegrationEvent, IntegrationEventType
from app.integrations.types import IntegrationSource
from app.workspace.dependencies import (
    SalesforceConnectionServiceDep,
    UserEnvDep,
)

logger = get_logger()

router = APIRouter()


@router.get("/salesforce/auth-url")
async def get_salesforce_auth_url(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
):
    oauth_uri = await service.generate_oauth_authorization_uri(
        user_id=user_id,
        environment=user_env,
    )

    return {"auth_url": oauth_uri}


@router.get("/salesforce/callback")
async def process_salesforce_callback(
    user_id: AuthenticatedUserIdDep,
    user_env: UserEnvDep,
    service: SalesforceConnectionServiceDep,
    code: str = Query(...),
    state: str = Query(...),
):
    crm_token = await service.process_oauth_callback(
        user_id=user_id,
        environment=user_env,
        code=code,
        state=state,
    )

    # Emit CRM connected event
    try:
        event = IntegrationEvent(
            event_type=IntegrationEventType.CRM_CONNECTED,
            tenant_id=user_env.id,
            user_id=user_id,
            integration_type=IntegrationSource.SALESFORCE.value,
            metadata={
                "environment_id": str(user_env.id),
            },
        )
        await integration_event_handler.emit_event(event)
        logger.info(f"Emitted CRM connected event for user {user_id}")
    except Exception as e:
        logger.warning(f"Failed to emit CRM connected event: {e}")

    return {
        "message": "OAuth succeed",
        "crm_user_id": crm_token.external_user_id,
        "user_id": str(user_id),
    }
