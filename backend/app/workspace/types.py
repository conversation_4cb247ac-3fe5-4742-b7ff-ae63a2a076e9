from enum import Enum

from app.integrations.types import IntegrationSource


class EnvironmentType(str, Enum):
    SANDBOX = "sandbox"
    PROD = "prod"


class IntegrationType(str, Enum):
    CRM = "crm"
    MESSAGING = "messaging"
    FILE = "file"
    CALENDAR = "calendar"
    EMAIL = "email"


CRM_SOURCES = {
    IntegrationSource.SALESFORCE,
    IntegrationSource.HUBSPOT,
}

MESSAGING_SOURCES = {
    IntegrationSource.SLACK,
    IntegrationSource.TEAMS,
}

FILE_SOURCES = {
    IntegrationSource.GCS,
}

CALENDAR_SOURCES = {
    IntegrationSource.GOOGLE_CALENDAR,
    IntegrationSource.OUTLOOK_CALENDAR,
}

EMAIL_SOURCES = {
    IntegrationSource.GMAIL,
}

SOURCE_TYPE_MAP = {
    IntegrationSource.SALESFORCE: IntegrationType.CRM,
    IntegrationSource.HUBSPOT: IntegrationType.CRM,
    IntegrationSource.SLACK: IntegrationType.MESSAGING,
    IntegrationSource.TEAMS: IntegrationType.MESSAGING,
    IntegrationSource.GCS: IntegrationType.FILE,
    IntegrationSource.GOOGLE_CALENDAR: IntegrationType.CALENDAR,
    IntegrationSource.OUTLOOK_CALENDAR: IntegrationType.CALENDAR,
    IntegrationSource.GMAIL: IntegrationType.EMAIL,
}
