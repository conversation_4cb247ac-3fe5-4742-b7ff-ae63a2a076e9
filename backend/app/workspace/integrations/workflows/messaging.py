import uuid
from typing import Any

from app.common.helpers.logger import get_logger
from app.core.database import AsyncSessionLocal
from app.integrations.factory import IntegrationFactory, create_factory
from app.integrations.types import IntegrationSource
from app.workspace.integrations.credentials_resolver import (
    OrganizationCredentialsResolver,
)
from app.workspace.repositories.environment import EnvironmentRepository
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.repositories.organization import OrganizationRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.services.integration_config import IntegrationConfigService
from app.workspace.services.organization import OrganizationService
from app.workspace.types import EnvironmentType

logger = get_logger()


class MessagingWorkflow:
    def __init__(
        self,
        org_id: uuid.UUID,
        source: IntegrationSource,
        env_type: EnvironmentType = EnvironmentType.PROD,
    ):
        self.org_id = org_id
        self.source = source
        self.env_type = env_type

        if not self._is_source_supported(source):
            raise ValueError(f"Unsupported integration source: {source}")

        self.db_session = AsyncSessionLocal()

        org_repo = OrganizationRepository(self.db_session)
        env_repo = EnvironmentRepository(self.db_session)
        integration_user_repo = IntegrationUserRepository(self.db_session)
        integration_cfg_repo = IntegrationConfigRepository(self.db_session)

        self.org_service = OrganizationService(
            db_session=self.db_session,
            org_repo=org_repo,
            env_repo=env_repo,
        )
        self.integration_cfg_service = IntegrationConfigService(
            integration_cfg_repo=integration_cfg_repo,
            integration_user_repo=integration_user_repo,
        )

        self.environment: OrgEnvironment | None = None
        self.integration_factory: IntegrationFactory | None = None
        self.credentials_resolver = None

    async def initialize(self) -> None:
        if self.environment is None:
            self.environment = await self._get_environment()

            if self.environment is None:
                raise RuntimeError("Failed to initialize environment")

            credentials_resolver = OrganizationCredentialsResolver(
                environment=self.environment,
                integration_config_service=self.integration_cfg_service,
            )

            self.integration_factory = create_factory(
                tenant_id=self.environment.id,
                db_session_factory=AsyncSessionLocal,
                credentials_resolver=credentials_resolver,
            )

    async def _get_environment(self) -> OrgEnvironment:
        environment = await self.org_service.get_env(
            org_id=self.org_id, env_type=self.env_type
        )
        if not environment:
            raise RuntimeError(
                f"No environment found for org {self.org_id} and type {self.env_type}"
            )
        return environment

    async def start_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        if self.environment is None or self.integration_factory is None:
            await self.initialize()

        if self.integration_factory is None:
            raise RuntimeError("Failed to initialize integration factory")

        result = {
            "status": "success",
            "channel_count": len(channel_ids),
            "details": None,
        }

        try:
            logger.info(f"Starting {self.source.value} ingestion workflow...")

            messaging = self.integration_factory.messaging(source=self.source)

            ingestion_result = await messaging.start_channel_ingestion(
                channel_ids=channel_ids,
                interval_seconds=interval_seconds,
                lookback_days=lookback_days,
                batch_size=batch_size,
                daemon_mode=daemon_mode,
            )

            if daemon_mode:
                logger.info(f"{self.source.value} channel ingestion daemon has started")
                result["daemon_mode"] = True
            else:
                logger.info(
                    f"{self.source.value} channel ingestion completed successfully!"
                )
                result["details"] = ingestion_result

            return result

        except Exception as e:
            logger.exception(f"Error starting {self.source.value} ingestion: {e}")
            result["status"] = "error"
            result["error"] = str(e)
            return result

    async def start_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        if self.environment is None or self.integration_factory is None:
            await self.initialize()

        if self.integration_factory is None:
            raise RuntimeError("Failed to initialize integration factory")

        result = {
            "status": "success",
            "channel_count": len(channel_ids),
            "details": None,
        }

        try:
            logger.info(f"Starting {self.source.value} processing workflow...")

            messaging = self.integration_factory.messaging(source=self.source)

            processing_result = await messaging.start_channel_processing(
                channel_ids=channel_ids,
                interval_seconds=interval_seconds,
                batch_size=batch_size,
                daemon_mode=daemon_mode,
            )

            if daemon_mode:
                logger.info(
                    f"{self.source.value} channel processing daemon has started"
                )
                result["daemon_mode"] = True
            else:
                logger.info(
                    f"{self.source.value} channel processing completed successfully!"
                )
                result["details"] = processing_result

            return result

        except Exception as e:
            logger.exception(f"Error starting {self.source.value} processing: {e}")
            result["status"] = "error"
            result["error"] = str(e)
            return result

    async def close(self):
        if self.db_session:
            await self.db_session.close()

    async def __aenter__(self):
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    @staticmethod
    def _is_source_supported(source: IntegrationSource) -> bool:
        return source == IntegrationSource.SLACK
