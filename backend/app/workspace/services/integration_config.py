from uuid import UUID

from app.common.helpers.logger import get_logger
from app.integrations.types import IntegrationSource
from app.workspace.models import IntegrationConfig, IntegrationUser
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.repositories.integration_user import IntegrationUserRepository
from app.workspace.schemas import OrgEnvironment
from app.workspace.types import (
    CALENDAR_SOURCES,
    CRM_SOURCES,
    EMAIL_SOURCES,
    FILE_SOURCES,
    IntegrationType,
)

logger = get_logger()


class IntegrationConfigService:
    def __init__(
        self,
        integration_cfg_repo: IntegrationConfigRepository,
        integration_user_repo: IntegrationUserRepository,
    ):
        self.integration_cfg_repo = integration_cfg_repo
        self.integration_user_repo = integration_user_repo

    async def get_integration_configs(
        self, environment: OrgEnvironment
    ) -> list[IntegrationConfig]:
        return await self.integration_cfg_repo.get_all_by_org_id(
            org_id=environment.organization_id, env_type=environment.type
        )

    async def get_integration_config(
        self,
        environment: OrgEnvironment,
        source: IntegrationSource,
    ) -> IntegrationConfig | None:
        configs = await self.get_integration_configs(environment)
        config = next((config for config in configs if config.source is source), None)

        if not config:
            logger.warning(
                f"No integration config found for org {environment.organization_id}, source {source}"
            )

        return config

    async def get_crm_config(
        self, environment: OrgEnvironment
    ) -> IntegrationConfig | None:
        configs = await self.get_integration_configs(environment)
        return next(
            (config for config in configs if config.source in CRM_SOURCES),
            None,
        )

    async def get_file_config(
        self, environment: OrgEnvironment
    ) -> IntegrationConfig | None:
        configs = await self.get_integration_configs(environment)
        return next(
            (config for config in configs if config.source in FILE_SOURCES),
            None,
        )

    async def get_calendar_config(
        self, environment: OrgEnvironment
    ) -> IntegrationConfig | None:
        configs = await self.get_integration_configs(environment)
        return next(
            (config for config in configs if config.source in CALENDAR_SOURCES),
            None,
        )

    async def get_email_config(
        self, environment: OrgEnvironment
    ) -> IntegrationConfig | None:
        configs = await self.get_integration_configs(environment)
        return next(
            (config for config in configs if config.source in EMAIL_SOURCES),
            None,
        )

    async def get_integration_user(
        self, integration_config_id: UUID, user_id: UUID
    ) -> IntegrationUser | None:
        integration_user = await self.integration_user_repo.get_by_user_and_integration(
            integration_config_id=integration_config_id, user_id=user_id
        )

        if not integration_user:
            logger.debug(
                f"No integration user found for user {user_id}, integration {integration_config_id}"
            )

        return integration_user

    async def get_user_activated_integration_config(
        self,
        environment: OrgEnvironment,
        integration_type: IntegrationType,
        user_id: UUID,
    ) -> IntegrationConfig | None:
        user_integration_users = await self.integration_user_repo.get_by_user_id(
            user_id
        )

        if not user_integration_users:
            logger.debug(f"No integration users found for user {user_id}")
            return None

        available_configs = await self.get_integration_configs(environment)

        for integration_user in user_integration_users:
            for config in available_configs:
                if (
                    config.id == integration_user.integration_config_id
                    and config.integration_type == integration_type
                ):
                    logger.debug(
                        f"Found activated {integration_type.value} integration for user {user_id}: {config.source.value}"
                    )
                    return config

        logger.debug(
            f"No activated {integration_type.value} integration found for user {user_id}"
        )
        return None
