import datetime
from typing import Any

from app.workspace.integrations.user_integrations import UserIntegrations


class CalendarService:
    def __init__(self, user_integrations: UserIntegrations):
        self._user_integrations = user_integrations

    async def list_calendars(self) -> list[dict[str, Any]]:
        calendar = await self._user_integrations.calendar()
        if not calendar:
            return []

        return await calendar.list_calendars()

    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        calendar = await self._user_integrations.calendar()
        if not calendar:
            return {}

        return await calendar.get_calendar(calendar_id)

    async def list_events(
        self,
        calendar_id: str,
        start_date: datetime.datetime | None = None,
        end_date: datetime.datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        calendar = await self._user_integrations.calendar()
        if not calendar:
            return {}

        return await calendar.list_events(
            calendar_id,
            start_date,
            end_date,
            max_results,
            single_events,
            order_by,
            show_deleted,
            page_token,
        )

    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        calendar = await self._user_integrations.calendar()
        if not calendar:
            return {}

        return await calendar.get_event(calendar_id, event_id)
