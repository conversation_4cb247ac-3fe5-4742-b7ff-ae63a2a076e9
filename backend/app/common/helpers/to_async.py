import asyncio
from collections.abc import Callable
from functools import wraps
from typing import Any


def to_async(func: Callable[..., Any]) -> Callable:
    """
    Transforms a synchronous function into an asynchronous one by executing it in a separate thread.

    This decorator allows blocking synchronous operations to be awaited without blocking
    the main event loop, enabling better concurrency in async applications.
    """

    @wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        return await asyncio.to_thread(func, *args, **kwargs)

    return wrapper
