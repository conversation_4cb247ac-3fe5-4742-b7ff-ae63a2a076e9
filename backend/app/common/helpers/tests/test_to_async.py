import asyncio
import time

from app.common.helpers.to_async import to_async


def test_decorator_makes_function_async():
    """Test that decorator converts sync function to async."""

    @to_async
    def sync_function(x: int) -> int:
        return x * 2

    assert asyncio.iscoroutinefunction(sync_function)


def test_to_async_basic_functionality():
    """Test that to_async decorator correctly runs a function in a separate thread."""

    @to_async
    def add(a: int, b: int) -> int:
        return a + b

    result = asyncio.run(add(1, 2))
    assert result == 3


def test_to_async_with_multiple_calls():
    """Test that to_async decorator works correctly with multiple concurrent calls."""

    @to_async
    def process_item(item: int) -> int:
        time.sleep(0.1)
        return item * 2

    async def run_multiple():
        tasks = [process_item(i) for i in range(5)]
        return await asyncio.gather(*tasks)

    start_time = time.time()
    results = asyncio.run(run_multiple())
    end_time = time.time()

    assert results == [0, 2, 4, 6, 8]
    # All tasks should run concurrently, so total time should be close to 0.1s
    assert end_time - start_time < 0.2
