from app.common.email.email_client import IEmailClient
from app.common.helpers.logger import get_logger

logger = get_logger(__name__)


class LocalEmailClient(IEmailClient):
    """
    A local email client for testing purposes.
    It does not send emails but logs them.
    """

    def __init__(self):
        self.outbox = []

    def send_email(self, to, subject, body):
        self.outbox.append(
            {
                "to": to,
                "subject": subject,
                "body": body,
            }
        )
        logger.info(
            f"""--------------------
Email recipient: "{to}"
Email subject: "{subject}"
Email body: "{body}"
--------------------"""
        )
