from app.common.email.template_renderer import Template<PERSON><PERSON><PERSON>


def test_template_renderer():
    renderer = TemplateRenderer("app/common/email/tests/email_templates")

    subject, body = renderer.render_template(
        "dummy.html",
        var1="foo",
        var2="bar",
    )

    assert subject == "Subject with templated variable: foo"
    assert "Body with templated variable: bar" in body
