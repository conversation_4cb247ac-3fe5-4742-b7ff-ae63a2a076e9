import pytest
from fastapi import BackgroundTasks
from pytest_mock import <PERSON><PERSON><PERSON><PERSON><PERSON>

from app.common.email.email_client import IEmailClient
from app.common.email.email_service import EmailService
from app.common.email.template_renderer import Template<PERSON>enderer


@pytest.fixture
def template_renderer_mock(mocker: MockerFixture):
    mock = mocker.create_autospec(TemplateRenderer)
    mock.render_template.return_value = "Test Subject", "Test Body"
    return mock


@pytest.fixture
def email_client_mock(mocker: MockerFixture):
    return mocker.create_autospec(IEmailClient)


@pytest.fixture
def background_tasks_mock(mocker: MockerFixture):
    return mocker.create_autospec(BackgroundTasks)


@pytest.fixture
def email_service(template_renderer_mock, email_client_mock, background_tasks_mock):
    return EmailService(
        template_renderer=template_renderer_mock,
        email_client=email_client_mock,
        background_tasks=background_tasks_mock,
    )


def test_send_email(
    email_service, template_renderer_mock, email_client_mock, background_tasks_mock
):
    to_email = "<EMAIL>"
    template_name = "welcome_email"
    render_args = {"user_name": "<PERSON>", "company": "Acme"}

    email_service.send_email(to=to_email, template_name=template_name, **render_args)

    background_tasks_mock.add_task.assert_called_once()

    call_args = background_tasks_mock.add_task.call_args
    task_func = call_args[0][0]
    task_kwargs = call_args[1]

    assert task_func == email_service._send_email_sync

    assert task_kwargs["to"] == to_email
    assert task_kwargs["template_name"] == template_name
    assert task_kwargs["user_name"] == "John"
    assert task_kwargs["company"] == "Acme"

    template_renderer_mock.render_template.assert_not_called()
    email_client_mock.send_email.assert_not_called()


def test_send_email_sync_directly(
    email_service, template_renderer_mock, email_client_mock
):
    to_email = "<EMAIL>"
    template_name = "welcome_email"
    render_args = {"user_name": "John", "company": "Acme"}

    email_service._send_email_sync(
        to=to_email, template_name=template_name, **render_args
    )

    template_renderer_mock.render_template.assert_called_once_with(
        template_name, **render_args
    )

    email_client_mock.send_email.assert_called_once_with(
        to=to_email, subject="Test Subject", body="Test Body"
    )
