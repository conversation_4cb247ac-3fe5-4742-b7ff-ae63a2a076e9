from abc import ABC, abstractmethod


class IEmailClient(ABC):
    """Interface defining an email client for sending emails."""

    outbox: list[dict[str, str]] = []

    @abstractmethod
    def send_email(
        self,
        to: str,
        subject: str,
        body: str,
    ) -> None:
        """
        Send an email to the specified recipient.

        Args:
            to (str): The recipient's email address.
            subject (str): The subject of the email.
            body (str): The body of the email.
        """
        pass
