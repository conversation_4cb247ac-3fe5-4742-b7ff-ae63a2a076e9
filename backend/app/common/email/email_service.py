from typing import Any

from fastapi import BackgroundTasks

from app.common.email.email_client import IEmailClient
from app.common.email.template_renderer import TemplateRenderer


class EmailService:
    def __init__(
        self,
        template_renderer: TemplateRenderer,
        email_client: IEmailClient,
        background_tasks: BackgroundTasks,
    ):
        self.template_renderer = template_renderer
        self.email_client = email_client
        self.background_tasks = background_tasks

    def send_email(
        self,
        to: str,
        template_name: str,
        **render_args: Any,
    ) -> None:
        self.background_tasks.add_task(
            self._send_email_sync, to=to, template_name=template_name, **render_args
        )

    def _send_email_sync(
        self,
        to: str,
        template_name: str,
        **render_args: Any,
    ) -> None:
        subject, body = self.template_renderer.render_template(
            template_name, **render_args
        )

        self.email_client.send_email(
            to=to,
            subject=subject,
            body=body,
        )
