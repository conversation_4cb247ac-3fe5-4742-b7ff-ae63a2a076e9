from typing import Annotated

from fastapi import Depends

from app.common.email.customerio_client import CustomerIOClient
from app.common.email.email_client import IEmailClient
from app.common.email.local_email_client import LocalEmailClient
from app.core.config import config


def get_email_client() -> IEmailClient:
    if config.mailer.email_client == "customerio":
        return CustomerIOClient()
    elif config.mailer.email_client == "local":
        return LocalEmailClient()
    else:
        raise ValueError(f"Unknown email client: {config.mailer.client}")


EmailClientDep = Annotated[IEmailClient, Depends(get_email_client)]
