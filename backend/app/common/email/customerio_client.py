from customerio import APIClient, Regions, SendEmailRequest

from app.common.email.email_client import IEmailClient
from app.core.config import config


class CustomerIOClient(IEmailClient):
    def __init__(self):
        self.client = APIClient(
            key=config.mailer.customerio_api_key,
            region=getattr(Regions, config.mailer.customerio_region.upper()),
        )

    def send_email(self, to: str, subject: str, body: str):
        email_request = SendEmailRequest(
            _from=config.mailer.from_email,
            to=to,
            subject=subject,
            body=body,
            identifiers={
                "email": to,
            },
        )
        self.client.send_email(email_request)
