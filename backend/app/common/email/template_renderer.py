from pathlib import Path
from typing import Any

from jinja2 import Environment, FileSystemLoader, select_autoescape

PROJECT_ROOT_DIR = Path(__file__).resolve().parents[3]


class TemplateRenderer:
    def __init__(self, template_dir: str):
        base_dir = "app/common/email/email_templates"
        self.env = Environment(
            loader=FileSystemLoader(
                [PROJECT_ROOT_DIR / template_dir, PROJECT_ROOT_DIR / base_dir]
            ),
            autoescape=select_autoescape(["html"]),
            trim_blocks=True,
            lstrip_blocks=True,
        )

    def render_template(
        self, template_name: str, **render_args: Any
    ) -> tuple[str, str]:
        template = self.env.get_template(template_name)

        # Render the subject separately
        subject = "".join(template.blocks["subject"](template.new_context(render_args)))

        body = template.render(**render_args)

        return subject, body
