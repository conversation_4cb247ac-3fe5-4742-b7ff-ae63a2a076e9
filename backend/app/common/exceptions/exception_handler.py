from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.responses import JSONResponse

from app.common.exceptions.base import (
    BadRequestException,
    ForbiddenException,
    NotFoundException,
    UnauthorizedException,
)


def register_exception_handlers(app: FastAPI):
    @app.exception_handler(Exception)
    async def global_exception_handler(_request: Request, _exc: Exception):
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error."},
        )

    @app.exception_handler(UnauthorizedException)
    async def unauthorized_exception_handler(
        _request: Request, exc: UnauthorizedException
    ):
        return JSONResponse(
            status_code=401,
            content={"detail": exc.detail},
        )

    @app.exception_handler(NotFoundException)
    async def not_found_exception_handler(_request: Request, exc: NotFoundException):
        return JSONResponse(
            status_code=404,
            content={"detail": exc.detail},
        )

    @app.exception_handler(BadRequestException)
    async def bad_request_exception_handler(
        _request: Request, exc: BadRequestException
    ):
        return J<PERSON>NResponse(
            status_code=400,
            content={"detail": exc.detail},
        )

    @app.exception_handler(ForbiddenException)
    async def forbidden_exception_handler(_request: Request, exc: ForbiddenException):
        return JSONResponse(
            status_code=403,
            content={"detail": exc.detail},
        )
