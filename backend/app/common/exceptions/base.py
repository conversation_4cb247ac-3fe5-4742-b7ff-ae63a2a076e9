class _BaseException(Exception):
    detail = "An error occurred."

    def __init__(self, detail: str | None = None):
        self.detail = detail or self.detail
        super().__init__(self.detail)

    def __str__(self):
        return self.detail


class UnauthorizedException(_BaseException):
    """Exception raised for unauthorized access."""

    detail = "Authentication error."


class BadRequestException(_BaseException):
    """Exception raised for invalid request parameters or state."""

    detail = "Bad request."


class NotFoundException(_BaseException):
    """Exception raised when a resource is not found."""

    detail = "Resource not found."


class ForbiddenException(_BaseException):
    """Exception raised for forbidden access."""

    detail = "Forbidden access."
