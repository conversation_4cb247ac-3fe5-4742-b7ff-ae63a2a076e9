from enum import Enum
from typing import Any

from sqlalchemy.types import String, TypeDecorator


class StringEnum(TypeDecorator[Enum]):
    impl = String
    cache_ok = True

    def __init__(self, enum_cls: type[Enum], **kw: Any) -> None:
        super().__init__(**kw)
        self.enum_cls = enum_cls

    def process_bind_param(self, value: Any, _dialect: Any) -> str | None:
        if value is None:
            return None
        if isinstance(value, str):
            return self.enum_cls(value).value
        if isinstance(value, self.enum_cls):
            return value.value
        raise ValueError(
            f"Expected string or {self.enum_cls.__name__}, got {type(value)}"
        )

    def process_result_value(self, value: Any, _dialect: Any) -> Enum | None:
        if value is None:
            return None
        return self.enum_cls(value)
