from enum import Enum

import pytest

from app.common.orm.types import StringEnum


class SampleEnum(str, Enum):
    VALUE1 = "value1"
    VALUE2 = "value2"


def test_init():
    type_decorator = StringEnum(SampleEnum)
    assert type_decorator.enum_cls == SampleEnum


def test_process_bind_param_with_enum():
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_bind_param(SampleEnum.VALUE1, None)
    assert result == "value1"


def test_process_bind_param_with_valid_string():
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_bind_param("value1", None)
    assert result == "value1"


def test_process_bind_param_with_none():
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_bind_param(None, None)
    assert result is None


def test_process_bind_param_with_invalid_string():
    type_decorator = StringEnum(SampleEnum)
    with pytest.raises(ValueError, match="'invalid' is not a valid SampleEnum"):
        type_decorator.process_bind_param("invalid", None)


def test_process_bind_param_with_invalid_type():
    type_decorator = StringEnum(SampleEnum)
    with pytest.raises(
        ValueError, match="Expected string or SampleEnum, got <class 'int'>"
    ):
        type_decorator.process_bind_param(123, None)


def test_process_result_value_with_valid_string():
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_result_value("value1", None)
    assert result == SampleEnum.VALUE1
    assert isinstance(result, SampleEnum)


def test_process_result_value_with_none():
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_result_value(None, None)
    assert result is None


def test_process_result_value_with_invalid_string():
    type_decorator = StringEnum(SampleEnum)
    with pytest.raises(ValueError, match="'invalid' is not a valid SampleEnum"):
        type_decorator.process_result_value("invalid", None)


@pytest.mark.parametrize(
    ("value", "expected"),
    [
        (SampleEnum.VALUE1, "value1"),  # enum -> string
        ("value1", "value1"),  # string -> string
        (None, None),  # None -> None
    ],
)
def test_process_bind_param_valid_cases(value, expected):
    type_decorator = StringEnum(SampleEnum)
    result = type_decorator.process_bind_param(value, None)
    assert result == expected


@pytest.mark.parametrize(
    ("invalid_value", "expected_error"),
    [
        ("invalid", "'invalid' is not a valid SampleEnum"),
        (123, "Expected string or SampleEnum, got <class 'int'>"),
        (True, "Expected string or SampleEnum, got <class 'bool'>"),
        ([], "Expected string or SampleEnum, got <class 'list'>"),
    ],
)
def test_process_bind_param_invalid_cases(invalid_value, expected_error):
    type_decorator = StringEnum(SampleEnum)
    with pytest.raises(ValueError, match=expected_error):
        type_decorator.process_bind_param(invalid_value, None)
