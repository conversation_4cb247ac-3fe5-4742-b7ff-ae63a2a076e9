import asyncio
from typing import Any

from app.common.helpers.logger import get_logger
from app.common.task_runner.base_task import BaseTask
from app.common.task_runner.sequential_task import SequentialTask


class TaskRunner:
    """
    Async runner for tasks with daemon support and controlled stopping.
    """

    def __init__(self, tasks: list[BaseTask] | None = None):
        """Initialize the task runner."""
        self.tasks = tasks or []
        self.logger = get_logger(self.__class__.__name__)

        # Task management
        self._tasks: dict[str, asyncio.Task] = {}
        self._composite_tasks: list[BaseTask] = []
        self.is_running = False

        # Daemon control
        self.should_stop = False

    def add_task(self, task: BaseTask) -> None:
        """
        Add a task to the runner.

        Args:
            task: The task to add

        Raises:
            RuntimeError: If the runner is already running
        """
        if self.is_running:
            raise RuntimeError("Cannot add tasks while the runner is running")

        self.tasks.append(task)
        self.logger.info(f"Added task: {task.__class__.__name__}")

    async def run(self) -> dict[str, Any]:
        """
        Execute all tasks once.

        Returns:
            Results of the execution
        """
        if len(self.tasks) == 1:
            # Single task - execute directly with error handling
            try:
                return await self.tasks[0].execute_once()
            except Exception as e:
                self.logger.exception(f"Error in single task execution: {e}")
                return {"status": "error", "error": str(e)}
        elif len(self.tasks) > 1:
            # Multiple tasks - create a sequential task
            sequential = SequentialTask(self.tasks)
            return await sequential.execute_once()
        else:
            self.logger.warning("No tasks to execute")
            return {}

    async def start_daemon(
        self,
        sequential_execution: bool = False,
        sequential_interval_seconds: int = 60,
        wait: bool = False,
    ) -> None:
        """
        Start the runner in daemon mode using asyncio tasks.

        Args:
            sequential_execution: Whether to run tasks sequentially
            sequential_interval_seconds: Interval between sequential executions
            wait: If True, wait for completion. If False, return immediately.

        Raises:
            RuntimeError: If the runner is already running
        """
        # Check if already running
        if self.is_running:
            raise RuntimeError("Task runner is already running")

        # Check if we have tasks to run
        if not self.tasks:
            self.logger.warning("No tasks to execute in daemon mode")
            return

        # Reset daemon control flags
        self.should_stop = False
        self.is_running = True

        # Keep track of composite tasks for proper stopping
        self._composite_tasks = []

        if sequential_execution and len(self.tasks) > 1:
            # Sequential mode - create a composite task
            sequential_task = SequentialTask(
                self.tasks, interval_seconds=sequential_interval_seconds
            )
            self._composite_tasks.append(sequential_task)

            task_name = "sequential"
            async_task = asyncio.create_task(
                self._run_task_daemon(sequential_task, task_name), name=task_name
            )
            self._tasks[task_name] = async_task

        else:
            # Parallel mode - run each task concurrently
            for task in self.tasks:
                task_name = f"{task.__class__.__name__}_{task.task_id}"
                async_task = asyncio.create_task(
                    self._run_task_daemon(task, task_name), name=task_name
                )
                self._tasks[task_name] = async_task

        self.logger.info(
            f"Task runner daemon started in {'sequential' if sequential_execution else 'parallel'} mode"
        )

        # Wait if requested
        if wait:
            await self.wait_for_completion()

    async def wait_for_completion(self) -> None:
        """
        Wait for all daemon tasks to complete.
        This is a separate method so start_daemon can return immediately.
        """
        if not self.is_running:
            self.logger.warning("Task runner is not running")
            return

        if self._tasks:
            try:
                self.logger.info("Waiting for all tasks to complete...")
                await asyncio.gather(*self._tasks.values(), return_exceptions=True)
            except Exception as e:
                self.logger.exception(f"Error in task runner daemon: {e}")
            finally:
                await self.stop_daemon()

    async def _run_task_daemon(self, task: BaseTask, name: str):
        """Run a task in daemon mode."""
        self.logger.info(f"Started daemon for {name}")

        try:
            await task.execute()
        except Exception as e:
            self.logger.exception(f"Error in task {name}: {e}")
        finally:
            self.logger.info(f"Daemon for {name} stopped")

    async def stop_daemon(self) -> None:
        """Stop all daemon tasks."""
        if not self.is_running:
            self.logger.info("Task runner is not running")
            return

        # Set stop flags
        self.should_stop = True
        self.is_running = False

        self.logger.info("Stopping task runner...")

        # Signal all tasks to stop
        for task in self.tasks:
            task.stop()

        # Also stop composite tasks (like SequentialTask)
        for composite_task in self._composite_tasks:
            composite_task.stop()

        # Give tasks time to stop gracefully
        graceful_timeout = 5  # seconds
        self.logger.info(f"Waiting {graceful_timeout}s for tasks to stop gracefully...")

        try:
            await asyncio.wait_for(
                asyncio.gather(
                    *[task for task in self._tasks.values() if not task.done()],
                    return_exceptions=True,
                ),
                timeout=graceful_timeout,
            )
            self.logger.info("All tasks stopped gracefully")
        except TimeoutError:
            self.logger.info("Graceful timeout reached, forcing cancellation...")

            # Force cancel remaining tasks
            for name, async_task in list(self._tasks.items()):
                if not async_task.done():
                    self.logger.info(f"Force cancelling task {name}...")
                    async_task.cancel()

                    try:
                        await asyncio.wait_for(async_task, timeout=5)
                    except (TimeoutError, asyncio.CancelledError):
                        self.logger.info(f"Task {name} cancelled")
                    except Exception as e:
                        self.logger.warning(f"Error stopping task {name}: {e}")

        # Clean up
        self._tasks = {}
        self._composite_tasks = []

        self.logger.info("Task runner stopped")

    def get_status(self) -> dict[str, Any]:
        """Get the current task runner status."""
        return {
            "running": self.is_running,
            "should_stop": self.should_stop,
            "tasks": [
                {
                    "name": task.__class__.__name__,
                    "task_id": task.task_id,
                    "running": any(
                        task.task_id in task_name and not async_task.done()
                        for task_name, async_task in self._tasks.items()
                    ),
                    "should_stop": task.should_stop,
                }
                for task in self.tasks
            ],
            "active_tasks": len([t for t in self._tasks.values() if not t.done()]),
        }
