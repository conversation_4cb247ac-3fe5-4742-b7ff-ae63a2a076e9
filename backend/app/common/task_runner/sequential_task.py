from app.common.task_runner.base_task import BaseTask


class SequentialTask(BaseTask):
    """
    A composite task that executes multiple tasks in sequence.
    """

    def __init__(
        self,
        tasks: list[BaseTask],
        interval_seconds: int = 60,
        task_id: str | None = None,
    ):
        """
        Initialize the sequential task.

        Args:
            tasks: List of tasks to execute in sequence.
            interval_seconds: Interval in seconds between executions.
                              Default is 60 seconds.
            task_id: Unique identifier for the task.
                     If not provided, a UUID will be generated.
        """
        super().__init__(task_id=task_id, interval_seconds=interval_seconds)
        self.tasks = tasks

    async def execute_once(self):
        """Execute all tasks in sequence once."""
        results = {}

        for i, task in enumerate(self.tasks):
            if self.should_stop:
                self.logger.info("Sequential execution stopped early")
                break

            task_name = task.__class__.__name__
            self.logger.info(f"Executing task {i + 1}/{len(self.tasks)}: {task_name}")

            try:
                results[task.task_id] = await task.execute_once()
            except Exception as e:
                self.logger.exception("Error executing %s", task_name)
                results[task.task_id] = {"error": str(e)}

        return results

    def stop(self):
        """Stop this task and all child tasks."""
        super().stop()
        # Also stop all child tasks in case they are running independently
        for task in self.tasks:
            task.stop()

    def get_status(self) -> dict:
        """Get status including child tasks."""
        status = super().get_status()
        status.update(
            {
                "tasks_count": len(self.tasks),
                "interval_seconds": self.interval_seconds,
                "child_tasks": [
                    {
                        "id": task.task_id,
                        "name": task.__class__.__name__,
                        "should_stop": task.should_stop,
                    }
                    for task in self.tasks
                ],
            }
        )
        return status
