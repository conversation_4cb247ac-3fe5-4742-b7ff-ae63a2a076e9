import asyncio
import uuid

import typer

from app.core.database import Async<PERSON><PERSON><PERSON>Local
from app.integrations.processors.embedders.noop_embedder import <PERSON>op<PERSON>mbedder
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

app = typer.Typer(help="Document search commands")


async def _similarity_search_async(
    tenant_id: uuid.UUID,
    source: IntegrationSource,
    query: str,
    limit: int,
):
    embedder = NoopEmbedder()
    query_embedding = await embedder.embed_text(query)

    async with AsyncSessionLocal() as session:
        store = PostgresDocumentStore(session, tenant_id, source)
        results = await store.find_similar_documents(query_embedding, limit)

    typer.echo(f"Found {len(results)} similar documents:")
    for doc, score in results:
        typer.echo(f"ID: {doc.id}")
        typer.echo(f"Content: {doc.content}")
        typer.echo(f"Timestamp: {doc.source_timestamp}")
        typer.echo(f"Tags: {doc.tags}")
        typer.echo(f"Score: {score}")
        typer.echo("---")


@app.command()
def similarity_search(
    tenant_id: uuid.UUID = typer.Option(..., help="Tenant ID"),
    source: IntegrationSource = typer.Option(..., help="Source ID"),
    query: str = typer.Option(..., help="Query text"),
    limit: int = typer.Option(5, help="Maximum number of results"),
):
    asyncio.run(_similarity_search_async(tenant_id, source, query, limit))
