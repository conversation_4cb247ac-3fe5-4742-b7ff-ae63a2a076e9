import asyncio
import uuid

import typer

from app.agentic.evals.pipeline import LangfuseEvaluationPipeline
from app.workspace.types import EnvironmentType

app = typer.Typer(help="Langfuse evaluation commands", no_args_is_help=True)


@app.command()
def evaluate_langfuse_dataset_for_user_crm_account(
    dataset_name: str = typer.Argument(
        ..., help="Name of the Langfuse dataset to evaluate"
    ),
    user_id: uuid.UUID = typer.Option(..., help="User ID"),
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    crm_account_id: str = typer.Option(..., help="CRM account ID to evaluate"),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
    experiment_name: str = typer.Option(..., help="Name of the experiment"),
    experiment_description: str = typer.Option(
        ..., help="Description of the experiment"
    ),
):
    try:
        typer.echo(f"Starting evaluation for dataset: {dataset_name}")

        asyncio.run(
            _evaluate_langfuse_dataset_for_user_crm_account(
                dataset_name,
                org_id,
                user_id,
                crm_account_id,
                env,
                experiment_name,
                experiment_description,
            )
        )
    except Exception as e:
        typer.echo(f"Evaluation failed: {e}")
        raise typer.Exit(code=1)


async def _evaluate_langfuse_dataset_for_user_crm_account(
    dataset_name: str,
    org_id: uuid.UUID,
    user_id: uuid.UUID,
    crm_account_id: str,
    env: EnvironmentType,
    experiment_name: str,
    experiment_description: str,
):
    async with LangfuseEvaluationPipeline(
        dataset_name=dataset_name,
        org_id=org_id,
        user_id=user_id,
        crm_account_id=crm_account_id,
        env_type=env,
        experiment_name=experiment_name,
        experiment_description=experiment_description,
    ) as langfuse_pipeline:
        await langfuse_pipeline.run_evaluation()
