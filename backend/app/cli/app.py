import typer

from app.cli import crm, document, evaluation, files, messaging
from app.core.config import config
from app.core.logging_config import setup_logging_config

# Set up logging
setup_logging_config(config)

app = typer.Typer(help="Pearl management commands", no_args_is_help=True)

app.add_typer(crm.app, name="crm")
app.add_typer(messaging.app, name="messaging")
app.add_typer(document.app, name="document")
app.add_typer(files.app, name="files")
app.add_typer(evaluation.app, name="evaluation")
