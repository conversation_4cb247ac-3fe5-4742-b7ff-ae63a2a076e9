import asyncio
import uuid

import typer

from app.cli.utils import parse_csv
from app.integrations.types import IntegrationSource
from app.workspace.integrations.workflows import MessagingWorkflow
from app.workspace.types import EnvironmentType

app = typer.Typer(
    help="Data integration pipeline management commands", no_args_is_help=True
)


@app.command()
def start_ingestion(
    source: IntegrationSource = typer.Argument(..., help="Integration source to start"),
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    channel_ids: str = typer.Option(
        ..., help="Slack channel IDs to ingest (comma-separated)"
    ),
    interval: int = typer.Option(300, help="Execution interval in seconds"),
    lookback_days: int = typer.Option(
        7, help="Number of days to look back for messages"
    ),
    batch_size: int = typer.Option(
        100, help="Number of messages to fetch in each Slack API call"
    ),
    daemon: bool = typer.Option(
        False, "--daemon", "-d", help="Run in daemon mode (continuous execution)"
    ),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
):
    channels = parse_csv(channel_ids)

    if not channels:
        typer.echo("At least one channel ID is required")
        raise typer.Exit(code=1)

    try:
        result = asyncio.run(
            _start_ingestion(
                source=source,
                org_id=org_id,
                channels=channels,
                interval=interval,
                lookback_days=lookback_days,
                batch_size=batch_size,
                daemon=daemon,
                env=env,
            )
        )

        if result["status"] == "success":
            if daemon:
                typer.echo(f"{source.value} channel ingestion daemon has started")
            else:
                typer.echo(f"{source.value} channel ingestion completed successfully!")
                if result.get("details"):
                    typer.echo(str(result["details"]))
        else:
            typer.echo(f"Error: {result.get('error', 'Unknown error')}")
            raise typer.Exit(code=1)

    except Exception as e:
        typer.echo(f"Error starting {source.value} ingestion: {e}")
        raise typer.Exit(code=1)


@app.command()
def start_processing(
    source: IntegrationSource = typer.Argument(
        ..., help="Integration source to start processing"
    ),
    org_id: uuid.UUID = typer.Option(..., help="Organization ID"),
    channel_ids: str = typer.Option(
        ..., help="Channel IDs to process (comma-separated)"
    ),
    interval: int = typer.Option(300, help="Execution interval in seconds"),
    batch_size: int = typer.Option(
        100, help="Number of changes to process in each batch"
    ),
    daemon: bool = typer.Option(
        False, "--daemon", "-d", help="Run in daemon mode (continuous execution)"
    ),
    env: EnvironmentType = typer.Option(EnvironmentType.PROD, help="Environment type"),
):
    channels = parse_csv(channel_ids)

    if not channels:
        typer.echo("At least one channel ID is required")
        raise typer.Exit(code=1)

    try:
        result = asyncio.run(
            _start_processing(
                source=source,
                org_id=org_id,
                channels=channels,
                interval=interval,
                batch_size=batch_size,
                daemon=daemon,
                env=env,
            )
        )

        if result["status"] == "success":
            if daemon:
                typer.echo(f"{source.value} channel processing daemon has started")
            else:
                typer.echo(f"{source.value} channel processing completed successfully!")
                if result.get("details"):
                    typer.echo(str(result["details"]))
        else:
            typer.echo(f"Error: {result.get('error', 'Unknown error')}")
            raise typer.Exit(code=1)

    except Exception as e:
        typer.echo(f"Error starting {source.value} processing: {e}")
        raise typer.Exit(code=1)


async def _start_ingestion(
    source: IntegrationSource,
    org_id: uuid.UUID,
    channels: list[str],
    interval: int,
    lookback_days: int,
    batch_size: int,
    daemon: bool,
    env: EnvironmentType,
) -> dict:
    async with MessagingWorkflow(
        org_id=org_id, source=source, env_type=env
    ) as workflow:
        result = await workflow.start_ingestion(
            channel_ids=channels,
            interval_seconds=interval,
            lookback_days=lookback_days,
            batch_size=batch_size,
            daemon_mode=daemon,
        )
        return result


async def _start_processing(
    source: IntegrationSource,
    org_id: uuid.UUID,
    channels: list[str],
    interval: int,
    batch_size: int,
    daemon: bool,
    env: EnvironmentType,
) -> dict:
    async with MessagingWorkflow(
        org_id=org_id, source=source, env_type=env
    ) as workflow:
        result = await workflow.start_processing(
            channel_ids=channels,
            interval_seconds=interval,
            batch_size=batch_size,
            daemon_mode=daemon,
        )
        return result
