import datetime
from typing import Any

from app.common.helpers.logger import get_logger
from app.integrations.schemas import MessageData

logger = get_logger()


def convert_slack_message_to_message_data(
    slack_message: dict[str, Any], channel_id: str
) -> MessageData:
    """
    Convert a Slack message to a MessageData object.

    Args:
        slack_message: Message object from Slack API
        channel_id: The Slack channel

    Returns:
        MessageData object
    """
    # Convert timestamp string to datetime
    ts = slack_message.get("ts", "0")
    sent_at = datetime.datetime.fromtimestamp(float(ts), tz=datetime.UTC)

    # Get the last edit timestamp if available
    last_edit_ts = slack_message.get("edited", {}).get("ts")
    last_edit_at = None
    if last_edit_ts:
        last_edit_at = datetime.datetime.fromtimestamp(
            float(last_edit_ts), tz=datetime.UTC
        )

    # Get thread ID
    thread_ts = slack_message.get("thread_ts")
    thread_id = thread_ts

    # Check for tombstone
    # In Conversations API, tombstone is identified by a subtype of "tombstone"
    is_tombstone = slack_message.get("subtype") == "tombstone" or slack_message.get(
        "is_tombstone", False
    )

    # Extract content safely
    content = slack_message.get("text", "")

    # Get user ID of the author
    author = slack_message.get("user", "unknown")

    # Use message channel if available, otherwise use provided channel_id
    message_channel_id = slack_message.get("channel", channel_id)

    # Create unique message id
    # todo: I need to better manage it, it is pretty critical, we must be sure not to forget to convert ids
    message_id = f"{message_channel_id}:{ts}"
    thread_id = f"{message_channel_id}:{thread_id}" if thread_id else None

    # Determine parent_id
    # Set parent_id only when thread_id exists and is different from message_id
    parent_id = None
    if thread_id and thread_id != message_id:
        parent_id = thread_id

    return MessageData(
        message_id=message_id,
        channel_id=message_channel_id,
        content=content,
        sent_at=sent_at,
        last_edit_at=last_edit_at,
        tombstone=is_tombstone,
        author=author,
        thread_id=thread_id,
        parent_id=parent_id,
    )
