from hubspot.crm.companies.exceptions import (
    UnauthorizedException as CompaniesApiException,
)
from hubspot.crm.contacts.exceptions import (
    UnauthorizedException as ContactsApiException,
)
from hubspot.crm.deals.exceptions import UnauthorizedException as DealsApiException
from hubspot.crm.objects.exceptions import UnauthorizedException as ObjectsApiException

from app.common.helpers.logger import get_logger
from app.integrations.adapters.hubspot.client import HubSpotClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.oauth_refresh_mixin import BaseOAuthRefreshTokenMixin

logger = get_logger()


class HubSpotRefreshTokenClientMixin(BaseOAuthRefreshTokenMixin):
    credentials: ICredentials
    hubspot_client: HubSpotClient

    def init_hubspot_client(self, credentials: ICredentials):
        self.credentials = credentials

        if "access_token" not in self.credentials.secrets:
            error_msg = "Missing required 'access_token' for HubSpot authentication"
            logger.exception(error_msg)
            raise ValueError(error_msg)

        logger.debug("Using access token authentication for HubSpot")

        self.hubspot_client = HubSpotClient(
            access_token=self.credentials.secrets["access_token"]
        )

    def get_integration_specific_auth_exceptions(self) -> tuple[type, ...]:
        return (
            CompaniesApiException,
            ContactsApiException,
            DealsApiException,
            ObjectsApiException,
        )

    def get_client_cache_attribute_name(self) -> str:
        return "hubspot_client"

    async def refresh_integration_token(self):
        logger.info("Refreshing HubSpot token")
        self.credentials = await self.credentials.refresh_token()
        logger.info("Re-initializing HubSpot client with refreshed token")
        self.init_hubspot_client(self.credentials)

    def reinitialize_client(self):
        self.init_hubspot_client(self.credentials)
