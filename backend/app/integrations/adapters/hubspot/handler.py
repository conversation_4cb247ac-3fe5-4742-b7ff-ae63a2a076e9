from datetime import datetime
from typing import Any, Literal

from app.common.helpers.logger import get_logger
from app.common.helpers.validation import is_valid_timestamp
from app.integrations.adapters.hubspot.access_resolver import (
    HubSpotAccountAccessResolver,
)
from app.integrations.adapters.hubspot.client import HubSpotClientError
from app.integrations.adapters.hubspot.refresh_token_client_mixin import (
    HubSpotRefreshTokenClientMixin,
)
from app.integrations.base.credentials_resolver import (
    ICredentials,
)
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics
from app.integrations.types import HubSpotObjectType

logger = get_logger()


class HubSpotHandler(HubSpotRefreshTokenClientMixin):
    def __init__(self, credentials: ICredentials):
        self.init_hubspot_client(credentials)

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_opportunity(self, opportunity_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.DEAL.value, opportunity_id
        )

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_opportunities_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def search_opportunities(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["dealname", "dealstage"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                elif field.lower() in ["amount", "closedate"]:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.DEAL.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_account(self, account_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.COMPANY.value, account_id
        )

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def search_accounts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["name", "domain"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.COMPANY.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_contact(self, contact_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.CONTACT.value, contact_id
        )

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_contacts_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def search_contacts(
        self,
        search_criteria: dict[str, Any],
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        filters = []

        for field, value in search_criteria.items():
            if value:
                if field.lower() in ["firstname", "lastname", "email"]:
                    filters.append(
                        {
                            "propertyName": field,
                            "operator": "CONTAINS_TOKEN",
                            "value": str(value),
                        }
                    )
                else:
                    filters.append(
                        {"propertyName": field, "operator": "EQ", "value": str(value)}
                    )

        if not filters:
            return []

        filter_groups = [{"filters": filters}]
        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.CONTACT.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_task(self, task_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.TASK.value, task_id
        )

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_tasks_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_tasks_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_tasks_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.TASK.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_event(self, event_id: str) -> dict[str, Any]:
        return await self.hubspot_client.get_object(
            HubSpotObjectType.MEETING.value, event_id
        )

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_events_by_contact(
        self,
        contact_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not contact_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.contact",
                        "operator": "EQ",
                        "value": contact_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_events_by_account(
        self,
        account_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not account_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.company",
                        "operator": "EQ",
                        "value": account_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def list_events_by_opportunity(
        self,
        opportunity_id: str,
        fields: list[str] | Literal["ALL"] | None = None,
        limit: int = 100,
        offset: int = 0,
    ) -> list[dict[str, Any]]:
        if not opportunity_id:
            return []

        filter_groups = [
            {
                "filters": [
                    {
                        "propertyName": "associations.deal",
                        "operator": "EQ",
                        "value": opportunity_id,
                    }
                ]
            }
        ]

        after = str(offset) if offset > 0 else None

        result = await self.hubspot_client.search_objects(
            object_type=HubSpotObjectType.MEETING.value,
            filter_groups=filter_groups,
            properties=fields,
            limit=limit,
            after=after,
        )

        return result.get("results", [])

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def resolve_account_access(
        self, crm_user_id: str
    ) -> list[CRMAccountAccessData]:
        resolver = HubSpotAccountAccessResolver(client=self.hubspot_client)
        return await resolver.get_user_account_access(crm_user_id)

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_account_closed_won_revenue(
        self,
        account_id: str,
        field_mapping: dict[str, Any] | None = None,
    ) -> float:
        logger.info(f"Getting closed won revenue for HubSpot account: {account_id}")
        logger.info(f"Mapping: {field_mapping}")
        # TODO: Implement HubSpot closed won revenue calculation
        total_revenue_usd = 0.0
        return total_revenue_usd

    @HubSpotRefreshTokenClientMixin.handle_expired_session
    async def get_metrics(
        self, crm_user_id: str, _field_mapping: dict[str, Any] | None = None
    ) -> CRMMetrics:
        try:
            deal_metrics = await self._get_deal_metrics_for_user(crm_user_id)

            quota = await self._get_quota_from_goals(crm_user_id)

            return CRMMetrics(
                quota=int(quota),
                closed_won=int(deal_metrics["closed_won"]),
                pipeline=int(deal_metrics["pipeline"]),
                forecast=int(deal_metrics["forecast"]),
            )

        except (
            KeyError,
            TypeError,
            ValueError,
            AttributeError,
            HubSpotClientError,
        ) as e:
            logger.exception(
                f"Failed to get metrics for user {crm_user_id} due to {type(e).__name__}: {e}"
            )
            return CRMMetrics(quota=0, closed_won=0, pipeline=0, forecast=0)

    async def _get_deal_metrics_for_user(
        self,
        crm_user_id: str,
    ) -> dict[str, float]:
        current_year = datetime.now().year
        year_start = int(datetime(current_year, 1, 1).timestamp() * 1000)

        closed_won = await self._get_deal_sum_by_filters(
            [
                {
                    "propertyName": "hubspot_owner_id",
                    "operator": "EQ",
                    "value": crm_user_id,
                },
                {"propertyName": "hs_is_closed_won", "operator": "EQ", "value": "true"},
                {
                    "propertyName": "closedate",
                    "operator": "GTE",
                    "value": str(year_start),
                },
            ]
        )

        pipeline_deals = await self._get_all_deals_by_filters(
            [
                {
                    "propertyName": "hubspot_owner_id",
                    "operator": "EQ",
                    "value": crm_user_id,
                },
                {"propertyName": "hs_is_closed", "operator": "EQ", "value": "false"},
            ],
            properties=["amount", "hs_forecast_amount"],
        )

        pipeline = 0.0
        forecast = 0.0

        for deal in pipeline_deals:
            props = deal.get("properties", {})
            amount = props.get("amount")
            forecast_amount = props.get("hs_forecast_amount")

            if amount:
                try:
                    pipeline += float(amount)
                except (ValueError, TypeError):
                    continue

            if forecast_amount:
                try:
                    forecast += float(forecast_amount)
                except (ValueError, TypeError):
                    continue

        return {
            "closed_won": closed_won,
            "pipeline": pipeline,
            "forecast": forecast,
        }

    async def _paginate_deals(
        self, filters: list[dict[str, str]], properties: list[str] | None = None
    ):
        after = None

        while True:
            batch = await self.hubspot_client.search_objects(
                object_type=HubSpotObjectType.DEAL.value,
                filter_groups=[{"filters": filters}],
                properties=properties,
                limit=200,
                after=after,
            )

            if batch and "results" in batch:
                for deal in batch.get("results", []):
                    yield deal

                paging = batch.get("paging")
                if paging:
                    next_page = paging.get("next", {})
                    after = next_page.get("after") if next_page else None
                else:
                    after = None

                if not after:
                    break
            else:
                break

    async def _get_all_deals_by_filters(
        self, filters: list[dict[str, str]], properties: list[str] | None = None
    ) -> list[dict[str, Any]]:
        return [deal async for deal in self._paginate_deals(filters, properties)]

    async def _get_deal_sum_by_filters(self, filters: list[dict[str, str]]) -> float:
        total = 0.0
        invalid_amounts = []

        async for deal in self._paginate_deals(filters, ["amount"]):
            amount = deal.get("properties", {}).get("amount")
            if amount:
                try:
                    amount_value = float(amount)
                    total += amount_value
                except (ValueError, TypeError):
                    invalid_amounts.append(amount)
                    continue

        if invalid_amounts:
            logger.warning(
                f"Encountered {len(invalid_amounts)} invalid amounts that could not be converted to float: {invalid_amounts}"
            )

        return total

    async def _get_quota_from_goals(
        self,
        crm_user_id: str,
    ) -> float:
        try:
            current_year = datetime.now().year

            goals_filter = [
                {
                    "filters": [
                        {
                            "propertyName": "hubspot_owner_id",
                            "operator": "EQ",
                            "value": crm_user_id,
                        }
                    ]
                }
            ]

            goals_response = await self.hubspot_client.search_objects(
                object_type=HubSpotObjectType.GOAL.value,
                filter_groups=goals_filter,
                properties=[
                    "hs_goal_target_value",
                    "hs_goal_type",
                    "hs_goal_period_start",
                    "hs_goal_period_end",
                    "hubspot_owner_id",
                ],
                limit=100,
            )

            goals = goals_response.get("results", [])

            total_quota = 0.0
            for goal in goals:
                goal_props = goal.get("properties", {})
                goal_type = goal_props.get("hs_goal_type", "").upper()
                target_value = goal_props.get("hs_goal_target_value")
                period_start = goal_props.get("hs_goal_period_start")

                if "REVENUE" in goal_type and target_value:
                    try:
                        quota_amount = float(target_value)

                        if period_start:
                            if is_valid_timestamp(period_start):
                                start_timestamp = int(period_start) / 1000
                                start_datetime = datetime.fromtimestamp(start_timestamp)
                                if start_datetime.year == current_year:
                                    total_quota += quota_amount
                            else:
                                total_quota += quota_amount
                        else:
                            total_quota += quota_amount

                    except (ValueError, TypeError, OverflowError) as e:
                        logger.exception(
                            f"Could not process goal {goal} due to {type(e).__name__}: {e}"
                        )
                        continue

            return total_quota

        except (KeyError, TypeError, AttributeError, HubSpotClientError) as e:
            logger.exception(
                f"Could not retrieve goals for user {crm_user_id} due to {type(e).__name__}: {e}"
            )
            return 0.0
