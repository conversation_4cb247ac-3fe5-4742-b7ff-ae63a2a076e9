from typing import Any

from googleapiclient.discovery import build

from app.common.helpers.logger import get_logger
from app.common.helpers.to_async import to_async
from app.integrations.base.google_client import BaseGoogleClient

logger = get_logger()


class GmailClient(BaseGoogleClient):
    """Gmail API client for email operations."""

    def __init__(self, credentials: dict[str, Any]):
        super().__init__(credentials)
        self._service = None

    @property
    def service(self):
        if self._service is None:
            self._service = build("gmail", "v1", credentials=self.credentials)
        return self._service

    @to_async
    def get_user_info(self) -> dict[str, Any]:
        try:
            profile = self.service.users().getProfile(userId="me").execute()
            return profile
        except Exception as e:
            logger.exception(f"Error fetching user info: {e}")
            raise

    @to_async
    def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> dict[str, Any]:
        try:
            result = (
                self.service.users()
                .messages()
                .list(
                    userId="me",
                    q=query,
                    maxResults=max_results,
                    pageToken=page_token,
                    includeSpamTrash=include_spam_trash,
                )
                .execute()
            )
            return result
        except Exception as e:
            logger.exception(f"Error listing messages: {e}")
            raise

    @to_async
    def get_message(
        self, message_id: str, message_format: str = "full"
    ) -> dict[str, Any]:
        try:
            message = (
                self.service.users()
                .messages()
                .get(userId="me", id=message_id, format=message_format)
                .execute()
            )
            return message
        except Exception as e:
            logger.exception(f"Error getting message {message_id}: {e}")
            raise

    @to_async
    def list_labels(self) -> dict[str, Any]:
        try:
            result = self.service.users().labels().list(userId="me").execute()
            return result
        except Exception as e:
            logger.exception(f"Error listing labels: {e}")
            raise

    @to_async
    def modify_message(
        self,
        message_id: str,
        add_label_ids: list[str] | None = None,
        remove_label_ids: list[str] | None = None,
    ) -> dict[str, Any]:
        try:
            body = {}
            if add_label_ids:
                body["addLabelIds"] = add_label_ids
            if remove_label_ids:
                body["removeLabelIds"] = remove_label_ids

            result = (
                self.service.users()
                .messages()
                .modify(userId="me", id=message_id, body=body)
                .execute()
            )
            return result
        except Exception as e:
            logger.exception(f"Error modifying message {message_id}: {e}")
            raise

    @to_async
    def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> dict[str, Any]:
        try:
            result = (
                self.service.users()
                .threads()
                .list(
                    userId="me",
                    q=query,
                    maxResults=max_results,
                    pageToken=page_token,
                    includeSpamTrash=include_spam_trash,
                )
                .execute()
            )
            return result
        except Exception as e:
            logger.exception(f"Error listing threads: {e}")
            raise

    @to_async
    def get_thread(self, thread_id: str, thread_format: str = "full") -> dict[str, Any]:
        try:
            thread = (
                self.service.users()
                .threads()
                .get(userId="me", id=thread_id, format=thread_format)
                .execute()
            )
            return thread
        except Exception as e:
            logger.exception(f"Error getting thread {thread_id}: {e}")
            raise

    @to_async
    def get_attachment(self, message_id: str, attachment_id: str) -> dict[str, Any]:
        try:
            attachment = (
                self.service.users()
                .messages()
                .attachments()
                .get(userId="me", messageId=message_id, id=attachment_id)
                .execute()
            )
            return attachment
        except Exception as e:
            logger.exception(f"Error getting attachment {attachment_id}: {e}")
            raise
