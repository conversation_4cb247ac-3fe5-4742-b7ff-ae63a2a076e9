from typing import <PERSON><PERSON><PERSON>

from app.common.helpers.logger import get_logger
from app.integrations.adapters.gcs.client import GCSClient
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.schemas import FileData
from app.integrations.types import IntegrationSource

logger = get_logger()


class GCSAdapter(BaseFileAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self._client = self._create_client(credentials)

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.GCS

    def _create_client(self, credentials: ICredentials) -> GCSClient:
        """Create a GCS client using the provided credentials."""
        if "gcs_key" not in credentials.secrets:
            error_msg = "GCS key not found in credentials"
            logger.exception(error_msg)
            raise ValueError(error_msg)

        return GCSClient(
            gcs_credentials=credentials.secrets["gcs_key"],
        )

    async def upload_file(
        self,
        bucket_name: str,
        file_obj: BinaryIO,
        file_name: str,
        content_type: str | None = None,
    ) -> None:
        await self._client.upload_file(
            bucket_name=bucket_name,
            file_obj=file_obj,
            file_name=file_name,
            content_type=content_type,
        )

    async def download_file(self, bucket_name: str, file_name: str) -> bytes:
        return await self._client.download_file(bucket_name, file_name)

    async def delete_file(self, bucket_name: str, file_name: str) -> None:
        await self._client.delete_file(bucket_name, file_name)

    async def list_files(self, bucket_name: str) -> list[FileData]:
        files = await self._client.list_files(bucket_name)
        return [
            FileData(
                id=file.id,
                name=file.name,
                size=file.size,
                time_created=file.time_created,
                last_modified=file.updated,
                md5_hash=file.md5_hash,
                content_type=file.content_type,
            )
            for file in files
        ]

    async def create_bucket(self, bucket_name: str) -> None:
        await self._client.create_bucket(bucket_name)

    async def bucket_exists(self, bucket_name: str) -> bool:
        return await self._client.bucket_exists(bucket_name)
