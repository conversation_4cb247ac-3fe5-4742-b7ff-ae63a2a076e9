import logging
from datetime import datetime
from typing import Any

from app.core.config import config
from app.integrations.backends.email.process_runner import <PERSON>ailProcessRunner
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.base.email_adapter import BaseEmailAdapter
from app.integrations.base.email_backend import BaseEmailBackend
from app.integrations.context import IntegrationContext
from app.integrations.processors.embedders.openai_embedder import OpenAIEmbedder
from app.integrations.schemas import (
    CRMAccountData,
    DocumentData,
    EmailMessage,
    EmailThread,
)
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

logger = logging.getLogger(__name__)


class EmailBackend(BaseEmailBackend):
    """Email backend implementation that wraps email adapters."""

    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseEmailAdapter],
        source: IntegrationSource,
    ):
        super().__init__(context, adapter_class, source)

    async def _get_adapter(self) -> BaseEmailAdapter:
        credentials_resolver: ICredentialsResolver = self.context.credentials_resolver
        credentials = await credentials_resolver.get_credentials(self.source)
        return self.adapter_class(credentials)

    async def get_user_info(self) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.get_user_info()

    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        adapter = await self._get_adapter()
        return await adapter.list_messages(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_message(self, message_id: str) -> EmailMessage:
        adapter = await self._get_adapter()
        return await adapter.get_message(message_id)

    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        adapter = await self._get_adapter()
        return await adapter.list_threads(
            query=query,
            max_results=max_results,
            page_token=page_token,
            include_spam_trash=include_spam_trash,
        )

    async def get_thread(self, thread_id: str) -> EmailThread:
        adapter = await self._get_adapter()
        return await adapter.get_thread(thread_id)

    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        adapter = await self._get_adapter()
        return await adapter.search_messages(
            sender=sender,
            recipient=recipient,
            subject=subject,
            has_attachment=has_attachment,
            is_unread=is_unread,
            in_folder=in_folder,
            after_date=after_date,
            before_date=before_date,
            query_text=query_text,
            max_results=max_results,
        )

    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        adapter = await self._get_adapter()
        return await adapter.modify_message(
            message_id=message_id,
            add_labels=add_labels,
            remove_labels=remove_labels,
        )

    async def list_labels(self) -> list[dict[str, Any]]:
        adapter = await self._get_adapter()
        return await adapter.list_labels()

    async def create_process_runner(
        self, crm_account_data: CRMAccountData | None = None
    ) -> EmailProcessRunner:
        adapter = await self._get_adapter()
        embedder = OpenAIEmbedder(api_key=config.openai_api_key)

        return EmailProcessRunner(
            email_adapter=adapter,
            embedder=embedder,
            tenant_id=self.context.tenant_id,
            crm_account_data=crm_account_data,
        )

    async def process_account_emails(
        self,
        crm_account_id: str,
        crm_account_data: CRMAccountData | None = None,
        lookback_days: int = 365,
        max_emails: int = 1000,
    ) -> dict[str, Any]:
        process_runner = await self.create_process_runner(
            crm_account_data=crm_account_data
        )
        return await process_runner.process_account_emails(
            crm_account_id=crm_account_id,
            lookback_days=lookback_days,
            max_emails=max_emails,
        )

    async def search_email_documents(
        self,
        query: str,
        limit: int = 10,
        crm_account_id: str | None = None,
    ) -> list[tuple[DocumentData, float]]:
        try:
            embedder = OpenAIEmbedder(api_key=config.openai_api_key)
            query_embedding = await embedder.embed_text(query)

            async with self.context.db_session_factory() as session:
                document_store = PostgresDocumentStore(
                    session=session,
                    tenant_id=self.context.tenant_id,
                    source=self.source,
                )

                # Filter by email source and optionally by CRM account
                tag_filter = ["email"]
                if crm_account_id:
                    tag_filter.append(f"crm_account_id:{crm_account_id}")

                results = await document_store.find_similar_documents(
                    embedding=query_embedding,
                    limit=limit,
                    tag_filter=tag_filter,
                )

                return results

        except Exception as e:
            logger.exception(f"Failed to search emails: {e}")
            raise ValueError(f"Failed to search emails: {str(e)}")
