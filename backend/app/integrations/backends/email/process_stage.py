import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.task_runner.base_task import BaseTask
from app.integrations.backends.email.process_runner import EmailProcessRunner
from app.integrations.models.email_processing_run import EmailProcessingRun
from app.integrations.types import IntegrationSource

logger = get_logger()


class EmailProcessStage(BaseTask):
    """
    Stage for processing emails from email providers.
    Tracking processing runs in a database.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session: AsyncSession,
        process_runner: EmailProcessRunner,
        crm_account_ids: list[str],
        lookback_days: int = 365,
        max_emails_per_account: int = 1000,
        interval_seconds: int = 3600,  # 1 hour default
        stage_id: str | None = None,
    ):
        """
        Initialize the email process stage.

        Args:
            tenant_id: The tenant (environment) ID
            source: Integration source type
            db_session: SQLAlchemy async session for tracking runs
            process_runner: The email process runner to use
            crm_account_ids: List of CRM account IDs to process emails for
            lookback_days: Days to look back for emails (default 365)
            max_emails_per_account: Maximum emails to process per account
            interval_seconds: The interval between processing runs in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            task_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.process_runner = process_runner
        self.crm_account_ids = crm_account_ids
        self.lookback_days = lookback_days
        self.max_emails_per_account = max_emails_per_account
        self.tenant_id = tenant_id
        self.source = source
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "accounts_processed": 0,
            "errors_count": 0,
            "total_emails_processed": 0,
            "total_emails_filtered": 0,
        }

    async def execute_once(self) -> dict:
        """
        Execute one cycle of email processing for the specified CRM accounts.

        Returns:
            Dictionary with execution results and metrics
        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "accounts_processed": 0,
            "accounts": {},
        }

        try:
            for account_id in self.crm_account_ids:
                try:
                    self.logger.info(f"Processing emails for CRM account {account_id}")

                    processing_result = (
                        await self.process_runner.process_account_emails(
                            crm_account_id=account_id,
                            lookback_days=self.lookback_days,
                            max_emails=self.max_emails_per_account,
                        )
                    )

                    results["accounts_processed"] += 1
                    self.metrics["accounts_processed"] += 1

                    if processing_result["status"] == EmailProcessingRun.Status.SUCCESS:
                        self.metrics["total_emails_processed"] += processing_result[
                            "emails_processed"
                        ]
                        self.metrics["total_emails_filtered"] += processing_result[
                            "emails_filtered"
                        ]

                    results["accounts"][account_id] = {
                        "status": processing_result["status"].value,
                        "emails_processed": processing_result["emails_processed"],
                        "emails_filtered": processing_result["emails_filtered"],
                        "run_duration": (
                            (
                                processing_result["run_end"]
                                - processing_result["run_start"]
                            ).total_seconds()
                            if processing_result["run_end"]
                            and processing_result["run_start"]
                            else None
                        ),
                    }

                    if processing_result["error_message"]:
                        results["accounts"][account_id]["error"] = processing_result[
                            "error_message"
                        ]

                    self.logger.info(
                        f"Successfully processed emails for CRM account {account_id}: "
                        f"{processing_result['emails_processed']} processed, "
                        f"{processing_result['emails_filtered']} filtered"
                    )

                except Exception as e:
                    error_msg = f"Error processing emails for CRM account {account_id}"
                    self.logger.exception(error_msg)

                    # If it's a database transaction error, try to rollback the session
                    if "InFailedSQLTransactionError" in str(
                        e
                    ) or "PendingRollbackError" in str(e):
                        try:
                            await self.db_session.rollback()
                            self.logger.info(
                                f"Rolled back database session after error for account {account_id}"
                            )
                        except Exception as rollback_error:
                            self.logger.exception(
                                f"Failed to rollback session: {rollback_error}"
                            )

                    results["accounts"][account_id] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # If any account failed, mark overall status as partial
            if any(
                account.get("status") == "error"
                for account in results["accounts"].values()
            ):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            self.logger.exception(f"Email processing stage failed: {self.task_id}")
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "source": str(self.source),
                "crm_account_ids": self.crm_account_ids,
                "lookback_days": self.lookback_days,
                "max_emails_per_account": self.max_emails_per_account,
                "interval_seconds": self.interval_seconds,
                "metrics": self.metrics,
            }
        )
        return status
