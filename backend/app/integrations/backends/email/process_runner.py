from datetime import datetime, timedelta
from uuid import UUID

from app.common.helpers.logger import get_logger
from app.core.database import AsyncSessionLocal
from app.integrations.base.email_adapter import BaseEmailAdapter
from app.integrations.base.embedder import <PERSON><PERSON>mbedder
from app.integrations.models import EmailProcessingRun
from app.integrations.processors.chunkers.recursive_chunker import RecursiveChunker
from app.integrations.processors.email.client_filter import EmailClientFilter
from app.integrations.processors.email.content_formatter import EmailContentFormatter
from app.integrations.processors.email.email_processor import EmailProcessor
from app.integrations.schemas import CRMAccountData
from app.integrations.stores.email_processing_run_store import EmailProcessingRunStore
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class EmailProcessRunner:
    def __init__(
        self,
        email_adapter: BaseEmailAdapter,
        embedder: <PERSON><PERSON><PERSON>dder,
        tenant_id: UUID,
        crm_account_data: CRMAccountData | None = None,
    ):
        self.email_adapter = email_adapter
        self.embedder = embedder
        self.tenant_id = tenant_id
        self.crm_account_data = crm_account_data

    async def process_account_emails(
        self,
        crm_account_id: str,
        lookback_days: int = 365,
        max_emails: int = 1000,
    ) -> dict:
        run_start = datetime.now()
        sync_window_start = run_start - timedelta(days=lookback_days)
        sync_window_end = run_start

        # Create fresh session for processing run management
        processing_run_id = None
        async with AsyncSessionLocal() as session:
            processing_run = EmailProcessingRun(
                tenant_id=self.tenant_id,
                source=self.email_adapter.source,
                crm_account_id=crm_account_id,
                status=EmailProcessingRun.Status.IN_PROGRESS,
                sync_window_start=sync_window_start,
                sync_window_end=sync_window_end,
                run_start=run_start,
            )

            session.add(processing_run)
            await session.commit()
            await session.refresh(processing_run)  # Ensure all attributes are loaded

            # Extract the ID while still in session context
            processing_run_id = processing_run.id

        # Use plain Python variables instead of SQLAlchemy attributes
        emails_processed = 0
        emails_filtered = 0
        final_status = EmailProcessingRun.Status.SUCCESS
        final_run_end = None
        error_message = None

        try:
            logger.info(f"Getting account emails for {crm_account_id}")

            # Create fresh session for client filter
            async with AsyncSessionLocal() as filter_session:
                client_filter = EmailClientFilter(
                    email_adapter=self.email_adapter,
                    db_session=filter_session,
                    tenant_id=str(self.tenant_id),
                    crm_account_data=self.crm_account_data,
                )

                async for email in client_filter.get_account_emails(
                    crm_account_id=crm_account_id,
                    since=sync_window_start,
                    max_results=max_emails,
                ):
                    logger.info(f"Processing email {email.id}")
                    emails_filtered += 1
                    try:
                        logger.info(f"Processing email 2 {email.id}")
                        success = await self._process_single_email(
                            email=email,
                            crm_account_id=crm_account_id,
                            processing_run_id=processing_run_id,
                        )

                        if success:
                            emails_processed += 1
                            logger.info(f"Successfully processed email {email.id}")
                        else:
                            logger.info(f"Email {email.id} - no processing needed")

                    except Exception as e:
                        print(f"Error processing email {email.id}: {e}")
                        logger.debug(f"Error processing email {email.id}: {e}")
                        continue

            final_run_end = datetime.now()

        except Exception as e:
            final_status = EmailProcessingRun.Status.FAILED
            error_message = str(e)[:1024]
            final_run_end = datetime.now()

        # Update the SQLAlchemy model with fresh session
        try:
            async with AsyncSessionLocal() as update_session:
                # Re-fetch the processing_run object with fresh session using store
                processing_store = EmailProcessingRunStore(update_session)
                processing_run = await processing_store.get_processing_run_by_id(
                    processing_run_id
                )

                # Update the processing run
                processing_run.emails_processed = emails_processed
                processing_run.emails_filtered = emails_filtered
                processing_run.status = final_status
                processing_run.run_end = final_run_end
                processing_run.error_message = error_message

                await update_session.commit()
        except Exception as commit_error:
            # If everything fails, at least log the error
            # Update our local variables to reflect the error
            final_status = EmailProcessingRun.Status.FAILED
            error_message = f"Database commit failed: {str(commit_error)[:500]}"

        # Return data using our plain Python variables, not SQLAlchemy attributes
        return {
            "status": final_status,
            "emails_processed": emails_processed,
            "emails_filtered": emails_filtered,
            "run_start": run_start,
            "run_end": final_run_end,
            "error_message": error_message,
        }

    async def process_single_email(
        self,
        email,
        crm_account_id: str,
        force_reprocess: bool = False,  # noqa: ARG002
    ) -> bool:
        return await self._process_single_email(
            email=email,
            crm_account_id=crm_account_id,
            processing_run_id=None,  # No processing run tracking for individual emails
        )

    async def _process_single_email(
        self,
        email,
        crm_account_id: str,
        processing_run_id: UUID | None,
    ) -> bool:
        try:
            # Create a fresh session context to avoid SQLAlchemy async context issues
            async with AsyncSessionLocal() as fresh_session:
                # Create a fresh document store for this email
                document_store = PostgresDocumentStore(
                    session=fresh_session,
                    tenant_id=self.tenant_id,
                    source=IntegrationSource.GMAIL,
                )

                # Create a fresh email processor for this email
                email_processor = EmailProcessor(
                    embedder=self.embedder,
                    document_store=document_store,
                    chunker=RecursiveChunker(chunk_size=2560, chunk_overlap=256),
                    formatter=EmailContentFormatter(),
                )

                # Process the email
                result = await email_processor.process_single_email(
                    email=email,
                    crm_account_id=crm_account_id,
                    processing_run_id=processing_run_id,
                )

                return result

        except Exception as e:
            logger.exception(f"Error processing email {email.id}: {e}")
            return False
