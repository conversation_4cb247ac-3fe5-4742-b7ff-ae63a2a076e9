from app.integrations.schemas import DocumentData


def format_email_documents(
    list_of_documents: list[tuple[DocumentData, float]],
) -> list[str]:
    return [
        f"Email from {extract_sender_from_tags(list(document.tags))}\n\n{document.content}..."
        for document, _score in list_of_documents
    ]


def extract_sender_from_tags(tags: list[str]) -> str:
    for tag in tags:
        if tag.startswith("sender_email:"):
            return tag.split(":", 1)[1]
    return "Unknown"
