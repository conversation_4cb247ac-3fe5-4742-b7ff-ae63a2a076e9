from app.common.helpers.logger import get_logger
from app.common.task_runner.runner import TaskRunner
from app.integrations.backends.messaging.channel_processor import (
    MessagingChannelProcessor,
)
from app.integrations.backends.messaging.document_formatter import (
    MessageDocumentFormatter,
)
from app.integrations.backends.messaging.process_stage import MessagingProcessStage
from app.integrations.context import IntegrationContext
from app.integrations.processors.embedders.noop_embedder import <PERSON>opEmbedder
from app.integrations.processors.indexers.message_context_doc_indexer import (
    MessageWithContextToDocumentIndexer,
)
from app.integrations.processors.readers.channel_changelog_reader import (
    ChannelChangelogReader,
)
from app.integrations.stores.pg_cursor_store import PostgresCursorStore
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.stores.pg_message_store import PostgresMessageStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class MessagingProcessRunner:
    """
    Runner for executing messaging data processing pipelines.

    This service handles the orchestration of data processing for
    messaging platforms, encapsulating the complexity of pipeline
    creation and execution.
    """

    def __init__(
        self,
        context: IntegrationContext,
        source: IntegrationSource,
    ):
        """
        Initialize the MessagingProcessRunner.

        Args:
            context: Integration context with tenant_id and db_session_factory
            source: Integration source type
        """
        self.context = context
        self.source = source
        self.tenant_id = context.tenant_id
        self.db_session_factory = context.db_session_factory

    async def run(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict:
        """
        Run message channel processing pipeline for the specified channels.

        Args:
            channel_ids: List of channel IDs to process
            interval_seconds: Interval between executions in seconds
            batch_size: Number of changes to process in one batch
            daemon_mode: Whether to run continuously in the background

        Returns:
            Results of the pipeline execution if not in daemon mode
            If daemon_mode is True, this method will block until the daemon is stopped,
            then return a status indicating the daemon has stopped.
        """
        logger.info(
            f"Starting {self.source.value} channel processing for tenant {self.tenant_id}"
        )

        # Create stores and stage with their own sessions
        async with (
            self.db_session_factory() as main_session,
            self.db_session_factory() as doc_session,
            self.db_session_factory() as stage_session,
        ):
            message_store = PostgresMessageStore(
                tenant_id=self.tenant_id,
                source=self.source,
                session=main_session,
            )
            cursor_store = PostgresCursorStore(
                tenant_id=self.tenant_id,
                session=main_session,
            )
            document_store = PostgresDocumentStore(
                tenant_id=self.tenant_id,
                session=doc_session,
                source=self.source,
            )

            changelog_reader = ChannelChangelogReader(
                message_store=message_store,
                cursor_store=cursor_store,
            )
            document_indexer = MessageWithContextToDocumentIndexer(
                message_store=message_store,
                document_store=document_store,
                document_formatter=MessageDocumentFormatter(),
                embedder=NoopEmbedder(),
            )

            # Create the generic messaging processor
            messaging_processor = MessagingChannelProcessor(
                channel_changelog_reader=changelog_reader,
                document_indexer=document_indexer,
                batch_size=batch_size,
                consumer_id=f"{self.source.value}_channel_processor",
            )

            # Create the messaging process stage
            messaging_stage = MessagingProcessStage(
                tenant_id=self.tenant_id,
                source=self.source,
                db_session=stage_session,
                processor=messaging_processor,
                channel_ids=channel_ids,
                interval_seconds=interval_seconds,
                stage_id=f"{self.source.value}_processor_for_{self.tenant_id}",
            )

            pipeline = TaskRunner()
            pipeline.add_task(messaging_stage)

            # Execute the pipeline
            if daemon_mode:
                logger.info("Starting channel processing pipeline in daemon mode")
                await pipeline.start_daemon(wait=True)
                return {"status": "daemon_stopped"}
            else:
                logger.info("Running channel processing pipeline once")
                results = await pipeline.run()
                logger.info("Channel processing pipeline execution completed")
                return results
