import datetime
import uuid

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.common.task_runner.base_task import BaseTask
from app.integrations.backends.file.file_processor import (
    FileProcessor,
)
from app.integrations.models.file_processing_run import FileProcessingRun
from app.integrations.types import IntegrationSource

logger = get_logger()


class FileProcessStage(BaseTask):
    """
    Stage for processing files from buckets.
    Tracking processing runs in a database.
    """

    def __init__(
        self,
        tenant_id: uuid.UUID,
        source: IntegrationSource,
        db_session: AsyncSession,
        processor: FileProcessor,
        bucket_names: list[str],
        interval_seconds: int = 10,
        stage_id: str | None = None,
    ):
        """
        Initialize the file process stage.

        Args:
            tenant_id: The tenant
            source: Integration source type
            db_session: SQLAlchemy async session for tracking runs
            processor: The file processor to use
            bucket_names: The names of the buckets to process
            interval_seconds: The interval between processing runs in seconds
            stage_id: Unique identifier for this stage
        """
        super().__init__(
            task_id=stage_id,
            interval_seconds=interval_seconds,
            logger=logger,
        )
        self.processor = processor
        self.bucket_names = bucket_names
        self.tenant_id = tenant_id
        self.source = source
        self.db_session = db_session
        self.metrics = {
            "total_runs": 0,
            "successful_runs": 0,
            "buckets_processed": 0,
            "errors_count": 0,
        }

    async def execute_once(self) -> dict:
        """
        Execute one cycle of file processing for the specified buckets.

        Returns:
            Dictionary with execution results and metrics

        """
        self.metrics["total_runs"] += 1
        results: dict = {
            "status": "success",
            "buckets_processed": 0,
            "buckets": {},
        }

        try:
            for bucket_name in self.bucket_names:
                run = FileProcessingRun(
                    tenant_id=self.tenant_id,
                    source=self.source,
                    bucket_name=bucket_name,
                    status=FileProcessingRun.Status.IN_PROGRESS,
                    run_start=datetime.datetime.now(datetime.UTC),
                )
                self.db_session.add(run)
                await self.db_session.commit()

                try:
                    self.logger.info(
                        f"Processing {self.source.value} bucket {bucket_name}"
                    )

                    bucket_result = await self.processor.process_files(bucket_name)

                    run.status = FileProcessingRun.Status.SUCCESS
                    run.files_processed = bucket_result.processed_files
                    run.deleted_documents = bucket_result.deleted_documents
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    results["buckets_processed"] += 1
                    self.metrics["buckets_processed"] += 1

                    results["buckets"][bucket_name] = {
                        "status": "success",
                        **bucket_result.model_dump(),
                    }

                    self.logger.info(
                        f"Successfully processed {self.source.value} bucket {bucket_name}"
                    )

                except Exception as e:
                    run.status = FileProcessingRun.Status.FAILED
                    error_msg = str(e)
                    run.error_message = error_msg[:1024]
                    run.run_end = datetime.datetime.now(datetime.UTC)
                    await self.db_session.commit()

                    error_msg = (
                        f"Error processing {self.source.value} bucket {bucket_name}"
                    )
                    self.logger.exception(error_msg)
                    results["buckets"][bucket_name] = {
                        "status": "error",
                        "error": str(e),
                    }
                    self.metrics["errors_count"] += 1

            # If any bucket failed, mark overall status as partial
            if any(
                bucket.get("status") == "error"
                for bucket in results["buckets"].values()
            ):
                results["status"] = "partial"
            else:
                self.metrics["successful_runs"] += 1

            return results

        except Exception as e:
            self.logger.exception(f"File processing stage failed: {self.task_id}")
            results["status"] = "error"
            results["error"] = str(e)
            return results

    def get_status(self) -> dict:
        """Get detailed status information including metrics."""
        status = super().get_status()
        status.update(
            {
                "tenant_id": str(self.tenant_id),
                "source": str(self.source),
                "bucket_names": self.bucket_names,
                "interval_seconds": self.interval_seconds,
                "metrics": self.metrics,
            }
        )
        return status
