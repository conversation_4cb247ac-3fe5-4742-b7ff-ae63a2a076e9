from typing import Any

from app.common.helpers.logger import get_logger
from app.core.config import config
from app.integrations.backends.file.process_runner import FileProcessRunner
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.base.file_backend import BaseFileBackend
from app.integrations.context import IntegrationContext
from app.integrations.processors.embedders.openai_embedder import OpenAIEmbedder
from app.integrations.schemas import DocumentData, FileData
from app.integrations.stores.pg_document_store import PostgresDocumentStore
from app.integrations.types import IntegrationSource

logger = get_logger()


class FileBackend(BaseFileBackend):
    def __init__(
        self,
        context: IntegrationContext,
        adapter_class: type[BaseFileAdapter],
        source: IntegrationSource,
        openai_api_key: str = config.openai_api_key,
    ):
        super().__init__(
            context=context,
            adapter_class=adapter_class,
            source=source,
        )
        self.process_runner = FileProcessRunner(
            context=context,
            source=source,
            adapter_class=adapter_class,
        )
        self.openai_api_key = openai_api_key

    async def get_adapter(self) -> BaseFileAdapter:
        if not hasattr(self, "_adapter"):
            credentials = await self._context.credentials_resolver.get_credentials(
                self.source
            )
            self._adapter = self._adapter_class(credentials=credentials)
        return self._adapter

    async def start_processing(
        self,
        bucket_names: list[str],
    ) -> dict[str, Any]:
        return await self.process_runner.run(
            bucket_names=bucket_names,
        )

    async def search_files(
        self,
        query: str,
        limit: int = 10,
    ) -> list[tuple[DocumentData, float]]:
        try:
            #  could be configurable in the future
            embedder = OpenAIEmbedder(api_key=self.openai_api_key)
            query_embedding = await embedder.embed_text(query)

            async with self.context.db_session_factory() as session:
                document_store = PostgresDocumentStore(
                    session=session,
                    tenant_id=self.context.tenant_id,
                    source=self.source,
                )

                result = await document_store.find_similar_documents(
                    embedding=query_embedding,
                    limit=limit,
                    tag_filter="file",
                )

                return result
        except Exception as e:
            logger.exception(f"Failed to search files: {e}")
            raise ValueError(f"Failed to search files: {str(e)}")

    async def list_files(
        self,
        container_name: str,
    ) -> list[FileData]:
        try:
            adapter = await self.get_adapter()
            return await adapter.list_files(container_name)
        except Exception as e:
            logger.exception(
                f"Failed to list files from container {container_name}: {e}"
            )
            raise ValueError(f"Failed to list files: {str(e)}")

    async def create_bucket(self, bucket_name: str) -> None:
        try:
            adapter = await self.get_adapter()
            await adapter.create_bucket(bucket_name)
        except Exception as e:
            logger.exception(f"Failed to create bucket {bucket_name}: {e}")
            raise ValueError(f"Failed to create bucket: {str(e)}")

    async def bucket_exists(self, bucket_name: str) -> bool:
        try:
            adapter = await self.get_adapter()
            return await adapter.bucket_exists(bucket_name)
        except Exception as e:
            logger.exception(f"Failed to check if bucket {bucket_name} exists: {e}")
            return False

    async def delete_file(self, container_name: str, file_name: str) -> None:
        try:
            adapter = await self.get_adapter()

            files = await adapter.list_files(container_name)
            file_to_delete = None
            for file in files:
                if file.name == file_name:
                    file_to_delete = file
                    break

            if file_to_delete:
                # Delete associated documents from PostgreSQL
                await self._delete_associated_documents(file_to_delete.md5_hash)

            # Delete the file from storage
            await adapter.delete_file(container_name, file_name)
        except Exception as e:
            logger.exception(
                f"Failed to delete file {file_name} from container {container_name}: {e}"
            )
            raise ValueError(f"Failed to delete file: {str(e)}")

    async def _delete_associated_documents(self, md5_hash: str) -> None:
        try:
            async with self.context.db_session_factory() as session:
                document_store = PostgresDocumentStore(
                    session=session,
                    tenant_id=self.context.tenant_id,
                    source=self.source,
                )

                md5_tag = f"md5_hash:{md5_hash}"
                document_ids = await document_store.find_document_ids_by_tag(md5_tag)

                for document_id in document_ids:
                    await document_store.delete_document(document_id)

                logger.info(
                    f"Deleted {len(document_ids)} documents associated with md5_hash: {md5_hash}"
                )
        except Exception as e:
            logger.exception(
                f"Failed to delete associated documents for md5_hash {md5_hash}: {e}"
            )

    async def upload_file(
        self,
        container_name: str,
        file_obj,
        file_name: str,
        content_type: str | None = None,
    ) -> None:
        try:
            adapter = await self.get_adapter()
            await adapter.upload_file(container_name, file_obj, file_name, content_type)
        except Exception as e:
            logger.exception(
                f"Failed to upload file {file_name} to container {container_name}: {e}"
            )
            raise ValueError(f"Failed to upload file: {str(e)}")
