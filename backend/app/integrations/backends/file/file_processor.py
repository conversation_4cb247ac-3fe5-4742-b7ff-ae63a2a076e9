from app.common.helpers.logger import get_logger
from app.integrations.base.chunker import <PERSON>Chunker
from app.integrations.base.document_store import IDocumentStore
from app.integrations.base.embedder import IEmbedder
from app.integrations.base.file_adapter import BaseFileAdapter
from app.integrations.base.parser import IParser
from app.integrations.schemas import (
    DocumentData,
    FileProcessingResult,
)
from app.integrations.types import ExtensionType, IntegrationSource

logger = get_logger()


class FileProcessor:
    """
    Generic processor for files.

    This class handles:
    - The detection of files that need to be processed
    - The processing of files (parsing, chunking, embedding)
    """

    def __init__(
        self,
        document_store: IDocumentStore,
        source: IntegrationSource,
        adapter: BaseFileAdapter,
        parser: IParser,
        chunker: IChunker,
        embedder: IEmbedder,
    ):
        self.document_store = document_store
        self.source = source
        self.adapter = adapter
        self.parser = parser
        self.chunker = chunker
        self.embedder = embedder

    async def process_files(self, bucket_name: str) -> FileProcessingResult:
        try:
            result = FileProcessingResult(processed_files=0, deleted_documents=0)

            files = await self.adapter.list_files(bucket_name)

            for file in files:
                # TODO: Handle file not fully processed (e.g. crash during processing)

                # Do not process files that have already been processed
                md5_tag = f"md5_hash:{file.md5_hash}"
                if await self.document_store.find_document_ids_by_tag(md5_tag):
                    continue

                file_data = await self.adapter.download_file(bucket_name, file.name)
                file_extension = ExtensionType(file.name.split(".")[-1].lower())
                file_content = await self.parser.parse(
                    file_data, file_extension, file.content_type
                )

                # TODO: Chunker to return an Iterator
                for index, chunk in enumerate(self.chunker.chunk(file_content)):
                    if not chunk.strip():
                        continue

                    embedding = await self.embedder.embed_text(chunk)

                    tags: set[str] = {
                        md5_tag,
                        f"chunk_index:{index}",
                        "file",
                    }

                    document = DocumentData(
                        id=f"{file.id}_chunk_{index}",
                        content=chunk,
                        source_timestamp=file.last_modified,
                        tags=tags,
                    )

                    await self.document_store.store_document(document, embedding)

                result.processed_files += 1

            return result

        except Exception as e:
            logger.exception(f"Error processing files from bucket {bucket_name}")
            raise RuntimeError(f"Failed to process files: {str(e)}") from e
