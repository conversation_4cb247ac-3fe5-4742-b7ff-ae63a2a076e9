from abc import ABC, abstractmethod
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.schemas import DocumentData
from app.integrations.types import BackendType


class BaseMessagingBackend(BaseBackend, ABC):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.MESSAGING

    @abstractmethod
    async def search_channel_messages(
        self, channel_id: str, query: str, limit: int = 10
    ) -> list[tuple[DocumentData, float]]:
        pass

    @abstractmethod
    async def start_channel_ingestion(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        lookback_days: int = 7,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def start_channel_processing(
        self,
        channel_ids: list[str],
        interval_seconds: int = 300,
        batch_size: int = 100,
        daemon_mode: bool = False,
    ) -> dict[str, Any]:
        pass
