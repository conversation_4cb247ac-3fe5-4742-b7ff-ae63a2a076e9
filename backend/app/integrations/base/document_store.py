from abc import ABC, abstractmethod

from app.integrations.schemas import DocumentData


class IDocumentStore(ABC):
    @abstractmethod
    async def store_document(
        self, document: DocumentData, embedding: list[float] | None = None
    ) -> None:
        """
        Stores a document with optional embedding vector
        """
        pass

    @abstractmethod
    async def get_document(self, document_id: str) -> DocumentData | None:
        """
        Gets a document by its id
        """
        pass

    @abstractmethod
    async def delete_document(self, document_id: str) -> None:
        """
        Deletes a document by its id
        """
        pass

    @abstractmethod
    async def find_document_ids_by_tag(self, tag: str) -> set[str]:
        """
        Finds all document ids flagged with the given tag
        """
        pass

    @abstractmethod
    async def find_similar_documents(
        self,
        embedding: list[float],
        limit: int = 10,
        tag_filter: str | list[str] | None = None,
    ) -> list[tuple[DocumentData, float]]:
        """
        Find documents with similar embeddings
        Returns list of tuples with (DocumentData, similarity_score)
        """
        pass
