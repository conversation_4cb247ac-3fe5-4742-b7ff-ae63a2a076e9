from abc import abstractmethod
from datetime import datetime
from typing import Any

from app.integrations.base.backend import BaseBackend
from app.integrations.schemas import DocumentData, EmailMessage, EmailThread
from app.integrations.types import BackendType


class BaseEmailBackend(BaseBackend):
    @property
    def backend_type(self) -> BackendType:
        return BackendType.EMAIL

    @abstractmethod
    async def get_user_info(self) -> dict[str, Any]:
        """Get user profile information."""
        pass

    @abstractmethod
    async def list_messages(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailMessage]:
        """List messages matching the query."""
        pass

    @abstractmethod
    async def get_message(self, message_id: str) -> EmailMessage:
        """Get a specific message by ID."""
        pass

    @abstractmethod
    async def list_threads(
        self,
        query: str = "",
        max_results: int = 100,
        page_token: str | None = None,
        include_spam_trash: bool = False,
    ) -> list[EmailThread]:
        """List threads matching the query."""
        pass

    @abstractmethod
    async def get_thread(self, thread_id: str) -> EmailThread:
        """Get a specific thread by ID."""
        pass

    @abstractmethod
    async def search_messages(
        self,
        sender: str | None = None,
        recipient: str | None = None,
        subject: str | None = None,
        has_attachment: bool | None = None,
        is_unread: bool | None = None,
        in_folder: str | None = None,
        after_date: datetime | None = None,
        before_date: datetime | None = None,
        query_text: str | None = None,
        max_results: int = 100,
    ) -> list[EmailMessage]:
        """Search messages with various criteria."""
        pass

    @abstractmethod
    async def search_email_documents(
        self,
        query: str,
        limit: int = 10,
        crm_account_id: str | None = None,
    ) -> list[tuple[DocumentData, float]]:
        """Search emails using semantic similarity."""
        pass

    @abstractmethod
    async def modify_message(
        self,
        message_id: str,
        add_labels: list[str] | None = None,
        remove_labels: list[str] | None = None,
    ) -> dict[str, Any]:
        """Modify labels on a message."""
        pass

    @abstractmethod
    async def list_labels(self) -> list[dict[str, Any]]:
        """List all labels."""
        pass
