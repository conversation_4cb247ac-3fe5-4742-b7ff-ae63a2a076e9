from abc import ABC, abstractmethod

from app.integrations.types import ExtensionType


class IParser(ABC):
    @abstractmethod
    async def parse(
        self, file_data: bytes, file_extension: ExtensionType, content_type: str
    ) -> str:
        """
        Parses a file and returns the content.

        Args:
            file_data: The file data
            file_extension: The file extension
            content_type: The file content type
        """
        pass
