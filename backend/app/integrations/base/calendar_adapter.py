from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any

from app.integrations.base.adapter import BaseAdapter


class BaseCalendarAdapter(BaseAdapter, ABC):
    @abstractmethod
    async def list_calendars(self) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_calendar(self, calendar_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def list_events(
        self,
        calendar_id: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime",
        show_deleted: bool = False,
        page_token: str | None = None,
    ) -> dict[str, Any]:
        pass

    @abstractmethod
    async def get_event(self, calendar_id: str, event_id: str) -> dict[str, Any]:
        pass

    @abstractmethod
    async def search_events(
        self,
        calendar_id: str,
        query: str,
        start_time: datetime | None = None,
        end_time: datetime | None = None,
        max_results: int = 250,
        order_by: str = "startTime",
    ) -> list[dict[str, Any]]:
        pass

    @abstractmethod
    async def get_user_info(self) -> dict[str, Any]:
        pass
