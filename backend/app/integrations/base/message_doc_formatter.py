from abc import ABC, abstractmethod

from app.integrations.schemas import MessageData


class IMessageDocumentFormatter(ABC):
    @abstractmethod
    def format_document_content(
        self,
        main_message: MessageData,
        context_messages: list[MessageData] | None = None,
        replies: list[MessageData] | None = None,
    ) -> str:
        """
        Formats document content from message and its context
        """
        pass
