from enum import Enum, unique
from typing import TypeVar


@unique
class GoogleScope(str, Enum):
    # Gmail
    GMAIL_READONLY = "https://www.googleapis.com/auth/gmail.readonly"

    # Calendar
    CALENDAR_READONLY = "https://www.googleapis.com/auth/calendar.readonly"

    # Profile
    PROFILE = "https://www.googleapis.com/auth/userinfo.profile"
    EMAIL = "https://www.googleapis.com/auth/userinfo.email"

    @property
    def description(self) -> str:
        return _GOOGLE_DESCRIPTIONS[self]


_GOOGLE_DESCRIPTIONS: dict[GoogleScope, str] = {
    GoogleScope.GMAIL_READONLY: "Read e-mail metadata and content",
    GoogleScope.CALENDAR_READONLY: "Read calendars and events",
    GoogleScope.PROFILE: "Read basic profile information",
    GoogleScope.EMAIL: "Read primary e-mail address",
}


@unique
class SalesforceScope(str, Enum):
    API = "api"
    REFRESH_TOKEN = "refresh_token"

    @property
    def description(self) -> str:
        return _SALESFORCE_DESCRIPTIONS[self]


_SALESFORCE_DESCRIPTIONS: dict[SalesforceScope, str] = {
    SalesforceScope.API: "Access REST API",
    SalesforceScope.REFRESH_TOKEN: "Obtain refresh tokens",
}


@unique
class HubSpotScope(str, Enum):
    CONTACTS_READ = "crm.objects.contacts.read"
    COMPANIES_READ = "crm.objects.companies.read"
    DEALS_READ = "crm.objects.deals.read"
    LEADS_READ = "crm.objects.leads.read"
    CUSTOM_READ = "crm.objects.custom.read"
    USERS_READ = "crm.objects.users.read"
    GOALS_READ = "crm.objects.goals.read"

    @property
    def description(self) -> str:
        return _HUBSPOT_DESCRIPTIONS[self]


_HUBSPOT_DESCRIPTIONS: dict[HubSpotScope, str] = {
    HubSpotScope.CONTACTS_READ: "Read contacts",
    HubSpotScope.COMPANIES_READ: "Read companies",
    HubSpotScope.DEALS_READ: "Read deals",
    HubSpotScope.LEADS_READ: "Read leads",
    HubSpotScope.CUSTOM_READ: "Read custom objects",
    HubSpotScope.USERS_READ: "Read users",
    HubSpotScope.GOALS_READ: "Read goals",
}


T = TypeVar("T", bound=Enum)


class _BaseScopeBuilder:
    def __init__(self) -> None:
        self._scopes: set[Enum] = set()

    def to_list(self) -> list[str]:
        return sorted(scope.value for scope in self._scopes)

    def to_string(self) -> str:
        return " ".join(self.to_list())

    def _add(self, *scopes: Enum) -> None:
        self._scopes.update(scopes)


class GoogleScopeBuilder(_BaseScopeBuilder):
    def with_profile(self) -> "GoogleScopeBuilder":
        self._add(GoogleScope.PROFILE, GoogleScope.EMAIL)
        return self

    def with_gmail(self, readonly: bool = True) -> "GoogleScopeBuilder":
        if readonly:
            if GoogleScope.GMAIL_READONLY not in self._scopes:
                self._add(GoogleScope.GMAIL_READONLY)
        return self

    def with_calendar(self, readonly: bool = True) -> "GoogleScopeBuilder":
        if readonly:
            if GoogleScope.CALENDAR_READONLY not in self._scopes:
                self._add(GoogleScope.CALENDAR_READONLY)
        return self


class SalesforceScopeBuilder(_BaseScopeBuilder):
    def with_api(self) -> "SalesforceScopeBuilder":
        self._add(SalesforceScope.API)
        return self

    def with_refresh_token(self) -> "SalesforceScopeBuilder":
        self._add(SalesforceScope.REFRESH_TOKEN)
        return self


class HubSpotScopeBuilder(_BaseScopeBuilder):
    def with_contacts(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.CONTACTS_READ)
        return self

    def with_companies(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.COMPANIES_READ)
        return self

    def with_deals(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.DEALS_READ)
        return self

    def with_leads(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.LEADS_READ)
        return self

    def with_custom(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.CUSTOM_READ)
        return self

    def with_users(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.USERS_READ)
        return self

    def with_goals(self) -> "HubSpotScopeBuilder":
        self._add(HubSpotScope.GOALS_READ)
        return self
