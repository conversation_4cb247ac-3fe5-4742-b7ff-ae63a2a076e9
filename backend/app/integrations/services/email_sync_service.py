import logging
from uuid import <PERSON><PERSON><PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import As<PERSON><PERSON><PERSON><PERSON><PERSON>oc<PERSON>
from app.integrations.models.email_processing_run import EmailProcessingRun
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource
from app.workspace.integrations.user_integrations import create_user_integrations
from app.workspace.repositories.integration_config import IntegrationConfigRepository
from app.workspace.types import IntegrationType

logger = logging.getLogger(__name__)


class EmailSyncService:
    """Service to provide email synchronization methods for event-driven triggers."""

    def __init__(self):
        pass

    async def sync_all_accounts(
        self, tenant_id: UUID, lookback_days: int = 365
    ) -> dict:
        logger.info(
            f"Starting email sync for tenant {tenant_id} with lookback_days={lookback_days}"
        )
        try:
            async with AsyncSessionLocal() as session:
                # Get all CRM accounts for this tenant
                account_ids = await self._get_tenant_crm_accounts(session, tenant_id)
                logger.info(
                    f"Found {len(account_ids)} CRM accounts for tenant {tenant_id}: {account_ids}"
                )

                if not account_ids:
                    logger.warning(
                        f"No CRM accounts found for tenant {tenant_id} - email sync cannot proceed"
                    )
                    return {"status": "no_accounts", "accounts_processed": 0}

                # Get user with email integration for this tenant
                user_id, environment_id = await self._get_tenant_email_user(
                    session, tenant_id
                )
                if not user_id or not environment_id:
                    logger.info(f"No email integration found for tenant {tenant_id}")
                    return {"status": "no_email_integration", "accounts_processed": 0}

                # Create email backend
                user_integrations = await create_user_integrations(
                    user_id=user_id,
                    environment_id=environment_id,
                    db_session=session,
                )

                email_backend = await user_integrations.email()
                if not email_backend:
                    logger.info(f"No email backend available for tenant {tenant_id}")
                    return {"status": "no_email_backend", "accounts_processed": 0}

                # Get CRM integration
                crm = await user_integrations.crm()

                if not crm:
                    logger.info(f"No CRM integration available for tenant {tenant_id}")
                    return {"status": "no_crm_integration", "accounts_processed": 0}

                # Get email handle for proper CRM integration
                email_handle = await user_integrations.email()
                if not email_handle:
                    logger.info(f"No email handle available for tenant {tenant_id}")
                    return {"status": "no_email_handle", "accounts_processed": 0}

                # Process all accounts with CRM integration using the handle
                results = await email_handle.process_all_accounts(
                    account_ids=account_ids,
                    crm_handle=crm,
                    lookback_days=lookback_days,
                    max_emails_per_account=1000,
                )

                accounts_processed = len(
                    [
                        r
                        for r in results
                        if r.get("status") == EmailProcessingRun.Status.SUCCESS
                    ]
                )
                result = {
                    "status": "success",
                    "accounts_processed": accounts_processed,
                    "total_accounts": len(account_ids),
                    "results": results,
                }
                logger.info(
                    f"Initial email sync completed for tenant {tenant_id}: {result}"
                )
                return result

        except Exception as e:
            logger.exception(f"Error in initial email sync for tenant {tenant_id}: {e}")
            return {"status": "error", "error": str(e), "accounts_processed": 0}

    async def sync_single_account(
        self, tenant_id: UUID, crm_account_id: str, lookback_days: int = 7
    ) -> dict:
        try:
            async with AsyncSessionLocal() as session:
                # Get user with email integration for this tenant
                user_id, environment_id = await self._get_tenant_email_user(
                    session, tenant_id
                )
                if not user_id or not environment_id:
                    logger.info(f"No email integration found for tenant {tenant_id}")
                    return {"status": "no_email_integration", "accounts_processed": 0}

                # Create email backend
                user_integrations = await create_user_integrations(
                    user_id=user_id,
                    environment_id=environment_id,
                    db_session=session,
                )

                email_backend = await user_integrations.email()
                if not email_backend:
                    logger.info(f"No email backend available for tenant {tenant_id}")
                    return {"status": "no_email_backend", "accounts_processed": 0}

                # Get CRM integration
                crm = await user_integrations.crm()

                if not crm:
                    logger.info(f"No CRM integration available for tenant {tenant_id}")
                    return {"status": "no_crm_integration", "accounts_processed": 0}

                # Get email handle for proper CRM integration
                email_handle = await user_integrations.email()
                if not email_handle:
                    logger.info(f"No email handle available for tenant {tenant_id}")
                    return {"status": "no_email_handle", "accounts_processed": 0}

                # Process single account with CRM integration using the handle
                processing_result = await email_handle.process_account_emails(
                    crm_account_id=crm_account_id,
                    crm_handle=crm,
                    lookback_days=lookback_days,
                    max_emails=100,  # Smaller limit for incremental
                )

                result = {
                    "status": "success"
                    if processing_result.get("status")
                    == EmailProcessingRun.Status.SUCCESS
                    else "error",
                    "accounts_processed": 1
                    if processing_result.get("status")
                    == EmailProcessingRun.Status.SUCCESS
                    else 0,
                    "crm_account_id": crm_account_id,
                    "processing_result": processing_result,
                }
                logger.info(
                    f"Incremental email sync completed for account {crm_account_id}: {result}"
                )
                return result

        except Exception as e:
            logger.exception(
                f"Error in incremental email sync for account {crm_account_id}: {e}"
            )
            return {"status": "error", "error": str(e), "accounts_processed": 0}

    async def _get_tenant_crm_accounts(
        self, session: AsyncSession, tenant_id: UUID
    ) -> list[str]:
        logger.info(f"Querying CRMAccountAccess table for tenant_id={tenant_id}")

        # First get user with email integration for this tenant
        user_id, environment_id = await self._get_tenant_email_user(session, tenant_id)
        if not user_id or not environment_id:
            logger.warning(f"No email integration user found for tenant {tenant_id}")
            return []

        # Determine which CRM integration is active for this tenant
        crm_source = await self._get_active_crm_source(session, user_id, environment_id)
        if not crm_source:
            logger.warning(f"No active CRM integration found for tenant {tenant_id}")
            return []

        # Use CRM store to get account IDs
        crm_store = PostgresCRMStore(
            tenant_id=tenant_id,
            source=crm_source,
            session=session,
        )
        account_ids = await crm_store.get_account_ids_for_tenant()

        logger.info(
            f"CRMAccountAccess query returned {len(account_ids)} accounts for tenant {tenant_id} (source: {crm_source})"
        )

        return account_ids

    async def _get_active_crm_source(
        self, session: AsyncSession, user_id: UUID, environment_id: UUID
    ) -> IntegrationSource | None:
        # Use the existing user_integrations pattern to determine active CRM
        user_integrations = await create_user_integrations(
            user_id=user_id,
            environment_id=environment_id,
            db_session=session,
        )

        # Check if CRM integration exists and get its source
        crm_handle = await user_integrations.crm()
        if not crm_handle:
            return None

        # Get the CRM config to determine the source
        crm_config = await user_integrations._get_config(IntegrationType.CRM)
        if crm_config:
            return crm_config.source

        return None

    async def _get_tenant_email_user(
        self, session: AsyncSession, tenant_id: UUID
    ) -> tuple[UUID | None, UUID | None]:
        logger.info(f"Looking for email integration user for tenant {tenant_id}")

        # Use integration repository to get email integration user
        integration_repo = IntegrationConfigRepository(session)
        (
            user_id,
            environment_id,
        ) = await integration_repo.get_email_integration_user_for_tenant(tenant_id)

        logger.info(
            f"Email integration user query result for tenant {tenant_id}: ({user_id}, {environment_id})"
        )

        if user_id and environment_id:
            logger.info(
                f"Found email integration user for tenant {tenant_id}: user_id={user_id}, environment_id={environment_id}"
            )
            return user_id, environment_id

        logger.warning(f"No email integration user found for tenant {tenant_id}")
        return None, None


# Global instance
email_sync_service = EmailSyncService()
