from typing import Any

from app.integrations.schemas import CRMAccount, CRMContact


def convert_to_account(raw_data: dict[str, Any]) -> CRMAccount:
    # Handle both Salesforce and HubSpot formats
    if "properties" in raw_data:  # HubSpot format
        props = raw_data["properties"]
        return CRMAccount(
            id=raw_data["id"],
            name=props.get("name", ""),
            website=props.get("website"),
            industry=props.get("industry"),
            annual_revenue=_parse_number(props.get("annualrevenue")),
            employee_count=_parse_int(props.get("numberofemployees")),
            phone=props.get("phone"),
            owner_id=props.get("hubspot_owner_id"),
            domain=props.get("domain"),
        )
    else:  # Salesforce format
        return CRMAccount(
            id=raw_data.get("Id", ""),
            name=raw_data.get("Name", ""),
            website=raw_data.get("Website"),
            industry=raw_data.get("Industry"),
            annual_revenue=raw_data.get("AnnualRevenue"),
            employee_count=raw_data.get("NumberOfEmployees"),
            phone=raw_data.get("Phone"),
            owner_id=raw_data.get("OwnerId"),
            account_type=raw_data.get("Type"),
            instance_url=raw_data.get("instance_url"),
        )


def convert_to_contact(raw_data: dict[str, Any]) -> CRMContact:
    # Handle both Salesforce and HubSpot formats
    if "properties" in raw_data:  # HubSpot format
        props = raw_data["properties"]
        return CRMContact(
            id=raw_data["id"],
            first_name=props.get("firstname"),
            last_name=props.get("lastname"),
            email=props.get("email"),
            phone=props.get("phone"),
        )
    else:  # Salesforce format
        return CRMContact(
            id=raw_data.get("Id", ""),
            first_name=raw_data.get("FirstName"),
            last_name=raw_data.get("LastName"),
            name=raw_data.get("Name"),
            email=raw_data.get("Email"),
            phone=raw_data.get("Phone"),
            account_id=raw_data.get("AccountId"),
        )


def _parse_number(value: Any) -> int | float | None:
    if value is None or value == "":
        return None
    try:
        # Try int first, then float
        if isinstance(value, int | float):
            return value
        if isinstance(value, str):
            if "." in value:
                return float(value)
            return int(value)
        return None
    except (ValueError, TypeError):
        return None


def _parse_int(value: Any) -> int | None:
    if value is None or value == "":
        return None
    try:
        if isinstance(value, int):
            return value
        if isinstance(value, str | float):
            return int(float(value))
        return None
    except (ValueError, TypeError):
        return None
