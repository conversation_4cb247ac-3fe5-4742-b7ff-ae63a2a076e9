import uuid
from datetime import datetime
from io import BytesIO

import pytest

from app.integrations.backends.file.backend import FileBackend
from app.integrations.backends.file.process_runner import <PERSON><PERSON>rocessRunner
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import IntegrationContext
from app.integrations.schemas import FileData
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_context(mocker):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()

    mock_session = mocker.Mock()
    mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
    mock_session.__aexit__ = mocker.AsyncMock(return_value=None)

    mock_context.db_session_factory = mocker.Mock(return_value=mock_session)
    mock_context.credentials_resolver = mocker.Mock(spec=ICredentialsResolver)
    return mock_context


@pytest.fixture
def mock_adapter_class(mocker):
    mock_class = mocker.Mock()
    return mock_class


@pytest.fixture
def file_backend(mock_context, mock_adapter_class):
    return FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )


def test_file_backend_initialization(mock_context, mock_adapter_class):
    backend = FileBackend(
        context=mock_context,
        adapter_class=mock_adapter_class,
        source=IntegrationSource.GCS,
    )

    assert backend._context == mock_context
    assert backend._adapter_class == mock_adapter_class
    assert backend.source == IntegrationSource.GCS
    assert backend.tenant_id == mock_context.tenant_id

    assert isinstance(backend.process_runner, FileProcessRunner)


@pytest.mark.anyio
async def test_start_processing(file_backend, mocker):
    mock_process_runner = mocker.Mock(spec=FileProcessRunner)
    mock_process_runner.run = mocker.AsyncMock(return_value={"status": "success"})
    file_backend.process_runner = mock_process_runner

    bucket_names = ["test-bucket-1", "test-bucket-2"]

    result = await file_backend.start_processing(bucket_names=bucket_names)

    mock_process_runner.run.assert_called_once_with(
        bucket_names=bucket_names,
    )

    assert result == {"status": "success"}


@pytest.mark.anyio
async def test_search_files(file_backend, mocker):
    query = "test query"
    expected_result = [("doc1", 0.9), ("doc2", 0.8)]
    file_backend.search_files = mocker.AsyncMock(return_value=expected_result)

    result = await file_backend.search_files(query, limit=5)

    file_backend.search_files.assert_called_once_with(query, limit=5)
    assert result == expected_result


@pytest.mark.anyio
async def test_list_files(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    expected_files = [
        FileData(
            id="file1",
            name="document.pdf",
            size=1024,
            time_created=datetime(2023, 1, 1),
            last_modified=datetime(2023, 1, 2),
            md5_hash="hash1",
            content_type="application/pdf",
        )
    ]
    mock_adapter.list_files.return_value = expected_files
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    result = await file_backend.list_files("test-container")

    mock_adapter.list_files.assert_called_once_with("test-container")
    assert result == expected_files


@pytest.mark.anyio
async def test_list_files_error(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.list_files.side_effect = Exception("List failed")
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    with pytest.raises(ValueError, match="Failed to list files"):
        await file_backend.list_files("test-container")


@pytest.mark.anyio
async def test_create_bucket(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    await file_backend.create_bucket("test-bucket")

    mock_adapter.create_bucket.assert_called_once_with("test-bucket")


@pytest.mark.anyio
async def test_create_bucket_error(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.create_bucket.side_effect = Exception("Creation failed")
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    with pytest.raises(ValueError, match="Failed to create bucket"):
        await file_backend.create_bucket("test-bucket")


@pytest.mark.anyio
async def test_bucket_exists_true(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.bucket_exists.return_value = True
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    result = await file_backend.bucket_exists("existing-bucket")

    mock_adapter.bucket_exists.assert_called_once_with("existing-bucket")
    assert result is True


@pytest.mark.anyio
async def test_bucket_exists_false(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.bucket_exists.return_value = False
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    result = await file_backend.bucket_exists("nonexistent-bucket")

    mock_adapter.bucket_exists.assert_called_once_with("nonexistent-bucket")
    assert result is False


@pytest.mark.anyio
async def test_bucket_exists_error(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.bucket_exists.side_effect = Exception("Check failed")
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    result = await file_backend.bucket_exists("test-bucket")

    assert result is False


@pytest.mark.anyio
async def test_delete_file_success(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()

    mock_file = FileData(
        id="file1",
        name="test.pdf",
        size=1024,
        time_created=datetime(2023, 1, 1),
        last_modified=datetime(2023, 1, 2),
        md5_hash="test-hash",
        content_type="application/pdf",
    )
    mock_adapter.list_files.return_value = [mock_file]
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    mock_delete_docs = mocker.patch.object(file_backend, "_delete_associated_documents")

    await file_backend.delete_file("test-container", "test.pdf")

    mock_adapter.list_files.assert_called_once_with("test-container")
    mock_delete_docs.assert_called_once_with("test-hash")
    mock_adapter.delete_file.assert_called_once_with("test-container", "test.pdf")


@pytest.mark.anyio
async def test_delete_file_not_found(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.list_files.return_value = []
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    mock_delete_docs = mocker.patch.object(file_backend, "_delete_associated_documents")

    await file_backend.delete_file("test-container", "nonexistent.pdf")

    mock_adapter.list_files.assert_called_once_with("test-container")
    mock_delete_docs.assert_not_called()
    mock_adapter.delete_file.assert_called_once_with(
        "test-container", "nonexistent.pdf"
    )


@pytest.mark.anyio
async def test_delete_file_error(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.delete_file.side_effect = Exception("Delete failed")
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    with pytest.raises(ValueError, match="Failed to delete file"):
        await file_backend.delete_file("test-container", "test.pdf")


@pytest.mark.anyio
async def test_upload_file(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    file_obj = BytesIO(b"test content")
    await file_backend.upload_file("test-container", file_obj, "test.pdf")

    mock_adapter.upload_file.assert_called_once_with(
        "test-container", file_obj, "test.pdf", None
    )


@pytest.mark.anyio
async def test_upload_file_error(file_backend, mocker):
    mock_adapter = mocker.AsyncMock()
    mock_adapter.upload_file.side_effect = Exception("Upload failed")
    file_backend.get_adapter = mocker.AsyncMock(return_value=mock_adapter)

    file_obj = BytesIO(b"test content")
    with pytest.raises(ValueError, match="Failed to upload file"):
        await file_backend.upload_file("test-container", file_obj, "test.pdf")


@pytest.mark.anyio
async def test_delete_associated_documents(file_backend, mocker):
    mock_session = mocker.AsyncMock()
    mock_document_store = mocker.Mock()
    mock_document_store.find_document_ids_by_tag = mocker.AsyncMock(
        return_value=["doc1", "doc2"]
    )
    mock_document_store.delete_document = mocker.AsyncMock()

    mock_postgres_store = mocker.patch(
        "app.integrations.backends.file.backend.PostgresDocumentStore",
        return_value=mock_document_store,
    )

    file_backend._context.db_session_factory.return_value.__aenter__.return_value = (
        mock_session
    )

    await file_backend._delete_associated_documents("test-hash")

    mock_postgres_store.assert_called_once_with(
        session=mock_session,
        tenant_id=file_backend._context.tenant_id,
        source=file_backend.source,
    )

    mock_document_store.find_document_ids_by_tag.assert_called_once_with(
        "md5_hash:test-hash"
    )
    assert mock_document_store.delete_document.call_count == 2
    mock_document_store.delete_document.assert_any_call("doc1")
    mock_document_store.delete_document.assert_any_call("doc2")
