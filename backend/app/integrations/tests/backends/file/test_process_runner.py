import uuid

import pytest

from app.common.task_runner.runner import <PERSON>Runner
from app.integrations.backends.file.file_processor import FileProcessor
from app.integrations.backends.file.process_runner import FileProcessRunner
from app.integrations.backends.file.process_stage import FileProcessStage
from app.integrations.base.credentials_resolver import (
    ICredentials,
    ICredentialsResolver,
)
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {"gcs_credentials": "test-credentials"}
    return mock_creds


@pytest.fixture
def mock_credentials_resolver(mocker, mock_credentials):
    mock_resolver = mocker.Mock(spec=ICredentialsResolver)
    mock_resolver.get_credentials = mocker.AsyncMock(return_value=mock_credentials)
    return mock_resolver


@pytest.fixture
def mock_context(mocker, mock_credentials_resolver):
    mock_context = mocker.Mock(spec=IntegrationContext)
    mock_context.tenant_id = uuid.uuid4()

    def create_mock_session():
        mock_session = mocker.Mock()
        mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = mocker.AsyncMock(return_value=None)
        return mock_session

    mock_context.db_session_factory = create_mock_session
    mock_context.credentials_resolver = mock_credentials_resolver
    return mock_context


@pytest.fixture
def mock_adapter_class(mocker):
    mock_class = mocker.Mock()
    return mock_class


@pytest.fixture
def process_runner(mock_context, mock_adapter_class):
    return FileProcessRunner(
        context=mock_context,
        source=IntegrationSource.GCS,
        adapter_class=mock_adapter_class,
    )


def test_init(mock_context, mock_adapter_class):
    runner = FileProcessRunner(
        context=mock_context,
        source=IntegrationSource.GCS,
        adapter_class=mock_adapter_class,
    )

    assert runner.context == mock_context
    assert runner.source == IntegrationSource.GCS
    assert runner.tenant_id == mock_context.tenant_id
    assert runner.db_session_factory == mock_context.db_session_factory
    assert runner.adapter_class == mock_adapter_class


@pytest.mark.anyio
async def test_get_adapter(process_runner, mock_credentials):
    adapter = await process_runner.get_adapter()

    process_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.GCS
    )

    process_runner.adapter_class.assert_called_once_with(credentials=mock_credentials)

    assert adapter == process_runner.adapter_class.return_value


@pytest.mark.anyio
async def test_run(mocker, process_runner):
    mock_pipeline = mocker.Mock(spec=TaskRunner)
    mock_pipeline.run = mocker.AsyncMock(
        return_value={"status": "success", "details": "test results"}
    )
    mocker.patch(
        "app.integrations.backends.file.process_runner.TaskRunner",
        return_value=mock_pipeline,
    )

    mock_document_store_class = mocker.patch(
        "app.integrations.backends.file.process_runner.PostgresDocumentStore"
    )

    mock_file_processor = mocker.Mock(spec=FileProcessor)
    mocker.patch(
        "app.integrations.backends.file.process_runner.FileProcessor",
        return_value=mock_file_processor,
    )

    mock_stage = mocker.Mock(spec=FileProcessStage)
    stage_init_mock = mocker.patch(
        "app.integrations.backends.file.process_runner.FileProcessStage",
        return_value=mock_stage,
    )

    bucket_names = ["bucket1", "bucket2"]
    result = await process_runner.run(
        bucket_names=bucket_names,
    )

    process_runner.context.credentials_resolver.get_credentials.assert_called_once_with(
        IntegrationSource.GCS
    )

    process_runner.adapter_class.assert_called_once()
    call_args = process_runner.adapter_class.call_args
    assert "credentials" in call_args.kwargs

    mock_document_store_class.assert_called_once()
    store_args = mock_document_store_class.call_args.kwargs
    assert store_args["tenant_id"] == process_runner.tenant_id
    assert store_args["source"] == process_runner.source

    stage_init_mock.assert_called_once()
    stage_args = stage_init_mock.call_args.kwargs
    assert stage_args["tenant_id"] == process_runner.tenant_id
    assert stage_args["source"] == process_runner.source
    assert stage_args["processor"] == mock_file_processor
    assert stage_args["bucket_names"] == bucket_names

    assert mock_pipeline.add_task.call_count == 1
    assert mock_pipeline.run.call_count == 1
    assert result == {"status": "success", "details": "test results"}


@pytest.mark.anyio
async def test_run_missing_credentials_resolver(mocker, mock_adapter_class):
    mock_context = mocker.Mock()
    mock_context.tenant_id = uuid.uuid4()

    def create_mock_session():
        mock_session = mocker.Mock()
        mock_session.__aenter__ = mocker.AsyncMock(return_value=mock_session)
        mock_session.__aexit__ = mocker.AsyncMock(return_value=None)
        return mock_session

    mock_context.db_session_factory = create_mock_session
    mock_context.credentials_resolver = None

    runner = FileProcessRunner(
        context=mock_context,
        source=IntegrationSource.GCS,
        adapter_class=mock_adapter_class,
    )

    with pytest.raises(RuntimeError):
        await runner.get_adapter()
