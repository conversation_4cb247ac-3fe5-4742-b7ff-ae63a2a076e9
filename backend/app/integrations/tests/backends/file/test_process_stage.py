import uuid

import pytest

from app.common.task_runner.base_task import BaseTask
from app.integrations.backends.file.process_stage import FileProcessStage
from app.integrations.schemas import FileProcessingResult
from app.integrations.types import IntegrationSource


@pytest.mark.anyio
async def test_execute_once_success(mocker):
    bucket_names = ["bucket1", "bucket2"]
    tenant_id = uuid.uuid4()
    source = IntegrationSource.GCS

    file_processor = mocker.Mock()
    file_processor.process_files = mocker.AsyncMock()

    result1 = FileProcessingResult(processed_files=10, deleted_documents=5)
    result2 = FileProcessingResult(processed_files=20, deleted_documents=15)

    file_processor.process_files.side_effect = [result1, result2]

    session = mocker.Mock()
    session.add = mocker.Mock()
    session.commit = mocker.AsyncMock()

    stage = FileProcessStage(
        tenant_id=tenant_id,
        source=source,
        db_session=session,
        processor=file_processor,
        bucket_names=bucket_names,
        interval_seconds=60,
    )

    result = await stage.execute_once()

    assert result["status"] == "success"
    assert result["buckets_processed"] == 2

    bucket_result = result["buckets"].get("bucket1")
    assert bucket_result is not None
    assert bucket_result["status"] == "success"
    assert bucket_result["processed_files"] == 10
    assert bucket_result["deleted_documents"] == 5

    bucket_result = result["buckets"].get("bucket2")
    assert bucket_result is not None
    assert bucket_result["status"] == "success"
    assert bucket_result["processed_files"] == 20
    assert bucket_result["deleted_documents"] == 15

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["buckets_processed"] == 2
    assert stage.metrics["successful_runs"] == 1
    assert stage.metrics["errors_count"] == 0

    call_kwargs_list = file_processor.process_files.call_args_list
    assert len(call_kwargs_list) == 2

    assert session.add.call_count == 2
    assert session.commit.call_count == 4


@pytest.mark.anyio
async def test_execute_once_partial(mocker, async_db_session):
    bucket_names = ["bucket1", "bucket2"]
    tenant_id = uuid.uuid4()
    source = IntegrationSource.GCS

    file_processor = mocker.Mock()
    file_processor.process_files = mocker.AsyncMock()

    async def fake_process_files(bucket_name):
        if bucket_name == "bucket2":
            raise Exception("Process error")
        return FileProcessingResult(processed_files=10, deleted_documents=5)

    file_processor.process_files.side_effect = fake_process_files

    stage = FileProcessStage(
        tenant_id=tenant_id,
        source=source,
        db_session=async_db_session,
        processor=file_processor,
        bucket_names=bucket_names,
        interval_seconds=60,
    )

    result = await stage.execute_once()

    assert result["status"] == "partial"
    assert result["buckets_processed"] == 1

    bucket_res_ok = result["buckets"].get("bucket1")
    assert bucket_res_ok is not None
    assert bucket_res_ok["status"] == "success"
    assert bucket_res_ok["processed_files"] == 10
    assert bucket_res_ok["deleted_documents"] == 5

    bucket_res_err = result["buckets"].get("bucket2")
    assert bucket_res_err is not None
    assert bucket_res_err["status"] == "error"
    assert "Process error" in bucket_res_err["error"]

    assert stage.metrics["total_runs"] == 1
    assert stage.metrics["buckets_processed"] == 1
    assert stage.metrics["successful_runs"] == 0
    assert stage.metrics["errors_count"] == 1


def test_get_status(mocker):
    bucket_names = ["bucket1", "bucket2"]
    tenant_id = uuid.UUID("12345678-1234-5678-1234-************")
    source = IntegrationSource.GCS

    file_processor = mocker.Mock()

    dummy_base_status = {"base": True}
    mocker.patch.object(BaseTask, "get_status", return_value=dummy_base_status)

    session = mocker.Mock()
    session.add = mocker.Mock()
    session.commit = mocker.AsyncMock()

    stage = FileProcessStage(
        tenant_id=tenant_id,
        source=source,
        db_session=session,
        processor=file_processor,
        bucket_names=bucket_names,
        interval_seconds=30,
    )

    status = stage.get_status()

    assert status["base"] is True
    assert status["tenant_id"] == "12345678-1234-5678-1234-************"
    assert status["source"] == str(source)
    assert status["bucket_names"] == bucket_names
    assert status["interval_seconds"] == 30
    assert "metrics" in status

    assert status["metrics"]["total_runs"] == 0
    assert status["metrics"]["successful_runs"] == 0
    assert status["metrics"]["buckets_processed"] == 0
    assert status["metrics"]["errors_count"] == 0
