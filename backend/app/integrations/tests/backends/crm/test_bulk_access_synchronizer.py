import uuid
from typing import Any

import pytest

from app.integrations.backends.crm.bulk_access_synchronizer import (
    AccountAccessSynchronizer,
)
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.schemas import (
    CRMAccountAccessData,
    CRMAccountAccessSlice,
    CRMAccountAccessSyncResult,
    CRMMetrics,
)
from app.integrations.types import IntegrationSource


class MockCRMAdapter(BaseCRMAdapter):
    def __init__(self):
        self.resolve_account_access_called = False
        self.user_id_received = None

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    async def resolve_account_access(self, crm_user_id: str):
        self.resolve_account_access_called = True
        self.user_id_received = crm_user_id
        return [
            CRMAccountAccessData(
                account_id="account1",
                account_name="Account One",
                access_type="owner",
                access_role="Manager",
            ),
            CRMAccountAccessData(
                account_id="account2",
                account_name="Account Two",
                access_type="team",
                access_role="Member",
            ),
        ]

    async def get_opportunity(self, opportunity_id: str):
        pass

    async def update_opportunity(self, opportunity_id: str, fields):
        pass

    async def list_opportunities_by_account(self, account_id, limit=100, offset=0):
        pass

    async def search_opportunities(self, search_criteria: dict, limit=100, offset=0):
        pass

    async def get_account(self, account_id: str):
        pass

    async def update_account(self, account_id: str, fields):
        pass

    async def search_accounts(self, search_criteria: dict, limit=100, offset=0):
        pass

    async def get_contact(self, contact_id: str):
        pass

    async def create_contact(self, contact_data: dict):
        pass

    async def update_contact(self, contact_id: str, contact_data: dict):
        pass

    async def list_contacts_by_account(self, account_id: str, limit=100, offset=0):
        pass

    async def search_contacts(self, search_criteria: dict, limit=100, offset=0):
        pass

    async def get_task(self, task_id: str):
        pass

    async def create_task(self, task_data: dict):
        pass

    async def update_task(self, task_id: str, task_data: dict):
        pass

    async def list_tasks_by_contact(self, contact_id: str, limit=100, offset=0):
        pass

    async def list_tasks_by_account(self, account_id: str, limit=100, offset=0):
        pass

    async def get_event(self, event_id: str):
        pass

    async def create_event(self, event_data: dict):
        pass

    async def update_event(self, event_id: str, event_data: dict):
        pass

    async def list_events_by_contact(self, contact_id: str, limit=100, offset=0):
        pass

    async def list_events_by_account(self, account_id: str, limit=100, offset=0):
        pass

    async def list_events_by_opportunity(
        self, opportunity_id: str, limit=100, offset=0
    ):
        pass

    async def list_tasks_by_opportunity(self, opportunity_id, limit=100, offset=0):
        pass

    async def get_metrics(
        self, _crm_user_id: str, _field_mapping: dict[str, Any] | None = None
    ):
        return CRMMetrics(
            quota=0,
            closed_won=0,
            pipeline=0,
            forecast=0,
        )

    async def get_account_closed_won_revenue(
        self, _account_id: str, _field_mapping: dict[str, Any] | None = None
    ) -> float:
        return 0.0


@pytest.fixture
def mock_crm_store(mocker):
    store_mock = mocker.Mock()
    store_mock.store_account_access = mocker.AsyncMock(return_value=(2, 1))
    return store_mock


@pytest.fixture
def mock_adapter():
    return MockCRMAdapter()


@pytest.fixture
def synchronizer(mock_crm_store, mock_adapter):
    tenant_id = uuid.uuid4()
    return AccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=mock_crm_store,
        adapter=mock_adapter,
    )


@pytest.mark.anyio
async def test_sync_user_access_success(synchronizer, mock_crm_store, mock_adapter):
    user_id = "user123"
    result = await synchronizer.sync_user_access(user_id)

    assert mock_adapter.resolve_account_access_called is True
    assert mock_adapter.user_id_received == user_id

    mock_crm_store.store_account_access.assert_called_once()

    call_args = mock_crm_store.store_account_access.call_args[0]
    assert isinstance(call_args[0], CRMAccountAccessSlice)
    assert call_args[0].user_id == user_id
    assert len(call_args[0].accounts) == 2

    assert isinstance(result, CRMAccountAccessSyncResult)
    assert result.new_access_count == 2
    assert result.old_access_count == 1


@pytest.mark.anyio
async def test_sync_user_access_with_empty_records(mocker, mock_crm_store):
    tenant_id = uuid.uuid4()
    mock_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter.resolve_account_access = mocker.AsyncMock(return_value=[])

    mock_crm_store.store_account_access = mocker.AsyncMock(return_value=(0, 0))

    synchronizer = AccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=mock_crm_store,
        adapter=mock_adapter,
    )

    user_id = "user123"
    result = await synchronizer.sync_user_access(user_id)

    mock_adapter.resolve_account_access.assert_called_once_with(user_id)

    mock_crm_store.store_account_access.assert_called_once()

    call_args = mock_crm_store.store_account_access.call_args[0]
    assert isinstance(call_args[0], CRMAccountAccessSlice)
    assert call_args[0].user_id == user_id
    assert len(call_args[0].accounts) == 0

    assert isinstance(result, CRMAccountAccessSyncResult)
    assert result.new_access_count == 0
    assert result.old_access_count == 0


@pytest.mark.anyio
async def test_sync_user_access_resolver_failure(mocker, mock_crm_store):
    tenant_id = uuid.uuid4()
    mock_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter.resolve_account_access = mocker.AsyncMock(
        side_effect=Exception("Resolution error")
    )

    synchronizer = AccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=mock_crm_store,
        adapter=mock_adapter,
    )

    user_id = "user123"
    with pytest.raises(Exception, match="Resolution error"):
        await synchronizer.sync_user_access(user_id)

    mock_adapter.resolve_account_access.assert_called_once_with(user_id)
    mock_crm_store.store_account_access.assert_not_called()


@pytest.mark.anyio
async def test_sync_user_access_store_failure(mocker, mock_crm_store):
    tenant_id = uuid.uuid4()
    mock_adapter = mocker.Mock(spec=BaseCRMAdapter)
    mock_adapter.resolve_account_access = mocker.AsyncMock(
        return_value=[
            CRMAccountAccessData(
                account_id="account1",
                account_name="Account One",
                access_type="owner",
                access_role="Manager",
            )
        ]
    )

    mock_crm_store.store_account_access = mocker.AsyncMock(
        side_effect=Exception("Storage error")
    )

    synchronizer = AccountAccessSynchronizer(
        tenant_id=tenant_id,
        crm_store=mock_crm_store,
        adapter=mock_adapter,
    )

    user_id = "user123"
    with pytest.raises(Exception, match="Storage error"):
        await synchronizer.sync_user_access(user_id)

    mock_adapter.resolve_account_access.assert_called_once_with(user_id)
    mock_crm_store.store_account_access.assert_called_once()
