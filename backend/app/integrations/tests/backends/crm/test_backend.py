import uuid
from typing import Any

import pytest

from app.integrations.backends.crm.backend import CRMBackend
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.base.crm_adapter import BaseCRMAdapter
from app.integrations.context import IntegrationContext
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics
from app.integrations.types import IntegrationSource


class MockCRMAdapter(BaseCRMAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)
        self.mock_data = {
            "opportunities": {
                "opp1": {"Id": "opp1", "Name": "Test Opportunity"},
            },
            "accounts": {
                "acc1": {"Id": "acc1", "Name": "Test Account"},
            },
            "contacts": {
                "con1": {"Id": "con1", "FirstName": "John", "LastName": "Doe"},
            },
            "tasks": {
                "tsk1": {
                    "Id": "tsk1",
                    "Subject": "Call Client",
                    "Status": "Not Started",
                },
            },
            "events": {
                "evt1": {
                    "Id": "evt1",
                    "Subject": "Client Meeting",
                    "StartDateTime": "2024-01-15T10:00:00Z",
                },
            },
        }

    @property
    def source(self) -> IntegrationSource:
        return IntegrationSource.SALESFORCE

    async def get_opportunity(self, opportunity_id: str):
        return self.mock_data["opportunities"].get(opportunity_id)

    async def list_opportunities_by_account(self, account_id, limit=100, offset=0):
        _ = limit, offset
        return [
            opp
            for opp in self.mock_data["opportunities"].values()
            if opp.get("AccountId") == account_id
        ]

    async def get_account(self, account_id: str):
        return self.mock_data["accounts"].get(account_id)

    async def get_contact(self, contact_id: str):
        return self.mock_data["contacts"].get(contact_id)

    async def list_contacts_by_account(self, account_id, limit=100, offset=0):
        _ = limit, offset
        return [
            contact
            for contact in self.mock_data["contacts"].values()
            if contact.get("AccountId") == account_id
        ]

    async def search_contacts(self, search_criteria, limit=100, offset=0):
        _ = limit, offset
        contacts = []
        for contact in self.mock_data["contacts"].values():
            match = True
            for field, value in search_criteria.items():
                if contact.get(field) != value:
                    match = False
                    break
            if match:
                contacts.append(contact)
        return contacts

    async def search_opportunities(self, search_criteria, limit=100, offset=0):
        _ = limit, offset
        opportunities = []
        for opportunity in self.mock_data["opportunities"].values():
            match = True
            for field, value in search_criteria.items():
                if opportunity.get(field) != value:
                    match = False
                    break
            if match:
                opportunities.append(opportunity)
        return opportunities

    async def search_accounts(self, search_criteria, limit=100, offset=0):
        _ = limit, offset
        accounts = []
        for account in self.mock_data["accounts"].values():
            match = True
            for field, value in search_criteria.items():
                if account.get(field) != value:
                    match = False
                    break
            if match:
                accounts.append(account)
        return accounts

    async def get_task(self, task_id: str):
        return self.mock_data["tasks"].get(task_id)

    async def list_tasks_by_contact(self, contact_id, limit=100, offset=0):
        _ = limit, offset
        return [
            task
            for task in self.mock_data["tasks"].values()
            if task.get("WhoId") == contact_id
        ]

    async def list_tasks_by_account(self, account_id, limit=100, offset=0):
        _ = limit, offset
        return [
            task
            for task in self.mock_data["tasks"].values()
            if task.get("WhatId") == account_id
        ]

    async def list_tasks_by_opportunity(self, opportunity_id, limit=100, offset=0):
        _ = limit, offset
        return [
            task
            for task in self.mock_data["tasks"].values()
            if task.get("WhatId") == opportunity_id
        ]

    async def get_event(self, event_id: str):
        return self.mock_data["events"].get(event_id)

    async def list_events_by_contact(self, contact_id, limit=100, offset=0):
        _ = limit, offset
        return [
            event
            for event in self.mock_data["events"].values()
            if event.get("WhoId") == contact_id
        ]

    async def list_events_by_account(self, account_id, limit=100, offset=0):
        _ = limit, offset
        return [
            event
            for event in self.mock_data["events"].values()
            if event.get("WhatId") == account_id
        ]

    async def list_events_by_opportunity(self, opportunity_id, limit=100, offset=0):
        _ = limit, offset
        return [
            event
            for event in self.mock_data["events"].values()
            if event.get("WhatId") == opportunity_id
        ]

    async def resolve_account_access(self, crm_user_id: str):
        _ = crm_user_id
        return [
            CRMAccountAccessData(
                account_id="acc1",
                account_name="Test Account",
                access_type="owner",
                access_role=None,
            )
        ]

    async def get_metrics(
        self, crm_user_id: str, field_mapping: dict[str, Any] | None = None
    ):
        _ = crm_user_id, field_mapping
        return CRMMetrics(
            quota=1000000,
            closed_won=250000,
            pipeline=500000,
            forecast=300000,
        )

    async def get_account_closed_won_revenue(
        self, account_id: str, field_mapping: dict[str, Any] | None = None
    ) -> float:
        _ = account_id, field_mapping
        return 125000.0


@pytest.fixture
def db_session_factory(mocker):
    def create_mock_session():
        mock_session = mocker.MagicMock()
        mock_session.__enter__ = mocker.Mock(return_value=mock_session)
        mock_session.__exit__ = mocker.Mock(return_value=None)
        return mock_session

    return create_mock_session


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "username": "test_user",
        "password": "test_pass",
    }

    async def mock_refresh_token():
        return mock_creds

    mock_creds.refresh_token = mock_refresh_token
    return mock_creds


@pytest.fixture
def credentials_resolver(mocker, mock_credentials):
    mock_resolver = mocker.MagicMock()

    async def mock_get_credentials(*_args, **_kwargs):
        return mock_credentials

    mock_resolver.get_credentials = mock_get_credentials
    return mock_resolver


@pytest.fixture
def context(tenant_id, db_session_factory, credentials_resolver):
    return IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=credentials_resolver,
    )


@pytest.fixture
def crm_store_mock(mocker):
    mock_store = mocker.MagicMock()
    return mock_store


@pytest.fixture
def crm_backend(mocker, context, crm_store_mock):
    mocker.patch(
        "app.integrations.backends.crm.backend.PostgresCRMStore",
        return_value=crm_store_mock,
    )

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )
    return backend


def test_init(context):
    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    assert backend.context == context
    assert backend.tenant_id == context.tenant_id
    assert backend.source == IntegrationSource.SALESFORCE


def test_init_with_null_credentials_resolver(context):
    context.credentials_resolver = None

    backend = CRMBackend(
        context=context,
        adapter_class=MockCRMAdapter,
        source=IntegrationSource.SALESFORCE,
    )

    assert backend.context.credentials_resolver is None


@pytest.mark.anyio
async def test_get_opportunity(crm_backend):
    result = await crm_backend.get_opportunity("opp1")
    assert result == {"Id": "opp1", "Name": "Test Opportunity"}


@pytest.mark.anyio
async def test_list_opportunities_by_account(crm_backend):
    result = await crm_backend.list_opportunities_by_account("acc1", limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_get_account(crm_backend):
    result = await crm_backend.get_account("acc1")
    assert result == {"Id": "acc1", "Name": "Test Account"}


@pytest.mark.anyio
async def test_get_contact(crm_backend):
    result = await crm_backend.get_contact("con1")
    assert result == {"Id": "con1", "FirstName": "John", "LastName": "Doe"}


@pytest.mark.anyio
async def test_list_contacts_by_account(crm_backend):
    result = await crm_backend.list_contacts_by_account("acc1", limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_search_contacts(crm_backend):
    search_criteria = {"FirstName": "John"}
    result = await crm_backend.search_contacts(search_criteria, limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_get_task(crm_backend):
    result = await crm_backend.get_task("tsk1")
    assert result == {"Id": "tsk1", "Subject": "Call Client", "Status": "Not Started"}


@pytest.mark.anyio
async def test_list_tasks_by_contact(crm_backend):
    result = await crm_backend.list_tasks_by_contact("con1", limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_list_tasks_by_account(crm_backend):
    result = await crm_backend.list_tasks_by_account("acc1", limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_list_tasks_by_opportunity(crm_backend):
    result = await crm_backend.list_tasks_by_opportunity("opp1", limit=10, offset=0)
    assert isinstance(result, list)


@pytest.mark.anyio
async def test_get_event(crm_backend):
    result = await crm_backend.get_event("evt1")
    assert result == {
        "Id": "evt1",
        "Subject": "Client Meeting",
        "StartDateTime": "2024-01-15T10:00:00Z",
    }


@pytest.mark.anyio
async def test_get_metrics(crm_backend):
    result = await crm_backend.get_metrics("user123")
    expected_metrics = CRMMetrics(
        quota=1000000,
        closed_won=250000,
        pipeline=500000,
        forecast=300000,
    )
    assert result == expected_metrics


@pytest.mark.anyio
async def test_get_account_closed_won_revenue(crm_backend):
    result = await crm_backend.get_account_closed_won_revenue("acc1")
    assert result == 125000.0
