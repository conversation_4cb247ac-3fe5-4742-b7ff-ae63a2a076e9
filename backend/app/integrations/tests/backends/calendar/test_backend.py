import uuid
from datetime import datetime

import pytest

from app.integrations.backends.calendar.backend import CalendarBackend
from app.integrations.base.calendar_adapter import BaseCalendarAdapter
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.context import IntegrationContext
from app.integrations.types import IntegrationSource


class MockCalendarAdapter(BaseCalendarAdapter):
    def __init__(self, credentials: ICredentials):
        super().__init__(credentials)

        self.calendars = {
            "cal1": {
                "id": "cal1",
                "name": "Primary Calendar",
                "description": "Main calendar",
                "timezone": "America/New_York",
                "is_primary": True,
                "access_role": "owner",
            },
            "cal2": {
                "id": "cal2",
                "name": "Work Calendar",
                "description": "Work events",
                "timezone": "America/New_York",
                "is_primary": False,
                "access_role": "writer",
            },
        }

        self.events = {
            "evt1": {
                "id": "evt1",
                "calendar_id": "cal1",
                "title": "Test Event",
                "description": "Test event description",
                "start": {
                    "date_time": datetime(2024, 1, 15, 10, 0, 0),
                    "timezone": "America/New_York",
                },
                "end": {
                    "date_time": datetime(2024, 1, 15, 10, 0, 0),
                    "timezone": "America/New_York",
                },
            },
            "evt2": {
                "id": "evt2",
                "calendar_id": "cal1",
                "title": "Another Event",
                "description": "Another test event",
                "start": {
                    "date_time": datetime(2024, 1, 15, 14, 0, 0),
                    "timezone": "America/New_York",
                },
                "end": {
                    "date_time": datetime(2024, 1, 15, 15, 0, 0),
                    "timezone": "America/New_York",
                },
            },
        }

    @property
    def source(self):
        return IntegrationSource.GOOGLE_CALENDAR

    async def list_calendars(self):
        return list(self.calendars.values())

    async def get_calendar(self, calendar_id):
        calendar = self.calendars.get(calendar_id)
        if calendar is None:
            raise KeyError(f"Calendar {calendar_id!r} not found")
        return calendar

    async def get_event(self, calendar_id, event_id):
        event = self.events.get(event_id)
        if event is None or event["calendar_id"] != calendar_id:
            raise KeyError(f"Event {event_id!r} not found in calendar {calendar_id!r}")
        return event

    async def get_user_info(self):
        return {"id": "user123", "email": "<EMAIL>", "name": "Test User"}

    async def list_events(
        self,
        calendar_id,
        start_time,
        end_time,
        max_results,
        single_events,  # noqa: ARG002
        order_by,  # noqa: ARG002
        show_deleted,  # noqa: ARG002
        page_token,  # noqa: ARG002
    ):
        selected = []

        for ev in self.events.values():
            if ev["calendar_id"] != calendar_id:
                continue

            if (
                ev["start"] is None
                or ev["start"]["date_time"] is None
                or ev["end"] is None
                or ev["end"]["date_time"] is None
            ):
                continue

            ev_start: datetime = ev["start"]["date_time"]
            ev_end: datetime = ev["end"]["date_time"]

            if start_time is not None and ev_end < start_time:
                continue
            if end_time is not None and ev_start > end_time:
                continue
            selected.append(ev)

        selected = selected[:max_results]

        return {
            "events": [
                {
                    "id": ev["id"],
                    "summary": ev["title"],
                    "start": {"dateTime": ev["start"]["date_time"].isoformat()},
                    "end": {"dateTime": ev["end"]["date_time"].isoformat()},
                }
                for ev in selected
            ],
            "nextPageToken": None,
        }

    async def search_events(
        self,
        calendar_id,
        query,
        start_time,
        end_time,
        max_results,
        order_by,  # noqa: ARG002
    ):
        found = []
        for ev in self.events.values():
            if ev["calendar_id"] != calendar_id:
                continue
            if query.lower() not in ev["title"].lower():
                continue
            if (
                ev["start"] is None
                or ev["start"]["date_time"] is None
                or ev["end"] is None
                or ev["end"]["date_time"] is None
            ):
                continue

            ev_start: datetime = ev["start"]["date_time"]
            ev_end: datetime = ev["end"]["date_time"]

            if start_time is not None and ev_end < start_time:
                continue
            if end_time is not None and ev_start > end_time:
                continue
            found.append(ev)

        return found[:max_results]


@pytest.fixture
def db_session_factory(mocker):
    def create_mock_session():
        mock_session = mocker.MagicMock()
        mock_session.__enter__ = mocker.Mock(return_value=mock_session)
        mock_session.__exit__ = mocker.Mock(return_value=None)
        return mock_session

    return create_mock_session


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def mock_credentials(mocker):
    mock_creds = mocker.Mock(spec=ICredentials)
    mock_creds.secrets = {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
    }

    async def mock_refresh_token():
        return mock_creds

    mock_creds.refresh_token = mock_refresh_token
    return mock_creds


@pytest.fixture
def credentials_resolver(mocker, mock_credentials):
    mock_resolver = mocker.MagicMock()

    async def mock_get_credentials(*_args, **_kwargs):
        return mock_credentials

    mock_resolver.get_credentials = mock_get_credentials
    return mock_resolver


@pytest.fixture
def context(tenant_id, db_session_factory, credentials_resolver):
    return IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=credentials_resolver,
    )


@pytest.fixture
def calendar_backend(context):
    backend = CalendarBackend(
        context=context,
        adapter_class=MockCalendarAdapter,
        source=IntegrationSource.GOOGLE_CALENDAR,
    )
    return backend


def test_init(context):
    backend = CalendarBackend(
        context=context,
        adapter_class=MockCalendarAdapter,
        source=IntegrationSource.GOOGLE_CALENDAR,
    )

    assert backend.context == context
    assert backend.tenant_id == context.tenant_id
    assert backend.source == IntegrationSource.GOOGLE_CALENDAR


def test_init_with_null_credentials_resolver(context):
    context.credentials_resolver = None

    backend = CalendarBackend(
        context=context,
        adapter_class=MockCalendarAdapter,
        source=IntegrationSource.GOOGLE_CALENDAR,
    )

    assert backend.context.credentials_resolver is None


@pytest.mark.anyio
async def test_list_calendars(calendar_backend):
    result = await calendar_backend.list_calendars()

    assert len(result) == 2
    assert result[0]["id"] == "cal1"
    assert result[0]["name"] == "Primary Calendar"
    assert result[0]["is_primary"] is True
    assert result[1]["id"] == "cal2"
    assert result[1]["name"] == "Work Calendar"
    assert result[1]["is_primary"] is False


@pytest.mark.anyio
async def test_get_calendar(calendar_backend):
    result = await calendar_backend.get_calendar("cal1")

    assert result["id"] == "cal1"
    assert result["name"] == "Primary Calendar"
    assert result["is_primary"] is True


@pytest.mark.anyio
async def test_list_events(calendar_backend):
    result = await calendar_backend.list_events("cal1")

    assert "events" in result
    assert len(result["events"]) == 2
    assert result["events"][0]["id"] == "evt1"
    assert result["events"][0]["summary"] == "Test Event"


@pytest.mark.anyio
async def test_list_events_with_params(calendar_backend):
    start_time = datetime(2024, 1, 15, 9, 0, 0)
    end_time = datetime(2024, 1, 15, 17, 0, 0)

    result = await calendar_backend.list_events(
        "cal1",
        start_time=start_time,
        end_time=end_time,
        max_results=10,
        single_events=True,
        order_by="startTime",
        show_deleted=False,
    )

    assert "events" in result
    assert len(result["events"]) <= 10


@pytest.mark.anyio
async def test_get_event(calendar_backend):
    result = await calendar_backend.get_event("cal1", "evt1")

    assert result["id"] == "evt1"
    assert result["calendar_id"] == "cal1"
    assert result["title"] == "Test Event"
    assert result["description"] == "Test event description"


@pytest.mark.anyio
async def test_search_events(calendar_backend):
    result = await calendar_backend.search_events("cal1", "test")

    assert len(result) >= 1
    assert any("test" in event["title"].lower() for event in result)


@pytest.mark.anyio
async def test_search_events_with_params(calendar_backend):
    start_time = datetime(2024, 1, 15, 9, 0, 0)
    end_time = datetime(2024, 1, 15, 17, 0, 0)

    result = await calendar_backend.search_events(
        "cal1",
        "test",
        start_time=start_time,
        end_time=end_time,
        max_results=10,
        order_by="startTime",
    )

    assert len(result) <= 10
    assert all("test" in event["title"].lower() for event in result)


@pytest.mark.anyio
async def test_get_user_info(calendar_backend):
    result = await calendar_backend.get_user_info()

    assert result["id"] == "user123"
    assert result["email"] == "<EMAIL>"
    assert result["name"] == "Test User"
