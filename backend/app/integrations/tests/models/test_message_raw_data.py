import datetime
import uuid

import pytest
from sqlalchemy.exc import IntegrityError

from app.integrations.models.message_raw_data import MessageRawData
from app.integrations.types import IntegrationSource


def test_unique_constraint(db_session):
    tenant_id = uuid.uuid4()
    message_id = "general#test_message"
    now = datetime.datetime.now(datetime.UTC)

    record1 = MessageRawData(
        tenant_id=tenant_id,
        message_id=message_id,
        channel_id="general",
        raw_data="{'content': 'Hello'}",
        hash="abc123",
        author="user1",
        thread_id=None,
        tombstone=False,
        sent_at=now,
        last_edit_at=None,
        source=IntegrationSource.SLACK,
    )
    db_session.add(record1)
    db_session.commit()

    record2 = MessageRawData(
        tenant_id=tenant_id,
        message_id=message_id,
        channel_id="general-noise",
        raw_data="{'content': 'World'}",
        hash="def456",
        author="user2",
        thread_id=None,
        tombstone=False,
        sent_at=now,
        last_edit_at=None,
        source=IntegrationSource.SLACK,
    )
    db_session.add(record2)

    with pytest.raises(IntegrityError):
        db_session.commit()
