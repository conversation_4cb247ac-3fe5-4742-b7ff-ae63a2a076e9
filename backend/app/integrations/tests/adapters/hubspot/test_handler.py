# ruff: noqa: S106

from datetime import datetime

import pytest

from app.integrations.adapters.hubspot.client import HubSpotClientError
from app.integrations.adapters.hubspot.handler import Hub<PERSON><PERSON><PERSON>andler
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import CRMAccountAccessData, CRMMetrics
from app.integrations.types import HubSpotObjectType


@pytest.fixture
def mock_credentials(mocker):
    mock_credentials = mocker.Mock(spec=ICredentials)
    mock_credentials.secrets = {
        "access_token": "mock_access_token",
    }
    return mock_credentials


@pytest.fixture
def hubspot_handler(mocker, mock_credentials):
    mock_client = mocker.MagicMock()
    mock_client.get_object = mocker.AsyncMock()
    mock_client.search_objects = mocker.AsyncMock()
    mock_client.search_goals = mocker.AsyncMock()

    mocker.patch(
        "app.integrations.adapters.hubspot.refresh_token_client_mixin.HubSpotClient",
        return_value=mock_client,
    )
    handler = HubSpotHandler(credentials=mock_credentials)
    assert handler.hubspot_client is mock_client
    return handler


def test_init_with_credentials(mocker, mock_credentials):
    mock_client = mocker.MagicMock()
    client_mock = mocker.patch(
        "app.integrations.adapters.hubspot.refresh_token_client_mixin.HubSpotClient",
        return_value=mock_client,
    )
    handler = HubSpotHandler(credentials=mock_credentials)

    client_mock.assert_called_once_with(access_token="mock_access_token")
    assert handler.hubspot_client is mock_client


@pytest.mark.anyio
async def test_get_opportunity_success(hubspot_handler):
    expected_deal = {
        "id": "*********",
        "properties": {
            "dealname": "Test Deal",
            "amount": "50000",
            "dealstage": "presentation",
            "closedate": "2025-12-31",
        },
    }
    hubspot_handler.hubspot_client.get_object.return_value = expected_deal
    result = await hubspot_handler.get_opportunity("*********")
    assert result == expected_deal
    hubspot_handler.hubspot_client.get_object.assert_called_once_with(
        HubSpotObjectType.DEAL.value, "*********"
    )


@pytest.mark.anyio
async def test_get_opportunity_error(hubspot_handler):
    hubspot_handler.hubspot_client.get_object.side_effect = HubSpotClientError(
        "Deal not found"
    )
    with pytest.raises(HubSpotClientError, match="Deal not found"):
        await hubspot_handler.get_opportunity("*********")


@pytest.mark.anyio
async def test_get_metrics_success(hubspot_handler):
    # Mock the search_objects for deals
    closed_won_deals = {
        "results": [
            {"properties": {"amount": "25000"}},
            {"properties": {"amount": "35000"}},
        ],
        "paging": None,
    }

    pipeline_deals = {
        "results": [
            {"properties": {"amount": "10000", "hs_forecast_amount": "8000"}},
            {"properties": {"amount": "20000", "hs_forecast_amount": "15000"}},
        ],
        "paging": None,
    }

    # Mock goals response
    goals_response = {
        "results": [
            {
                "properties": {
                    "hs_goal_target_value": "100000",
                    "hs_goal_type": "REVENUE",
                    "hs_goal_period_start": str(
                        int(datetime(2025, 1, 1).timestamp() * 1000)
                    ),
                }
            }
        ]
    }

    # Set up the mock to return different responses based on object type and filters
    def mock_search_objects(**kwargs):
        object_type = kwargs.get("object_type")

        # Handle goals requests
        if object_type == HubSpotObjectType.GOAL.value:
            return goals_response

        # Handle deal requests
        filters = kwargs.get("filter_groups", [{}])[0].get("filters", [])

        # Check if this is a closed won query
        for filter_item in filters:
            if filter_item.get("propertyName") == "hs_is_closed_won":
                return closed_won_deals

        # Check if this is a pipeline query
        for filter_item in filters:
            if filter_item.get("propertyName") == "hs_is_closed":
                return pipeline_deals

        return {"results": [], "paging": None}

    hubspot_handler.hubspot_client.search_objects.side_effect = mock_search_objects

    result = await hubspot_handler.get_metrics("12345")

    assert result == CRMMetrics(
        quota=100000,
        closed_won=60000,  # 25000 + 35000
        pipeline=30000,  # 10000 + 20000
        forecast=23000,  # 8000 + 15000
    )


@pytest.mark.anyio
async def test_get_metrics_with_invalid_amounts(hubspot_handler):
    # Mock deals with invalid amount data
    closed_won_deals = {
        "results": [
            {"properties": {"amount": "invalid"}},
            {"properties": {"amount": None}},
            {"properties": {"amount": "25000"}},
        ],
        "paging": None,
    }

    pipeline_deals = {
        "results": [
            {"properties": {"amount": "10000", "hs_forecast_amount": "invalid"}},
            {"properties": {"amount": "", "hs_forecast_amount": "5000"}},
        ],
        "paging": None,
    }

    goals_response = {"results": []}

    def mock_search_objects(**kwargs):
        object_type = kwargs.get("object_type")

        # Handle goals requests
        if object_type == HubSpotObjectType.GOAL.value:
            return goals_response

        # Handle deal requests
        filters = kwargs.get("filter_groups", [{}])[0].get("filters", [])

        for filter_item in filters:
            if filter_item.get("propertyName") == "hs_is_closed_won":
                return closed_won_deals

        for filter_item in filters:
            if filter_item.get("propertyName") == "hs_is_closed":
                return pipeline_deals

        return {"results": [], "paging": None}

    hubspot_handler.hubspot_client.search_objects.side_effect = mock_search_objects

    result = await hubspot_handler.get_metrics("12345")

    assert result == CRMMetrics(
        quota=0,
        closed_won=25000,  # Only valid amount
        pipeline=10000,  # Only valid amount
        forecast=5000,  # Only valid forecast amount
    )


@pytest.mark.anyio
async def test_get_metrics_with_pagination(hubspot_handler):
    # Mock paginated response
    first_batch = {
        "results": [{"properties": {"amount": "15000"}}],
        "paging": {"next": {"after": "token123"}},
    }

    second_batch = {
        "results": [{"properties": {"amount": "20000"}}],
        "paging": None,
    }

    call_count = 0

    def mock_search_objects(**kwargs):
        nonlocal call_count
        object_type = kwargs.get("object_type")

        # Handle goals requests
        if object_type == HubSpotObjectType.GOAL.value:
            return {"results": []}

        # Handle deal requests with pagination
        call_count += 1
        filters = kwargs.get("filter_groups", [{}])[0].get("filters", [])
        for filter_item in filters:
            if filter_item.get("propertyName") == "hs_is_closed_won":
                if call_count == 1:
                    return first_batch
                else:
                    return second_batch

        return {"results": [], "paging": None}

    hubspot_handler.hubspot_client.search_objects.side_effect = mock_search_objects

    result = await hubspot_handler.get_metrics("12345")

    assert result.closed_won == 35000  # 15000 + 20000


@pytest.mark.anyio
async def test_get_metrics_error_handling(hubspot_handler):
    hubspot_handler.hubspot_client.search_objects.side_effect = HubSpotClientError(
        "API Error"
    )

    result = await hubspot_handler.get_metrics("12345")

    assert result == CRMMetrics(
        quota=0,
        closed_won=0,
        pipeline=0,
        forecast=0,
    )


@pytest.mark.anyio
async def test_get_quota_from_goals_success(hubspot_handler):
    current_year = datetime.now().year
    goals_response = {
        "results": [
            {
                "properties": {
                    "hs_goal_target_value": "50000",
                    "hs_goal_type": "REVENUE_TARGET",
                    "hs_goal_period_start": str(
                        int(datetime(current_year, 1, 1).timestamp() * 1000)
                    ),
                }
            },
            {
                "properties": {
                    "hs_goal_target_value": "25000",
                    "hs_goal_type": "REVENUE",
                    "hs_goal_period_start": str(
                        int(datetime(current_year, 6, 1).timestamp() * 1000)
                    ),
                }
            },
        ]
    }

    hubspot_handler.hubspot_client.search_objects.return_value = goals_response

    result = await hubspot_handler._get_quota_from_goals("12345")

    assert result == 75000  # 50000 + 25000


@pytest.mark.anyio
async def test_get_quota_from_goals_invalid_data(hubspot_handler):
    goals_response = {
        "results": [
            {
                "properties": {
                    "hs_goal_target_value": "invalid",
                    "hs_goal_type": "REVENUE",
                }
            },
            {
                "properties": {
                    "hs_goal_target_value": None,
                    "hs_goal_type": "REVENUE",
                }
            },
        ]
    }

    hubspot_handler.hubspot_client.search_objects.return_value = goals_response

    result = await hubspot_handler._get_quota_from_goals("12345")

    assert result == 0


@pytest.mark.anyio
async def test_get_quota_from_goals_error(hubspot_handler):
    hubspot_handler.hubspot_client.search_objects.side_effect = HubSpotClientError(
        "Goals API Error"
    )

    result = await hubspot_handler._get_quota_from_goals("12345")

    assert result == 0


@pytest.mark.anyio
async def test_resolve_account_access(mocker, hubspot_handler):
    mock_access_data = [
        CRMAccountAccessData(
            account_id="*********",
            account_name="Test Company",
            access_type="owner",
            access_role=None,
        )
    ]
    mock_resolver = mocker.MagicMock()
    mock_resolver.get_user_account_access = mocker.AsyncMock(
        return_value=mock_access_data
    )
    mocker.patch(
        "app.integrations.adapters.hubspot.handler.HubSpotAccountAccessResolver",
        return_value=mock_resolver,
    )

    result = await hubspot_handler.resolve_account_access(crm_user_id="12345")

    from app.integrations.adapters.hubspot.handler import HubSpotAccountAccessResolver

    HubSpotAccountAccessResolver.assert_called_once_with(
        client=hubspot_handler.hubspot_client
    )
    mock_resolver.get_user_account_access.assert_called_once_with("12345")
    assert result == mock_access_data
