import pytest

from app.integrations.adapters.hubspot.access_resolver import (
    HubSpotAccountAccessResolver,
)
from app.integrations.types import HubSpotObjectType


@pytest.fixture
def mock_hubspot_client(mocker):
    client_mock = mocker.Mock()
    client_mock.search_objects = mocker.AsyncMock()
    client_mock.list_objects = mocker.AsyncMock()
    return client_mock


@pytest.fixture
def mock_resolver(mock_hubspot_client):
    resolver = HubSpotAccountAccessResolver(client=mock_hubspot_client)
    return resolver


@pytest.mark.anyio
async def test_get_owned_companies(mock_resolver):
    mock_resolver.hubspot_client.search_objects.return_value = {
        "results": [
            {
                "id": "company1",
                "properties": {
                    "name": "Company One",
                    "hubspot_owner_id": "user123",
                },
            },
            {
                "id": "company2",
                "properties": {
                    "name": "Company Two",
                    "hubspot_owner_id": "user123",
                },
            },
        ]
    }

    user_id = "user123"
    owned_companies = await mock_resolver._get_owned_companies(user_id)

    # Verify the search was called with correct parameters
    mock_resolver.hubspot_client.search_objects.assert_called_once_with(
        object_type=HubSpotObjectType.COMPANY.value,
        filter_groups=[
            {
                "filters": [
                    {
                        "propertyName": "hubspot_owner_id",
                        "operator": "EQ",
                        "value": user_id,
                    }
                ]
            }
        ],
        properties=["name", "domain", "hubspot_owner_id"],
        limit=100,
    )

    assert len(owned_companies) == 2
    assert all(company.get("access_type") == "owner" for company in owned_companies)
    assert {company["id"] for company in owned_companies} == {"company1", "company2"}
    assert owned_companies[0]["name"] == "Company One"
    assert owned_companies[1]["name"] == "Company Two"


@pytest.mark.anyio
async def test_get_accessible_companies(mock_resolver):
    mock_resolver.hubspot_client.list_objects.return_value = {
        "results": [
            {
                "id": "company3",
                "properties": {
                    "name": "Accessible Company One",
                    "hubspot_owner_id": "other_user",
                },
            },
            {
                "id": "company4",
                "properties": {
                    "name": "Accessible Company Two",
                    "hubspot_owner_id": "another_user",
                },
            },
            {
                "id": "company5",
                "properties": {
                    "name": "User Owned Company",
                    "hubspot_owner_id": "user123",  # This should be skipped
                },
            },
        ]
    }

    user_id = "user123"
    accessible_companies = await mock_resolver._get_accessible_companies(user_id)

    mock_resolver.hubspot_client.list_objects.assert_called_once_with(
        object_type=HubSpotObjectType.COMPANY.value,
        properties=["name", "domain", "hubspot_owner_id"],
        limit=100,
    )

    assert len(accessible_companies) == 2
    assert all(
        company.get("access_type") == "accessible" for company in accessible_companies
    )
    assert {company["id"] for company in accessible_companies} == {
        "company3",
        "company4",
    }
    assert accessible_companies[0]["name"] == "Accessible Company One"
    assert accessible_companies[1]["name"] == "Accessible Company Two"


@pytest.mark.anyio
async def test_retrieve_hubspot_companies_with_many_owned(mock_resolver):
    owned_companies_data = [
        {
            "id": f"owned_company_{i}",
            "properties": {
                "name": f"Owned Company {i}",
                "hubspot_owner_id": "user123",
            },
        }
        for i in range(15)
    ]

    mock_resolver.hubspot_client.search_objects.return_value = {
        "results": owned_companies_data
    }
    mock_resolver.hubspot_client.list_objects.return_value = {"results": []}

    user_id = "user123"
    companies = await mock_resolver._retrieve_hubspot_companies(user_id)

    mock_resolver.hubspot_client.search_objects.assert_called_once()
    mock_resolver.hubspot_client.list_objects.assert_called_once()

    assert len(companies) == 15
    assert all(company.get("access_type") == "owner" for company in companies)


@pytest.mark.anyio
async def test_retrieve_hubspot_companies_with_few_owned(mock_resolver):
    owned_companies_data = [
        {
            "id": "owned_company_1",
            "properties": {
                "name": "Owned Company 1",
                "hubspot_owner_id": "user123",
            },
        },
        {
            "id": "owned_company_2",
            "properties": {
                "name": "Owned Company 2",
                "hubspot_owner_id": "user123",
            },
        },
    ]

    accessible_companies_data = [
        {
            "id": "accessible_company_1",
            "properties": {
                "name": "Accessible Company 1",
                "hubspot_owner_id": "other_user",
            },
        },
    ]

    mock_resolver.hubspot_client.search_objects.return_value = {
        "results": owned_companies_data
    }
    mock_resolver.hubspot_client.list_objects.return_value = {
        "results": accessible_companies_data
    }

    user_id = "user123"
    companies = await mock_resolver._retrieve_hubspot_companies(user_id)

    mock_resolver.hubspot_client.search_objects.assert_called_once()
    mock_resolver.hubspot_client.list_objects.assert_called_once()

    assert len(companies) == 3
    owned_companies = [c for c in companies if c.get("access_type") == "owner"]
    accessible_companies = [
        c for c in companies if c.get("access_type") == "accessible"
    ]
    assert len(owned_companies) == 2
    assert len(accessible_companies) == 1


@pytest.mark.anyio
async def test_get_user_account_access(mock_resolver, mocker):
    mock_resolver._retrieve_hubspot_companies = mocker.AsyncMock(
        return_value=[
            {
                "id": "company1",
                "name": "Company One",
                "access_type": "owner",
                "role": None,
            },
            {
                "id": "company2",
                "name": "Company Two",
                "access_type": "accessible",
                "role": None,
            },
        ]
    )

    user_id = "user123"
    account_access = await mock_resolver.get_user_account_access(user_id)

    assert len(account_access) == 2
    assert account_access[0].account_id == "company1"
    assert account_access[0].account_name == "Company One"
    assert account_access[0].access_type == "owner"
    assert account_access[1].account_id == "company2"
    assert account_access[1].account_name == "Company Two"
    assert account_access[1].access_type == "accessible"


@pytest.mark.anyio
async def test_error_handling_in_accessible_companies(mock_resolver):
    mock_resolver.hubspot_client.list_objects.side_effect = Exception("List error")

    user_id = "user123"
    accessible_companies = await mock_resolver._get_accessible_companies(user_id)

    assert accessible_companies == []
