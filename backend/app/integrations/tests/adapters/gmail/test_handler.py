from datetime import datetime
from unittest.mock import AsyncMock, <PERSON><PERSON>

import pytest

from app.integrations.adapters.gmail.handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from app.integrations.base.credentials_resolver import ICredentials
from app.integrations.schemas import EmailMessage, EmailThread
from app.integrations.types import IntegrationSource


@pytest.fixture
def mock_credentials():
    credentials = Mock(spec=ICredentials)
    credentials.secrets = {
        "access_token": "test_access_token",
        "refresh_token": "test_refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "token_uri": "https://oauth2.googleapis.com/token",
        "scope": "https://www.googleapis.com/auth/gmail.readonly",
        "scopes": ["https://www.googleapis.com/auth/gmail.readonly"],
    }
    return credentials


@pytest.fixture
def mock_gmail_client():
    client = AsyncMock()

    # Mock user info
    client.get_user_info.return_value = {
        "emailAddress": "<EMAIL>",
        "messagesTotal": 1000,
        "threadsTotal": 500,
        "historyId": "12345",
    }

    # Mock list messages
    client.list_messages.return_value = {
        "messages": [
            {"id": "msg1", "threadId": "thread1"},
            {"id": "msg2", "threadId": "thread2"},
        ],
        "nextPageToken": None,
        "resultSizeEstimate": 2,
    }

    # Mock get message
    client.get_message.return_value = {
        "id": "msg1",
        "threadId": "thread1",
        "snippet": "Test message snippet",
        "internalDate": "1640995200000",  # 2022-01-01 00:00:00
        "sizeEstimate": 1024,
        "labelIds": ["INBOX", "UNREAD"],
        "payload": {
            "headers": [
                {"name": "Subject", "value": "Test Subject"},
                {"name": "From", "value": "<EMAIL>"},
                {"name": "To", "value": "<EMAIL>"},
                {"name": "Cc", "value": "<EMAIL>"},
            ],
            "body": {"data": "SGVsbG8gV29ybGQ"},  # "Hello World" in base64
            "mimeType": "text/plain",
        },
    }

    # Mock list threads
    client.list_threads.return_value = {
        "threads": [
            {"id": "thread1", "snippet": "Thread 1 snippet"},
            {"id": "thread2", "snippet": "Thread 2 snippet"},
        ],
        "nextPageToken": None,
        "resultSizeEstimate": 2,
    }

    # Mock get thread
    client.get_thread.return_value = {
        "id": "thread1",
        "snippet": "Thread snippet",
        "historyId": "12345",
        "labelIds": ["INBOX"],
        "messages": [
            {
                "id": "msg1",
                "threadId": "thread1",
                "snippet": "Message in thread",
                "internalDate": "1640995200000",
                "sizeEstimate": 512,
                "labelIds": ["INBOX"],
                "payload": {
                    "headers": [
                        {"name": "Subject", "value": "Thread Subject"},
                        {"name": "From", "value": "<EMAIL>"},
                    ],
                    "body": {
                        "data": "VGhyZWFkIG1lc3NhZ2U"
                    },  # "Thread message" in base64
                    "mimeType": "text/plain",
                },
            }
        ],
    }

    # Mock list labels
    client.list_labels.return_value = {
        "labels": [
            {"id": "INBOX", "name": "INBOX", "type": "system"},
            {"id": "SENT", "name": "SENT", "type": "system"},
        ]
    }

    # Mock modify message
    client.modify_message.return_value = {
        "id": "msg1",
        "labelIds": ["INBOX", "STARRED"],
    }

    return client


@pytest.fixture
def gmail_handler(mock_credentials, mock_gmail_client, mocker):
    # Mock the GmailClient class
    mocker.patch(
        "app.integrations.adapters.gmail.handler.GmailClient",
        return_value=mock_gmail_client,
    )

    # Mock the GoogleRefreshTokenClientMixin initialization
    mocker.patch.object(GmailHandler, "init_google_client")

    handler = GmailHandler(mock_credentials)
    handler.google_client = mock_gmail_client

    return handler


class TestGmailHandler:
    @pytest.mark.anyio
    async def test_get_user_info(self, gmail_handler, mock_gmail_client):
        result = await gmail_handler.get_user_info()

        assert result["emailAddress"] == "<EMAIL>"
        assert result["messagesTotal"] == 1000
        assert result["threadsTotal"] == 500
        mock_gmail_client.get_user_info.assert_called_once()

    @pytest.mark.anyio
    async def test_list_messages_success(
        self, gmail_handler, mock_gmail_client, mocker
    ):
        # Mock conversion function
        mock_convert = mocker.patch(
            "app.integrations.adapters.gmail.handler.convert_gmail_message_to_email_message"
        )
        mock_convert.return_value = EmailMessage(
            id="msg1",
            thread_id="thread1",
            subject="Test Subject",
            sender="<EMAIL>",
            recipients="<EMAIL>",
            cc="<EMAIL>",
            bcc=None,
            date=datetime.fromtimestamp(1640995200),
            body="Hello World",
            snippet="Test message snippet",
            labels=["INBOX", "UNREAD"],
            attachments=[],
            size_estimate=1024,
            raw_data=mock_gmail_client.get_message.return_value,
        )

        result = await gmail_handler.list_messages(
            query="from:<EMAIL>",
            max_results=50,
            page_token="page_token",  # noqa: S106
            include_spam_trash=True,
        )

        assert len(result) == 2
        assert isinstance(result[0], EmailMessage)
        assert result[0].subject == "Test Subject"
        assert result[0].sender == "<EMAIL>"

        mock_gmail_client.list_messages.assert_called_once_with(
            query="from:<EMAIL>",
            max_results=50,
            page_token="page_token",  # noqa: S106
            include_spam_trash=True,
        )
        # Called twice for each message in the list
        assert mock_gmail_client.get_message.call_count == 2
        assert mock_convert.call_count == 2

    @pytest.mark.anyio
    async def test_list_messages_minimal_params(
        self, gmail_handler, mock_gmail_client, mocker
    ):
        # Mock conversion function
        mocker.patch(
            "app.integrations.adapters.gmail.handler.convert_gmail_message_to_email_message",
            return_value=Mock(spec=EmailMessage),
        )

        result = await gmail_handler.list_messages()

        assert len(result) == 2
        mock_gmail_client.list_messages.assert_called_once_with(
            query="",
            max_results=100,
            page_token=None,
            include_spam_trash=False,
        )

    @pytest.mark.anyio
    async def test_get_message(self, gmail_handler, mock_gmail_client, mocker):
        message_id = "msg123"
        mock_convert = mocker.patch(
            "app.integrations.adapters.gmail.handler.convert_gmail_message_to_email_message"
        )
        mock_convert.return_value = EmailMessage(
            id=message_id,
            thread_id="thread1",
            subject="Test Subject",
            sender="<EMAIL>",
            recipients="<EMAIL>",
            cc=None,
            bcc=None,
            date=datetime.fromtimestamp(1640995200),
            body="Hello World",
            snippet="Test message snippet",
            labels=["INBOX"],
            attachments=[],
            size_estimate=1024,
            raw_data=mock_gmail_client.get_message.return_value,
        )

        result = await gmail_handler.get_message(message_id)

        assert isinstance(result, EmailMessage)
        assert result.id == message_id
        assert result.subject == "Test Subject"

        mock_gmail_client.get_message.assert_called_once_with(message_id)
        mock_convert.assert_called_once()

    @pytest.mark.anyio
    async def test_list_threads_success(self, gmail_handler, mock_gmail_client, mocker):
        # Mock conversion function
        mock_convert = mocker.patch(
            "app.integrations.adapters.gmail.handler.convert_gmail_thread_to_email_thread"
        )
        mock_convert.return_value = EmailThread(
            id="thread1",
            snippet="Thread snippet",
            messages=[],
            labels=["INBOX"],
            history_id="12345",
        )

        result = await gmail_handler.list_threads(
            query="subject:important",
            max_results=25,
            page_token="token",  # noqa: S106
            include_spam_trash=False,
        )

        assert len(result) == 2
        assert isinstance(result[0], EmailThread)
        assert result[0].id == "thread1"

        mock_gmail_client.list_threads.assert_called_once_with(
            query="subject:important",
            max_results=25,
            page_token="token",  # noqa: S106
            include_spam_trash=False,
        )
        # Called twice for each thread in the list
        assert mock_gmail_client.get_thread.call_count == 2
        assert mock_convert.call_count == 2

    @pytest.mark.anyio
    async def test_get_thread(self, gmail_handler, mock_gmail_client, mocker):
        thread_id = "thread123"
        mock_convert = mocker.patch(
            "app.integrations.adapters.gmail.handler.convert_gmail_thread_to_email_thread"
        )
        mock_convert.return_value = EmailThread(
            id=thread_id,
            snippet="Thread snippet",
            messages=[
                EmailMessage(
                    id="msg1",
                    thread_id=thread_id,
                    subject="Thread Subject",
                    sender="<EMAIL>",
                    recipients="<EMAIL>",
                    cc=None,
                    bcc=None,
                    date=datetime.fromtimestamp(1640995200),
                    body="Thread message",
                    snippet="Message in thread",
                    labels=["INBOX"],
                    attachments=[],
                    size_estimate=512,
                    raw_data={},
                )
            ],
            labels=["INBOX"],
            history_id="12345",
        )

        result = await gmail_handler.get_thread(thread_id)

        assert isinstance(result, EmailThread)
        assert result.id == thread_id
        assert len(result.messages) == 1

        mock_gmail_client.get_thread.assert_called_once_with(thread_id)
        mock_convert.assert_called_once()

    @pytest.mark.anyio
    async def test_search_messages_success(self, gmail_handler, mocker):
        # Mock the query creation function
        mock_create_query = mocker.patch(
            "app.integrations.adapters.gmail.handler.create_gmail_query"
        )
        mock_create_query.return_value = "from:<EMAIL> subject:important"

        # Mock the list_messages method
        mock_list_messages = mocker.patch.object(gmail_handler, "list_messages")
        mock_list_messages.return_value = [
            Mock(spec=EmailMessage, subject="Important message")
        ]

        result = await gmail_handler.search_messages(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="important",
            has_attachment=True,
            is_unread=True,
            in_folder="inbox",
            after_date=datetime(2022, 1, 1),
            before_date=datetime(2022, 12, 31),
            query_text="urgent",
            max_results=50,
        )

        assert len(result) == 1
        assert isinstance(result[0], Mock)

        mock_create_query.assert_called_once_with(
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="important",
            has_attachment=True,
            is_unread=True,
            in_folder="inbox",
            after_date=datetime(2022, 1, 1),
            before_date=datetime(2022, 12, 31),
            query_text="urgent",
        )
        mock_list_messages.assert_called_once_with(
            query="from:<EMAIL> subject:important", max_results=50
        )

    @pytest.mark.anyio
    async def test_search_messages_minimal_params(self, gmail_handler, mocker):
        # Mock the query creation function
        mock_create_query = mocker.patch(
            "app.integrations.adapters.gmail.handler.create_gmail_query"
        )
        mock_create_query.return_value = ""

        # Mock the list_messages method
        mock_list_messages = mocker.patch.object(gmail_handler, "list_messages")
        mock_list_messages.return_value = []

        result = await gmail_handler.search_messages()

        assert len(result) == 0

        mock_create_query.assert_called_once_with(
            sender=None,
            recipient=None,
            subject=None,
            has_attachment=None,
            is_unread=None,
            in_folder=None,
            after_date=None,
            before_date=None,
            query_text=None,
        )
        mock_list_messages.assert_called_once_with(query="", max_results=100)

    @pytest.mark.anyio
    async def test_modify_message(self, gmail_handler, mock_gmail_client):
        message_id = "msg123"
        add_labels = ["STARRED"]
        remove_labels = ["UNREAD"]

        result = await gmail_handler.modify_message(
            message_id, add_labels=add_labels, remove_labels=remove_labels
        )

        assert result["id"] == "msg1"
        assert "STARRED" in result["labelIds"]

        mock_gmail_client.modify_message.assert_called_once_with(
            message_id=message_id,
            add_label_ids=add_labels,
            remove_label_ids=remove_labels,
        )

    @pytest.mark.anyio
    async def test_modify_message_minimal_params(
        self, gmail_handler, mock_gmail_client
    ):
        message_id = "msg123"

        result = await gmail_handler.modify_message(message_id)

        assert result["id"] == "msg1"

        mock_gmail_client.modify_message.assert_called_once_with(
            message_id=message_id,
            add_label_ids=None,
            remove_label_ids=None,
        )

    @pytest.mark.anyio
    async def test_list_labels(self, gmail_handler, mock_gmail_client):
        result = await gmail_handler.list_labels()

        assert len(result) == 2
        assert result[0]["id"] == "INBOX"
        assert result[1]["id"] == "SENT"

        mock_gmail_client.list_labels.assert_called_once()

    @pytest.mark.anyio
    async def test_list_labels_empty(self, gmail_handler, mock_gmail_client):
        # Mock empty labels response
        mock_gmail_client.list_labels.return_value = {}

        result = await gmail_handler.list_labels()

        assert len(result) == 0
        mock_gmail_client.list_labels.assert_called_once()

    def test_init(self, mock_credentials, mocker):
        # Mock the mixin initialization
        mock_init = mocker.patch.object(GmailHandler, "init_google_client")

        GmailHandler(mock_credentials)

        mock_init.assert_called_once_with(mock_credentials, IntegrationSource.GMAIL)

    @pytest.mark.anyio
    async def test_decorated_methods_with_expired_session(
        self, gmail_handler, mock_gmail_client
    ):
        """Test that methods are properly decorated with handle_expired_session."""
        # This is more of an integration test to ensure the decorator is applied
        # We can't easily test the decorator behavior in unit tests without
        # complex mocking, but we can at least verify the methods work

        await gmail_handler.get_user_info()
        mock_gmail_client.get_user_info.assert_called()

        await gmail_handler.list_labels()
        mock_gmail_client.list_labels.assert_called()

        await gmail_handler.modify_message("msg1", add_labels=["STARRED"])
        mock_gmail_client.modify_message.assert_called()
