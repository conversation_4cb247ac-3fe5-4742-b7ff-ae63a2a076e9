# ruff: noqa: S106

import pytest
from simple_salesforce import SalesforceAuthenticationFailed

from app.integrations.adapters.salesforce.client import (
    SalesforceClient,
    SalesforceClientError,
)


@pytest.fixture
def sfdc_client(mocker):
    mock_sf = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mock_sf,
    )
    client = SalesforceClient(
        username="<EMAIL>",
        password="password123",
        security_token="token123",
    )
    return client


def test_create_client_success(mocker):
    mock_sf = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mock_sf,
    )
    client = SalesforceClient(
        username="<EMAIL>",
        password="password123",
        security_token="token123",
    )
    assert client._client is not None


def test_create_client_with_oauth_success(mocker):
    mock_sf = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mock_sf,
    )
    client = SalesforceClient(
        instance_url="https://test.salesforce.com",
        access_token="mock_access_token",
    )
    assert client._client is not None
    from app.integrations.adapters.salesforce.client import Salesforce

    Salesforce.assert_called_once_with(
        instance_url="https://test.salesforce.com",
        session_id="mock_access_token",
        domain="login",
        version="55.0",
    )


def test_create_client_with_oauth_auth_failed(mocker):
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        side_effect=SalesforceAuthenticationFailed(
            "INVALID_SESSION_ID", "Session expired or invalid"
        ),
    )
    with pytest.raises(SalesforceClientError, match="Authentication failed"):
        _ = SalesforceClient(
            instance_url="https://test.salesforce.com",
            access_token="invalid_token",
        )._client


def test_lazy_client_initialization_with_oauth(mocker):
    mock_create_client = mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mocker.Mock(),
    )
    client = SalesforceClient(
        instance_url="https://test.salesforce.com", access_token="mock_access_token"
    )
    mock_create_client.assert_not_called()
    _ = client._client
    mock_create_client.assert_called_once()
    mock_create_client.reset_mock()
    _ = client._client
    mock_create_client.assert_not_called()


def test_client_initialization_with_custom_domain_and_version(mocker):
    mock_sf = mocker.MagicMock()
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mock_sf,
    )
    client = SalesforceClient(
        instance_url="https://test.salesforce.com",
        access_token="mock_access_token",
        domain="test",
        version="56.0",
    )
    _ = client._client
    from app.integrations.adapters.salesforce.client import Salesforce

    Salesforce.assert_called_once_with(
        instance_url="https://test.salesforce.com",
        session_id="mock_access_token",
        domain="test",
        version="56.0",
    )


def test_create_client_using_different_auth_methods(mocker):
    mock_sf = mocker.MagicMock()
    sf_mock = mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mock_sf,
    )
    client1 = SalesforceClient(
        username="<EMAIL>",
        password="password123",
        security_token="token123",
    )
    _ = client1._client
    sf_mock.assert_called_with(
        username="<EMAIL>",
        password="password123",
        security_token="token123",
        domain="login",
        version="55.0",
    )
    sf_mock.reset_mock()
    client2 = SalesforceClient(
        instance_url="https://test.salesforce.com",
        access_token="mock_access_token",
    )
    _ = client2._client
    sf_mock.assert_called_with(
        instance_url="https://test.salesforce.com",
        session_id="mock_access_token",
        domain="login",
        version="55.0",
    )


def test_create_client_auth_failed(mocker):
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        side_effect=SalesforceAuthenticationFailed(
            "INVALID_LOGIN", "Invalid credentials"
        ),
    )
    with pytest.raises(SalesforceClientError, match="Authentication failed"):
        _ = SalesforceClient(
            username="<EMAIL>",
            password="wrong_password",
            security_token="wrong_token",
        )._client


def test_create_client_other_exception(mocker):
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        side_effect=Exception("Connection error"),
    )
    with pytest.raises(SalesforceClientError, match="Client initialization failed"):
        _ = SalesforceClient(
            username="<EMAIL>",
            password="password123",
            security_token="token123",
        )._client


def test_client_lazy_initialization(mocker):
    mock_create_client = mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        return_value=mocker.Mock(),
    )
    client = SalesforceClient(
        username="<EMAIL>", password="password123", security_token="token123"
    )
    mock_create_client.assert_not_called()
    _ = client._client
    mock_create_client.assert_called_once()
    mock_create_client.reset_mock()
    _ = client._client
    mock_create_client.assert_not_called()


def test_client_lazy_initialization_with_error(mocker):
    mocker.patch(
        "app.integrations.adapters.salesforce.client.Salesforce",
        side_effect=SalesforceAuthenticationFailed("INVALID_LOGIN", "Auth failed"),
    )
    client = SalesforceClient(
        username="<EMAIL>", password="password123", security_token="token123"
    )
    with pytest.raises(SalesforceClientError, match="Authentication failed"):
        _ = client._client


@pytest.mark.anyio
async def test_get_available_objects_lazy_loading(sfdc_client):
    mock_describe_result = {
        "sobjects": [{"name": "Account"}, {"name": "Contact"}, {"name": "Opportunity"}]
    }
    sfdc_client._client.describe.return_value = mock_describe_result
    first_access = await sfdc_client.get_available_objects()
    assert first_access == {"Account", "Contact", "Opportunity"}
    sfdc_client._client.describe.assert_called_once()
    sfdc_client._client.describe.reset_mock()
    second_access = await sfdc_client.get_available_objects()
    assert second_access == {"Account", "Contact", "Opportunity"}
    sfdc_client._client.describe.assert_not_called()


@pytest.mark.anyio
async def test_get_available_objects_with_no_objects(sfdc_client):
    sfdc_client._client.describe.return_value = {"sobjects": []}
    objects = await sfdc_client.get_available_objects()
    assert objects == set()
    sfdc_client._client.describe.assert_called_once()


@pytest.mark.anyio
async def test_get_object_success(sfdc_client, mocker):
    mock_account = mocker.MagicMock()
    mock_account.get.return_value = {"Id": "001XXXXXXXXXXXX", "Name": "Test Account"}
    sfdc_client._client.Account = mock_account
    result = await sfdc_client.get_object("Account", "001XXXXXXXXXXXX")
    assert result["Id"] == "001XXXXXXXXXXXX"
    assert result["Name"] == "Test Account"
    mock_account.get.assert_called_once_with("001XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_get_object_error(sfdc_client, mocker):
    mock_account = mocker.MagicMock()
    mock_account.get.side_effect = Exception("Object not found")
    sfdc_client._client.Account = mock_account
    with pytest.raises(SalesforceClientError, match="Failed to get Account"):
        await sfdc_client.get_object("Account", "001XXXXXXXXXXXX")


@pytest.mark.anyio
async def test_query_success(sfdc_client):
    expected_result = {
        "totalSize": 1,
        "done": True,
        "records": [{"Id": "001XXXXXXXXXXXX", "Name": "Test Account"}],
    }
    sfdc_client._client.query.return_value = expected_result
    result = await sfdc_client.query("SELECT Id, Name FROM Account LIMIT 1")
    assert result == expected_result
    sfdc_client._client.query.assert_called_once_with(
        "SELECT Id, Name FROM Account LIMIT 1"
    )


@pytest.mark.anyio
async def test_query_error(sfdc_client):
    sfdc_client._client.query.side_effect = Exception("Invalid SOQL query")
    with pytest.raises(SalesforceClientError, match="Query failed"):
        await sfdc_client.query("SELECT Invalid FROM Account")


@pytest.mark.anyio
async def test_list_objects_success(sfdc_client, mocker):
    expected_result = {
        "totalSize": 2,
        "done": True,
        "records": [
            {"Id": "001XXXXXXXXXXXX", "Name": "Account 1"},
            {"Id": "001YYYYYYYYYYYY", "Name": "Account 2"},
        ],
    }
    format_soql_mock = mocker.patch(
        "app.integrations.adapters.salesforce.client.format_soql",
        return_value="SELECT Id, Name FROM Account LIMIT 100 OFFSET 0",
    )
    sfdc_client._client.query.return_value = expected_result
    result = await sfdc_client.list_objects(
        object_type="Account", fields=["Id", "Name"], limit=100, offset=0
    )
    assert len(result) == 2
    assert result[0]["Name"] == "Account 1"
    assert result[1]["Name"] == "Account 2"
    format_soql_mock.assert_called_once()
    sfdc_client._client.query.assert_called_once_with(
        "SELECT Id, Name FROM Account LIMIT 100 OFFSET 0"
    )


@pytest.mark.anyio
async def test_list_objects_with_all_fields(sfdc_client, mocker):
    format_soql_mock = mocker.patch(
        "app.integrations.adapters.salesforce.client.format_soql",
        return_value="SELECT FIELDS(ALL) FROM Account LIMIT 10 OFFSET 0",
    )
    sfdc_client._client.query.return_value = {"records": []}
    await sfdc_client.list_objects(object_type="Account", fields="ALL", limit=10)
    format_soql_mock.assert_called_once()
    sfdc_client._client.query.assert_called_once_with(
        "SELECT FIELDS(ALL) FROM Account LIMIT 10 OFFSET 0"
    )


@pytest.mark.anyio
async def test_list_objects_with_where_and_order(sfdc_client, mocker):
    format_soql_mock = mocker.patch(
        "app.integrations.adapters.salesforce.client.format_soql",
        return_value="SELECT Id, Name, Amount FROM Opportunity WHERE Amount > 10000 ORDER BY CloseDate DESC LIMIT 5 OFFSET 0",
    )
    sfdc_client._client.query.return_value = {"records": []}
    await sfdc_client.list_objects(
        object_type="Opportunity",
        fields=["Id", "Name", "Amount"],
        where_clause="Amount > 10000",
        order_by="CloseDate DESC",
        limit=5,
    )
    format_soql_mock.assert_called_once()
    sfdc_client._client.query.assert_called_once_with(
        "SELECT Id, Name, Amount FROM Opportunity WHERE Amount > 10000 ORDER BY CloseDate DESC LIMIT 5 OFFSET 0"
    )


@pytest.mark.anyio
async def test_list_objects_query_error(sfdc_client, mocker):
    mocker.patch(
        "app.integrations.adapters.salesforce.client.format_soql",
        return_value="SELECT Id FROM Account",
    )
    sfdc_client._client.query.side_effect = Exception("SOQL syntax error")
    with pytest.raises(SalesforceClientError, match="Failed to list Account objects"):
        await sfdc_client.list_objects("Account")


@pytest.mark.anyio
async def test_list_objects_by_owner(sfdc_client, mocker):
    list_objects_mock = mocker.patch.object(
        sfdc_client, "list_objects", return_value=[{"Id": "001XX", "Name": "Test"}]
    )
    result = await sfdc_client.list_objects_by_owner(
        object_type="Account",
        owner_id="005XXXXXXXXXXXX",
        fields=["Id", "Name"],
        order_by="Name",
        limit=50,
    )
    assert result == [{"Id": "001XX", "Name": "Test"}]
    list_objects_mock.assert_called_once_with(
        object_type="Account",
        fields=["Id", "Name"],
        where_clause="OwnerId = '005XXXXXXXXXXXX'",
        order_by="Name",
        limit=50,
        offset=0,
    )


@pytest.mark.anyio
async def test_list_objects_by_owner_with_additional_filters(sfdc_client, mocker):
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    await sfdc_client.list_objects_by_owner(
        object_type="Opportunity",
        owner_id="005XXXXXXXXXXXX",
        additional_filters="StageName = 'Closed Won'",
        fields="ALL",
    )
    list_objects_mock.assert_called_once_with(
        object_type="Opportunity",
        fields="ALL",
        where_clause="OwnerId = '005XXXXXXXXXXXX' AND StageName = 'Closed Won'",
        order_by=None,
        limit=100,
        offset=0,
    )


@pytest.mark.anyio
async def test_list_objects_by_ids_success(sfdc_client, mocker):
    object_ids = ["001XXXXXXXXXXXX", "001YYYYYYYYYYYY", "001ZZZZZZZZZZZZ"]
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    expected_records = [
        {"Id": "001XXXXXXXXXXXX", "Name": "Account 1"},
        {"Id": "001YYYYYYYYYYYY", "Name": "Account 2"},
        {"Id": "001ZZZZZZZZZZZZ", "Name": "Account 3"},
    ]
    list_objects_mock.return_value = expected_records
    result = await sfdc_client.list_objects_by_ids(
        object_type="Account", object_ids=object_ids, fields=["Id", "Name"]
    )
    assert result == expected_records
    expected_where_clause = (
        "Id IN ('001XXXXXXXXXXXX', '001YYYYYYYYYYYY', '001ZZZZZZZZZZZZ')"
    )
    list_objects_mock.assert_called_once_with(
        object_type="Account",
        fields=["Id", "Name"],
        where_clause=expected_where_clause,
        limit=3,
    )


@pytest.mark.anyio
async def test_list_objects_by_ids_empty_list(sfdc_client, mocker):
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    result = await sfdc_client.list_objects_by_ids(
        object_type="Account", object_ids=[], fields=["Id", "Name"]
    )
    assert result == []
    list_objects_mock.assert_not_called()


@pytest.mark.anyio
async def test_list_objects_by_ids_error(sfdc_client, mocker):
    object_ids = ["001XXXXXXXXXXXX", "001YYYYYYYYYYYY"]
    mocker.patch.object(
        sfdc_client,
        "list_objects",
        side_effect=SalesforceClientError("Failed to execute query"),
    )
    with pytest.raises(
        SalesforceClientError, match="Failed to list Account objects by IDs"
    ):
        await sfdc_client.list_objects_by_ids(
            object_type="Account", object_ids=object_ids
        )


@pytest.mark.anyio
async def test_list_objects_by_ids_and_owner_success(sfdc_client, mocker):
    object_ids = ["001XXXXXXXXXXXX", "001YYYYYYYYYYYY"]
    owner_id = "005XXXXXXXXXXXX"
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    expected_records = [
        {"Id": "001XXXXXXXXXXXX", "Name": "Account 1", "OwnerId": owner_id},
        {"Id": "001YYYYYYYYYYYY", "Name": "Account 2", "OwnerId": owner_id},
    ]
    list_objects_mock.return_value = expected_records
    result = await sfdc_client.list_objects_by_ids_and_owner(
        object_type="Account",
        object_ids=object_ids,
        owner_id=owner_id,
        fields=["Id", "Name", "OwnerId"],
    )
    assert result == expected_records
    expected_where_clause = (
        f"Id IN ('001XXXXXXXXXXXX', '001YYYYYYYYYYYY') AND OwnerId = '{owner_id}'"
    )
    list_objects_mock.assert_called_once_with(
        object_type="Account",
        fields=["Id", "Name", "OwnerId"],
        where_clause=expected_where_clause,
        limit=2,
    )


@pytest.mark.anyio
async def test_list_objects_by_ids_and_owner_with_additional_filters(
    sfdc_client, mocker
):
    object_ids = ["006XXXXXXXXXXXX"]
    owner_id = "005XXXXXXXXXXXX"
    additional_filters = "Amount > 10000"
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    expected_records = [
        {"Id": "006XXXXXXXXXXXX", "Name": "Opportunity 1", "Amount": 15000},
    ]
    list_objects_mock.return_value = expected_records
    result = await sfdc_client.list_objects_by_ids_and_owner(
        object_type="Opportunity",
        object_ids=object_ids,
        owner_id=owner_id,
        fields=["Id", "Name", "Amount"],
        additional_filters=additional_filters,
    )
    assert result == expected_records
    expected_where_clause = (
        f"Id IN ('006XXXXXXXXXXXX') AND OwnerId = '{owner_id}' AND {additional_filters}"
    )
    list_objects_mock.assert_called_once_with(
        object_type="Opportunity",
        fields=["Id", "Name", "Amount"],
        where_clause=expected_where_clause,
        limit=1,
    )


@pytest.mark.anyio
async def test_list_objects_by_ids_and_owner_empty_list(sfdc_client, mocker):
    list_objects_mock = mocker.patch.object(sfdc_client, "list_objects")
    result = await sfdc_client.list_objects_by_ids_and_owner(
        object_type="Account", object_ids=[], owner_id="005XXXXXXXXXXXX"
    )
    assert result == []
    list_objects_mock.assert_not_called()


@pytest.mark.anyio
async def test_list_objects_by_ids_and_owner_error(sfdc_client, mocker):
    object_ids = ["001XXXXXXXXXXXX"]
    owner_id = "005XXXXXXXXXXXX"
    mocker.patch.object(
        sfdc_client,
        "list_objects",
        side_effect=SalesforceClientError("Failed to execute query"),
    )
    with pytest.raises(
        SalesforceClientError, match="Failed to list Account objects by IDs and owner"
    ):
        await sfdc_client.list_objects_by_ids_and_owner(
            object_type="Account", object_ids=object_ids, owner_id=owner_id
        )
