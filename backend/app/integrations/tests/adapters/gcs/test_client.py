from io import BytesIO
from unittest.mock import Mock

import pytest

from app.integrations.adapters.gcs.client import GCSClient, GCSClientError


@pytest.fixture
def valid_credentials():
    return {
        "type": "service_account",
        "project_id": "test-project",
        "private_key_id": "test-key-id",
        "private_key": "abcd",
    }


@pytest.fixture
def gcs_client(mocker, valid_credentials):
    """Create a GCSClient instance with mocked GCS dependencies."""
    mock_credentials = Mock()
    mock_client = Mock()
    mock_bucket = Mock()
    mock_blob = Mock()

    # Mock the credentials creation
    mocker.patch(
        "app.integrations.adapters.gcs.client.Credentials.from_service_account_info",
        return_value=mock_credentials,
    )

    # Mock the GCS client
    mocker.patch(
        "app.integrations.adapters.gcs.client.Client", return_value=mock_client
    )

    # Set up the mock chain: client -> bucket -> blob
    mock_client.bucket.return_value = mock_bucket
    mock_bucket.blob.return_value = mock_blob
    mock_bucket.list_blobs.return_value = [mock_blob]

    client = GCSClient(valid_credentials)

    # Attach mocks to client for easy access in tests
    client._mock_client = mock_client
    client._mock_bucket = mock_bucket
    client._mock_blob = mock_blob

    return client


class TestGCSClient:
    def test_initialization_with_invalid_credentials(self, mocker):
        mocker.patch(
            "app.integrations.adapters.gcs.client.Credentials.from_service_account_info",
            side_effect=Exception("Invalid credentials"),
        )

        with pytest.raises(Exception, match="Invalid credentials"):
            GCSClient({"invalid": "credentials"})

    def test_create_client_failure(self, mocker, valid_credentials):
        mock_credentials = Mock()
        mocker.patch(
            "app.integrations.adapters.gcs.client.Credentials.from_service_account_info",
            return_value=mock_credentials,
        )
        mocker.patch(
            "app.integrations.adapters.gcs.client.Client",
            side_effect=Exception("Client creation failed"),
        )

        client = GCSClient(valid_credentials)

        with pytest.raises(GCSClientError, match="Client initialization failed"):
            client._create_client()

    @pytest.mark.anyio
    async def test_upload_file_success(self, gcs_client):
        file_obj = BytesIO(b"test content")

        await gcs_client.upload_file("test-bucket", file_obj, "test-file.txt")

        gcs_client._mock_client.bucket.assert_called_once_with("test-bucket")
        gcs_client._mock_bucket.blob.assert_called_once_with("test-file.txt")
        gcs_client._mock_blob.upload_from_file.assert_called_once_with(
            file_obj, content_type="application/octet-stream"
        )

    @pytest.mark.anyio
    async def test_upload_file_failure(self, gcs_client):
        gcs_client._mock_blob.upload_from_file.side_effect = Exception("Upload failed")
        file_obj = BytesIO(b"test content")

        with pytest.raises(GCSClientError, match="Failed to upload test-file.txt"):
            await gcs_client.upload_file("test-bucket", file_obj, "test-file.txt")

    @pytest.mark.anyio
    async def test_download_file_success(self, gcs_client):
        gcs_client._mock_blob.download_as_bytes.return_value = b"downloaded content"

        result = await gcs_client.download_file("test-bucket", "test-file.txt")

        gcs_client._mock_client.bucket.assert_called_once_with("test-bucket")
        gcs_client._mock_bucket.blob.assert_called_once_with("test-file.txt")
        gcs_client._mock_blob.download_as_bytes.assert_called_once()
        assert result == b"downloaded content"

    @pytest.mark.anyio
    async def test_download_file_failure(self, gcs_client):
        gcs_client._mock_blob.download_as_bytes.side_effect = Exception(
            "Download failed"
        )

        with pytest.raises(GCSClientError, match="Failed to download test-file.txt"):
            await gcs_client.download_file("test-bucket", "test-file.txt")

    @pytest.mark.anyio
    async def test_delete_file_success(self, gcs_client):
        await gcs_client.delete_file("test-bucket", "test-file.txt")

        gcs_client._mock_client.bucket.assert_called_once_with("test-bucket")
        gcs_client._mock_bucket.blob.assert_called_once_with("test-file.txt")
        gcs_client._mock_blob.delete.assert_called_once()

    @pytest.mark.anyio
    async def test_delete_file_failure(self, gcs_client):
        gcs_client._mock_blob.delete.side_effect = Exception("Delete failed")

        with pytest.raises(GCSClientError, match="Failed to delete test-file.txt"):
            await gcs_client.delete_file("test-bucket", "test-file.txt")

    @pytest.mark.anyio
    async def test_list_files_success(self, gcs_client):
        mock_blob1 = Mock()
        mock_blob1.name = "file1.txt"
        mock_blob1.size = 100
        mock_blob2 = Mock()
        mock_blob2.name = "file2.pdf"
        mock_blob2.size = 200

        gcs_client._mock_bucket.list_blobs.return_value = [mock_blob1, mock_blob2]

        result = await gcs_client.list_files("test-bucket")

        gcs_client._mock_client.bucket.assert_called_once_with("test-bucket")
        gcs_client._mock_bucket.list_blobs.assert_called_once_with()

        assert len(result) == 2
        assert result[0].name == "file1.txt"
        assert result[1].name == "file2.pdf"

    @pytest.mark.anyio
    async def test_list_files_failure(self, gcs_client):
        gcs_client._mock_bucket.list_blobs.side_effect = Exception("List failed")

        with pytest.raises(GCSClientError, match="Failed to list files"):
            await gcs_client.list_files("test-bucket")

    @pytest.mark.anyio
    async def test_create_bucket_success(self, gcs_client):
        bucket_name = "test-bucket"
        location = "EU"

        await gcs_client.create_bucket(bucket_name, location)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_client.create_bucket.assert_called_once_with(
            gcs_client._mock_bucket, location=location
        )

    @pytest.mark.anyio
    async def test_create_bucket_success_default_location(self, gcs_client):
        bucket_name = "test-bucket"

        await gcs_client.create_bucket(bucket_name)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_client.create_bucket.assert_called_once_with(
            gcs_client._mock_bucket, location="EU"
        )

    @pytest.mark.anyio
    async def test_create_bucket_already_exists(self, gcs_client):
        bucket_name = "existing-bucket"
        gcs_client._mock_client.create_bucket.side_effect = Exception(
            "Bucket already exists"
        )

        await gcs_client.create_bucket(bucket_name)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_client.create_bucket.assert_called_once_with(
            gcs_client._mock_bucket, location="EU"
        )

    @pytest.mark.anyio
    async def test_create_bucket_conflict_error(self, gcs_client):
        bucket_name = "conflicting-bucket"
        gcs_client._mock_client.create_bucket.side_effect = Exception("409 Conflict")

        await gcs_client.create_bucket(bucket_name)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_client.create_bucket.assert_called_once_with(
            gcs_client._mock_bucket, location="EU"
        )

    @pytest.mark.anyio
    async def test_bucket_exists_true(self, gcs_client):
        bucket_name = "existing-bucket"
        gcs_client._mock_bucket.exists.return_value = True

        result = await gcs_client.bucket_exists(bucket_name)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_bucket.exists.assert_called_once()
        assert result is True

    @pytest.mark.anyio
    async def test_bucket_exists_false(self, gcs_client):
        bucket_name = "nonexistent-bucket"
        gcs_client._mock_bucket.exists.return_value = False

        result = await gcs_client.bucket_exists(bucket_name)

        gcs_client._mock_client.bucket.assert_called_once_with(bucket_name)
        gcs_client._mock_bucket.exists.assert_called_once()
        assert result is False
