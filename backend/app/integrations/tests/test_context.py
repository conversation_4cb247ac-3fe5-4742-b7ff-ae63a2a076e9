import uuid

import pytest
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.context import BaseContext
from app.integrations.base.credentials_resolver import ICredentialsResolver
from app.integrations.context import (
    IntegrationContext,
    ManagedAsyncSession,
    create_context,
)


@pytest.fixture
def tenant_id():
    return uuid.uuid4()


@pytest.fixture
def db_session_factory(mocker):
    return lambda: mocker.Mock(spec=AsyncSession)


@pytest.fixture
def mock_session(mocker):
    return mocker.Mock(spec=AsyncSession)


@pytest.fixture
def mock_credentials_resolver(mocker):
    return mocker.Mock(spec=ICredentialsResolver)


def test_integration_context(tenant_id, db_session_factory, mock_credentials_resolver):
    def managed_factory():
        return ManagedAsyncSession(db_session_factory(), can_close=True)

    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=managed_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert context.tenant_id == tenant_id
    assert context.credentials_resolver == mock_credentials_resolver
    assert isinstance(context, BaseContext)

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)


def test_integration_context_without_credentials(tenant_id, db_session_factory):
    def managed_factory():
        return ManagedAsyncSession(db_session_factory(), can_close=True)

    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=managed_factory,
    )

    assert context.tenant_id == tenant_id
    assert context.credentials_resolver is None

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)


def test_integration_context_with_session_factory(
    tenant_id, db_session_factory, mock_credentials_resolver
):
    def managed_factory():
        return ManagedAsyncSession(db_session_factory(), can_close=True)

    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=managed_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert context.tenant_id == tenant_id
    assert context.credentials_resolver == mock_credentials_resolver
    assert isinstance(context, BaseContext)

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)


def test_integration_context_with_shared_session(
    tenant_id, mock_session, mock_credentials_resolver
):
    def managed_factory():
        return ManagedAsyncSession(mock_session, can_close=False)

    context = IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=managed_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert context.tenant_id == tenant_id
    assert context.credentials_resolver == mock_credentials_resolver

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)


@pytest.mark.anyio
async def test_managed_async_session_closable(mock_session):
    managed_session = ManagedAsyncSession(mock_session, can_close=True)
    await managed_session.close()
    mock_session.close.assert_called_once()


@pytest.mark.anyio
async def test_managed_async_session_non_closable(mock_session):
    managed_session = ManagedAsyncSession(mock_session, can_close=False)
    await managed_session.close()
    mock_session.close.assert_not_called()


def test_create_context_validation_errors(tenant_id, mocker):
    with pytest.raises(
        ValueError,
        match="Exactly one of db_session or db_session_factory must be provided",
    ):
        create_context(tenant_id=tenant_id)

    with pytest.raises(
        ValueError,
        match="Exactly one of db_session or db_session_factory must be provided",
    ):
        create_context(
            tenant_id=tenant_id,
            db_session=mocker.Mock(spec=AsyncSession),
            db_session_factory=lambda: mocker.Mock(spec=AsyncSession),
        )


def test_create_context_with_factory(
    tenant_id, db_session_factory, mock_credentials_resolver
):
    context = create_context(
        tenant_id=tenant_id,
        db_session_factory=db_session_factory,
        credentials_resolver=mock_credentials_resolver,
    )

    assert isinstance(context, IntegrationContext)
    assert context.tenant_id == tenant_id
    assert context.credentials_resolver == mock_credentials_resolver

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)


@pytest.mark.anyio
async def test_create_context_with_session(
    tenant_id, mock_session, mock_credentials_resolver
):
    context = create_context(
        tenant_id=tenant_id,
        db_session=mock_session,
        credentials_resolver=mock_credentials_resolver,
    )

    assert isinstance(context, IntegrationContext)
    assert context.tenant_id == tenant_id
    assert context.credentials_resolver == mock_credentials_resolver

    session = context.db_session_factory()
    assert isinstance(session, ManagedAsyncSession)

    await session.close()
    mock_session.close.assert_not_called()
