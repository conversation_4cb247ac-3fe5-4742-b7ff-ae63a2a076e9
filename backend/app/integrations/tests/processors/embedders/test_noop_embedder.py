import pytest

from app.integrations.processors.embedders.noop_embedder import <PERSON>opEmbedder


@pytest.mark.anyio
async def test_noop_embedder_returns_correct_shape_default():
    embedder = NoopEmbedder()
    vector = await embedder.embed_text("whatever")
    assert isinstance(vector, list)
    assert len(vector) == 1536
    assert all(isinstance(x, float) for x in vector)


@pytest.mark.anyio
async def test_noop_embedder_custom_dimension():
    embedder = NoopEmbedder(dim=1536)
    vector = await embedder.embed_text("test")
    assert len(vector) == 1536
