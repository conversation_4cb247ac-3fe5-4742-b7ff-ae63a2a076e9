from unittest.mock import AsyncMock

import pytest

from app.integrations.processors.parsers.file_parser import FileParser
from app.integrations.types import ExtensionType


@pytest.fixture
def file_parser():
    return FileParser(mistral_api_key="test")


@pytest.mark.anyio
async def test_parse_txt_file_success(file_parser):
    file_data = b"Hello, World!"
    result = await file_parser.parse(file_data, ExtensionType.TXT, "text/plain")
    assert result == "Hello, World!"


@pytest.mark.anyio
async def test_parse_empty_file(file_parser):
    with pytest.raises(ValueError, match="File data is empty"):
        await file_parser.parse(b"", ExtensionType.TXT, "text/plain")


@pytest.mark.anyio
async def test_parse_unsupported_extension(file_parser):
    with pytest.raises(
        ValueError, match=f"Unsupported file extension: {ExtensionType.MP4}"
    ):
        await file_parser.parse(b"content", ExtensionType.MP4, "video/mp4")


@pytest.mark.anyio
async def test_parse_pdf_file_success(file_parser, mocker):
    mock_ocr_result = "# Document Title\n\nThis is the content of the PDF document."
    mocker.patch.object(
        file_parser.mistral_ocr,
        "parse",
        new_callable=AsyncMock,
        return_value=mock_ocr_result,
    )

    file_data = b"fake_pdf_content"
    result = await file_parser.parse(file_data, ExtensionType.PDF, "application/pdf")

    assert result == mock_ocr_result

    file_parser.mistral_ocr.parse.assert_called_once_with(file_data, "application/pdf")
