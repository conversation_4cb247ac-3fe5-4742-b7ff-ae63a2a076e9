import datetime
import uuid

import pytest
from sqlalchemy import delete, select

from app.integrations.models import Message<PERSON><PERSON><PERSON>g, MessageRawData
from app.integrations.schemas import ChannelDataSlice, MessageData, ReconciliationStats
from app.integrations.stores.pg_message_store import PostgresMessageStore
from app.integrations.types import IntegrationSource


@pytest.fixture
def store(async_db_session) -> PostgresMessageStore:
    tenant_id = uuid.uuid4()
    return PostgresMessageStore(
        tenant_id=tenant_id,
        session=async_db_session,
        source=IntegrationSource.SLACK,
    )


@pytest.fixture
def store_without_changelog(async_db_session) -> PostgresMessageStore:
    tenant_id = uuid.uuid4()
    return PostgresMessageStore(
        tenant_id=tenant_id,
        session=async_db_session,
        source=IntegrationSource.SLACK,
        with_changelog=False,
    )


@pytest.mark.anyio
async def test_insert_new_message(store):
    message = _create_message_data(
        message_id="test_msg",
        content="Test Content",
        author="test_user",
        thread_id="test_thread",
        parent_id="test_parent",
        tombstone=False,
        last_edit_at=datetime.datetime.now(),
    )

    await store.upsert_message(message)
    retrieved = await store.get_message(message.message_id)

    assert retrieved is not None
    assert retrieved.message_id == message.message_id
    assert retrieved.content == message.content
    assert retrieved.author == message.author
    assert retrieved.thread_id == message.thread_id
    assert retrieved.parent_id == message.parent_id
    assert retrieved.tombstone == message.tombstone
    assert retrieved.last_edit_at == message.last_edit_at


@pytest.mark.anyio
async def test_update_existing_message(store):
    message = _create_message_data(
        message_id="msg3",
        content="Original Content",
        author="original_user",
        thread_id="original_thread",
        parent_id="original_parent",
        tombstone=False,
    )
    await store.upsert_message(message)

    updated_message = _create_message_data(
        message_id="msg3",
        content="Updated Content",
        author="updated_user",
        thread_id="updated_thread",
        parent_id="updated_parent",
        tombstone=True,
        last_edit_at=datetime.datetime.now(),
    )
    await store.upsert_message(updated_message)

    retrieved = await store.get_message(updated_message.message_id)
    assert retrieved is not None
    assert retrieved.content == "Updated Content"
    assert retrieved.author == "updated_user"
    assert retrieved.thread_id == "updated_thread"
    assert retrieved.parent_id == "updated_parent"
    assert retrieved.tombstone == True
    assert retrieved.last_edit_at is not None


@pytest.mark.anyio
async def test_delete_message(store):
    message = _create_message_data(message_id="msg4")
    await store.upsert_message(message)

    result = await store.delete_message(message.message_id)
    assert result == True

    retrieved = await store.get_message(message.message_id)
    assert retrieved is None


@pytest.mark.anyio
async def test_delete_nonexistent_message(store):
    result = await store.delete_message("nonexistent-message")
    assert result == False


@pytest.mark.anyio
async def test_tombstone_update(store):
    message = _create_message_data(message_id="msg5", tombstone=False)
    await store.upsert_message(message)

    tombstone_message = _create_message_data(message_id="msg5", tombstone=True)
    await store.upsert_message(tombstone_message)

    retrieved = await store.get_message(tombstone_message.message_id)
    assert retrieved is not None
    assert retrieved.tombstone is True


@pytest.mark.anyio
async def test_get_message(store):
    msg = _create_message_data(message_id="test_msg", channel_id="general")
    await store.upsert_message(msg)

    retrieved = await store.get_message(msg.message_id)
    assert retrieved is not None
    assert retrieved.message_id == msg.message_id
    assert retrieved.channel_id == msg.channel_id


@pytest.mark.anyio
async def test_get_message_by_id(store, async_db_session):
    msg = _create_message_data(message_id="msg_by_id", channel_id="random")
    await store.upsert_message(msg)

    stmt = select(MessageRawData).filter(
        MessageRawData.message_id == msg.message_id,
        MessageRawData.channel_id == msg.channel_id,
        MessageRawData.tenant_id == store.tenant_id,
    )
    result = await async_db_session.execute(stmt)
    record = result.scalar_one_or_none()
    assert record is not None

    retrieved = await store.get_message_by_id(record.id)
    assert retrieved is not None
    assert retrieved.message_id == msg.message_id


@pytest.mark.anyio
async def test_get_main_messages_before(store):
    main_msg1 = _create_message_data(
        message_id="main1", sent_at=datetime.datetime(2023, 1, 1)
    )
    main_msg2 = _create_message_data(
        message_id="main2", sent_at=datetime.datetime(2023, 1, 2)
    )
    main_msg3 = _create_message_data(
        message_id="main3", sent_at=datetime.datetime(2023, 1, 3)
    )
    reply1 = _create_message_data(
        message_id="reply1",
        thread_id="main2",
        sent_at=datetime.datetime(2023, 1, 2, 12),
    )

    await store.upsert_message(main_msg1)
    await store.upsert_message(main_msg2)
    await store.upsert_message(main_msg3)
    await store.upsert_message(reply1)

    messages = await store.get_main_messages_before(
        channel_id="general", message_id="main2", limit=1
    )

    assert len(messages) == 1
    assert messages[0].message_id == "main1"


@pytest.mark.anyio
async def test_get_main_messages_after(store):
    main_msg1 = _create_message_data(
        message_id="main1", sent_at=datetime.datetime(2023, 1, 1)
    )
    main_msg2 = _create_message_data(
        message_id="main2", sent_at=datetime.datetime(2023, 1, 2)
    )
    main_msg3 = _create_message_data(
        message_id="main3", sent_at=datetime.datetime(2023, 1, 3)
    )
    reply1 = _create_message_data(
        message_id="reply1",
        thread_id="main2",
        sent_at=datetime.datetime(2023, 1, 2, 12),
    )

    await store.upsert_message(main_msg1)
    await store.upsert_message(main_msg2)
    await store.upsert_message(main_msg3)
    await store.upsert_message(reply1)

    messages = await store.get_main_messages_after(
        channel_id="general", message_id="main2", limit=1
    )

    assert len(messages) == 1
    assert messages[0].message_id == "main3"


@pytest.mark.anyio
async def test_get_thread_replies(store):
    thread_root = _create_message_data(message_id="thread1", thread_id="thread1")
    reply1 = _create_message_data(message_id="reply1", thread_id="thread1")
    reply2 = _create_message_data(message_id="reply2", thread_id="thread1")

    await store.upsert_message(thread_root)
    await store.upsert_message(reply1)
    await store.upsert_message(reply2)

    replies = await store.get_thread_replies(channel_id="general", message_id="thread1")

    assert len(replies) == 2
    reply_ids = [r.message_id for r in replies]
    assert "reply1" in reply_ids
    assert "reply2" in reply_ids


@pytest.mark.anyio
async def test_get_main_messages_around(store):
    main_msg1 = _create_message_data(
        message_id="main1", sent_at=datetime.datetime(2023, 1, 1)
    )
    date1 = datetime.datetime(2023, 1, 2)
    main_msg2 = _create_message_data(message_id="main2", sent_at=date1)
    main_msg3 = _create_message_data(message_id="main3", sent_at=date1)
    date2 = datetime.datetime(2023, 1, 4)
    main_msg4 = _create_message_data(message_id="main4", sent_at=date2)
    main_msg5 = _create_message_data(message_id="main5", sent_at=date2)
    reply1 = _create_message_data(
        message_id="reply1",
        thread_id="main2",
        sent_at=datetime.datetime(2023, 1, 2, 12),
    )

    await store.upsert_message(main_msg1)
    await store.upsert_message(main_msg2)
    await store.upsert_message(main_msg3)
    await store.upsert_message(main_msg4)
    await store.upsert_message(main_msg5)
    await store.upsert_message(reply1)

    before_messages = await store._get_main_messages_around(
        channel_id="general", message_id="main3", limit=2, after=False
    )
    assert len(before_messages) == 2
    assert before_messages[0]["message_id"] == "main2"
    assert before_messages[1]["message_id"] == "main1"

    after_messages = await store._get_main_messages_around(
        channel_id="general", message_id="main2", limit=2, after=True
    )
    assert len(after_messages) == 2
    assert after_messages[0]["message_id"] == "main3"
    assert after_messages[1]["message_id"] == "main4"

    after_messages = await store._get_main_messages_around(
        channel_id="general", message_id="main4", limit=2, after=True
    )
    assert len(after_messages) == 1
    assert after_messages[0]["message_id"] == "main5"


@pytest.mark.anyio
async def test_get_main_messages_around_different_channels(store):
    date = datetime.datetime(2023, 1, 1)
    main_msg1 = _create_message_data(
        message_id="main1", channel_id="channel1", sent_at=date
    )
    main_msg2 = _create_message_data(
        message_id="main2", channel_id="channel1", sent_at=date
    )
    main_msg3 = _create_message_data(
        message_id="main3", channel_id="channel2", sent_at=date
    )
    main_msg4 = _create_message_data(
        message_id="main4", channel_id="channel2", sent_at=date
    )

    await store.upsert_message(main_msg1)
    await store.upsert_message(main_msg2)
    await store.upsert_message(main_msg3)
    await store.upsert_message(main_msg4)

    messages_channel1 = await store._get_main_messages_around(
        channel_id="channel1", message_id="main1", limit=2, after=True
    )
    assert len(messages_channel1) == 1
    assert messages_channel1[0]["message_id"] == "main2"

    messages_channel2 = await store._get_main_messages_around(
        channel_id="channel2", message_id="main3", limit=2, after=True
    )
    assert len(messages_channel2) == 1
    assert messages_channel2[0]["message_id"] == "main4"

    messages_channel1_limited = await store._get_main_messages_around(
        channel_id="channel1", message_id="main1", limit=10, after=True
    )
    assert len(messages_channel1_limited) == 1
    assert all(msg["message_id"] != "main3" for msg in messages_channel1_limited)
    assert all(msg["message_id"] != "main4" for msg in messages_channel1_limited)


@pytest.mark.anyio
async def test_reconcile_channel_messages(store, mocker):
    channel_id = "test_channel"
    now = datetime.datetime.now()
    from_time = now - datetime.timedelta(hours=1)
    to_time = now

    # Create a ChannelDataSlice with test messages
    source_msgs = [
        _create_message_data(message_id="new_msg", sent_at=now),
        _create_message_data(
            message_id="update_msg", content="Updated Content", sent_at=now
        ),
        _create_message_data(
            message_id="existing_msg", content="Existing Content", sent_at=now
        ),
    ]

    source_slice = ChannelDataSlice(
        channel_id=channel_id,
        messages=source_msgs,
        from_time=from_time,
        to_time=to_time,
    )

    # Mock internal methods
    mock_diff_messages = mocker.patch.object(store, "_diff_messages")
    mock_apply_diff = mocker.patch.object(store, "_apply_diff")

    messages_to_insert = [_create_message_data(message_id="new_msg", sent_at=now)]
    messages_to_update = [
        _create_message_data(
            message_id="update_msg", content="Updated Content", sent_at=now
        )
    ]
    message_ids_to_delete = ["old_msg"]

    mock_diff_messages.return_value = (
        messages_to_insert,
        messages_to_update,
        message_ids_to_delete,
    )

    stats = await store.reconcile_channel_messages(source_slice)

    mock_diff_messages.assert_called_once_with(source_slice=source_slice)
    mock_apply_diff.assert_called_once_with(
        channel_id,
        messages_to_insert,
        messages_to_update,
        message_ids_to_delete,
    )
    assert stats == ReconciliationStats(
        inserts=len(messages_to_insert),
        updates=len(messages_to_update),
        deletes=len(message_ids_to_delete),
    )


@pytest.mark.anyio
async def test_compute_hash(store):
    message1 = _create_message_data(content="Test content")
    message2 = _create_message_data(content="Test content")
    message3 = _create_message_data(content="Other content", tombstone=False)
    message4 = _create_message_data(content="Other content", tombstone=True)

    hash1 = store._compute_hash(message1)
    hash2 = store._compute_hash(message2)
    hash3 = store._compute_hash(message3)
    hash4 = store._compute_hash(message4)

    assert hash1 == hash2
    assert hash1 != hash3
    assert hash4 != hash3
    assert isinstance(hash1, str)


@pytest.mark.anyio
async def test_find_inserts_and_updates(store, mocker):
    # Define source messages
    source_msgs = [
        _create_message_data(message_id="new_msg", content="New message"),
        _create_message_data(message_id="update_msg", content="Updated content"),
        _create_message_data(message_id="unchanged", content="Unchanged"),
        _create_message_data(
            message_id="tombstone", content="Unchanged", tombstone=True
        ),
    ]

    # Mock the _get_message_from_db method to return specific values
    async def mock_get_message_from_db(message_id):
        db_msgs = {
            "update_msg": {
                "message_id": "update_msg",
                "thread_id": None,
                "hash": store._compute_hash(
                    _create_message_data(content="Old content")
                ),
                "tombstone": False,
                "sent_at": datetime.datetime.now(),
            },
            "unchanged": {
                "message_id": "unchanged",
                "thread_id": None,
                "hash": store._compute_hash(_create_message_data(content="Unchanged")),
                "tombstone": False,
                "sent_at": datetime.datetime.now(),
            },
            "tombstone": {
                "message_id": "tombstone",
                "thread_id": None,
                "hash": store._compute_hash(_create_message_data(content="Unchanged")),
                "tombstone": False,
                "sent_at": datetime.datetime.now(),
            },
        }
        return db_msgs.get(message_id)

    mocker.patch.object(
        store, "_get_message_from_db", side_effect=mock_get_message_from_db
    )

    messages_to_insert = []
    messages_to_update = []

    await store._find_inserts_and_updates(
        source_msgs, messages_to_insert, messages_to_update
    )

    assert len(messages_to_insert) == 1
    assert len(messages_to_update) == 2

    assert messages_to_insert[0].message_id == "new_msg"

    messages_to_update_ids = [m.message_id for m in messages_to_update]
    assert "update_msg" in messages_to_update_ids
    assert "tombstone" in messages_to_update_ids


@pytest.mark.anyio
async def test_find_deletions(store, mocker):
    channel_id = "test_channel"
    now = datetime.datetime.now()
    from_time = now - datetime.timedelta(hours=1)
    to_time = now

    # Create source messages
    source_msgs = [
        _create_message_data(message_id="existing_thread_root", thread_id=None),
        _create_message_data(
            message_id="existing_thread_reply", thread_id="existing_thread_root"
        ),
    ]

    # Create ChannelDataSlice
    source_slice = ChannelDataSlice(
        channel_id=channel_id,
        messages=source_msgs,
        from_time=from_time,
        to_time=to_time,
    )

    # Mock _get_stale_main_message_ids_from_db with time parameters
    stale_main_message_ids = {"missing_thread_root_old"}
    mock_get_stale = mocker.patch.object(
        store,
        "_get_stale_main_message_ids_from_db",
        return_value=stale_main_message_ids,
    )

    # Mock _get_thread_message_ids_from_db
    stale_thread_messages = [
        "missing_thread_root_old",
        "missing_thread_reply1_recent",
        "missing_thread_reply2_old",
    ]
    mocker.patch.object(
        store, "_get_thread_message_ids_from_db", return_value=stale_thread_messages
    )

    # Mock _get_stale_reply_ids_from_db
    async def mock_get_stale_reply_ids_from_db(main_message_id, _source_reply_ids):
        if main_message_id == "existing_thread_root":
            return {
                "missing_existing_thread_reply1_old",
                "missing_existing_thread_reply2",
            }
        return set()

    mocker.patch.object(
        store,
        "_get_stale_reply_ids_from_db",
        side_effect=mock_get_stale_reply_ids_from_db,
    )

    # Execute the test
    messages_to_delete = []
    await store._find_deletions(source_slice, messages_to_delete)

    # Verify that _get_stale_main_message_ids_from_db was called with the correct time parameters
    source_main_message_ids = {
        msg.message_id
        for msg in source_slice.messages
        if msg.thread_id is None or msg.message_id == msg.thread_id
    }
    mock_get_stale.assert_called_once_with(
        source_main_message_ids, channel_id, from_time, to_time
    )

    # Verify that the correct messages were marked for deletion
    assert len(messages_to_delete) == 5

    # Stale messages from the missing thread
    for msg in stale_thread_messages:
        assert msg in messages_to_delete

    # Orphaned replies
    assert "missing_existing_thread_reply1_old" in messages_to_delete
    assert "missing_existing_thread_reply2" in messages_to_delete


@pytest.mark.anyio
async def test_get_stale_main_message_ids_from_db(store):
    # Create main messages in the database with different timestamps
    channel_id = "test_channel"
    now = datetime.datetime.now()
    two_days_ago = now - datetime.timedelta(days=2)
    one_day_ago = now - datetime.timedelta(days=1)

    # Messages in different time periods
    main_msg1 = _create_message_data(
        message_id="main1", channel_id=channel_id, thread_id=None, sent_at=one_day_ago
    )
    main_msg2 = _create_message_data(
        message_id="main2",
        channel_id=channel_id,
        thread_id="main2",
        sent_at=one_day_ago,
    )
    main_msg3 = _create_message_data(
        message_id="main3", channel_id=channel_id, thread_id=None, sent_at=one_day_ago
    )
    # This message is outside the time period (too old)
    main_msg4 = _create_message_data(
        message_id="main4", channel_id=channel_id, thread_id=None, sent_at=two_days_ago
    )

    # Store the messages
    await store.upsert_message(main_msg1)
    await store.upsert_message(main_msg2)
    await store.upsert_message(main_msg3)
    await store.upsert_message(main_msg4)

    # Define the time period and the set of source main message IDs
    from_time = now - datetime.timedelta(days=1, hours=12)  # 1.5 days before now
    to_time = now
    source_main_message_ids = {"main1", "main2"}  # doesn't include main3 or main4

    # Call the method being tested
    stale_main_message_ids = await store._get_stale_main_message_ids_from_db(
        source_main_message_ids, channel_id, from_time, to_time
    )

    # Verify that main3 is identified as stale because it's in the time period but not in source_main_message_ids
    # main4 should not be identified because it's outside the time period
    assert "main3" in stale_main_message_ids
    assert "main4" not in stale_main_message_ids
    assert len(stale_main_message_ids) == 1


@pytest.mark.anyio
async def test_get_thread_message_ids_from_db(store):
    # Create a main message and replies in the database
    channel_id = "test_channel"
    main_msg = _create_message_data(
        message_id="thread_root", channel_id=channel_id, thread_id="thread_root"
    )
    reply1 = _create_message_data(
        message_id="reply1", channel_id=channel_id, thread_id="thread_root"
    )
    reply2 = _create_message_data(
        message_id="reply2", channel_id=channel_id, thread_id="thread_root"
    )

    # Store the messages
    await store.upsert_message(main_msg)
    await store.upsert_message(reply1)
    await store.upsert_message(reply2)

    # Call the method being tested
    thread_messages = await store._get_thread_message_ids_from_db(
        {"thread_root"}, channel_id
    )

    # Verify that all thread messages are retrieved
    assert len(thread_messages) == 3
    assert "thread_root" in thread_messages
    assert "reply1" in thread_messages
    assert "reply2" in thread_messages


@pytest.mark.anyio
async def test_get_stale_reply_ids_from_db(store):
    # Create a main message and replies in the database
    channel_id = "test_channel"
    main_msg = _create_message_data(
        message_id="thread_root", channel_id=channel_id, thread_id="thread_root"
    )
    reply1 = _create_message_data(
        message_id="reply1", channel_id=channel_id, thread_id="thread_root"
    )
    reply2 = _create_message_data(
        message_id="reply2", channel_id=channel_id, thread_id="thread_root"
    )
    reply3 = _create_message_data(
        message_id="reply3", channel_id=channel_id, thread_id="thread_root"
    )

    # Store the messages
    await store.upsert_message(main_msg)
    await store.upsert_message(reply1)
    await store.upsert_message(reply2)
    await store.upsert_message(reply3)

    # Define the set of source reply IDs (doesn't include reply3)
    source_reply_ids = {"reply1", "reply2"}

    # Call the method being tested
    stale_reply_ids = await store._get_stale_reply_ids_from_db(
        "thread_root", source_reply_ids
    )

    # Verify that reply3 is identified as stale
    assert "reply3" in stale_reply_ids
    assert len(stale_reply_ids) == 1


@pytest.mark.anyio
async def test_diff_messages(store, mocker):
    now = datetime.datetime.now()
    channel_id = "test_channel"
    from_time = now - datetime.timedelta(hours=1)
    to_time = now

    # Create source messages
    source_msgs = [
        _create_message_data(message_id="new_msg", sent_at=now),
        _create_message_data(
            message_id="update_msg", content="Updated Content", sent_at=now
        ),
        _create_message_data(
            message_id="existing_msg", content="Existing Content", sent_at=now
        ),
    ]

    # Create ChannelDataSlice
    source_slice = ChannelDataSlice(
        channel_id=channel_id,
        messages=source_msgs,
        from_time=from_time,
        to_time=to_time,
    )

    # Mock the methods used by _diff_messages
    mock_find_inserts = mocker.patch.object(store, "_find_inserts_and_updates")
    mock_find_deletions = mocker.patch.object(store, "_find_deletions")

    # Simulate the behavior of _find_inserts_and_updates
    async def fake_find_inserts(_source_messages, inserts, updates):
        inserts.append(_create_message_data(message_id="new_msg"))
        updates.append(_create_message_data(message_id="update_msg"))

    mock_find_inserts.side_effect = fake_find_inserts

    # Simulate the behavior of _find_deletions
    async def fake_find_deletions(_source_slice, deletes):
        deletes.append("missing_msg")

    mock_find_deletions.side_effect = fake_find_deletions

    # Call the method being tested
    (
        messages_to_insert,
        messages_to_update,
        message_ids_to_delete,
    ) = await store._diff_messages(source_slice=source_slice)

    # Verify that the methods were called
    mock_find_inserts.assert_called_once()

    # Don't verify exact parameters as message_ids_to_delete is modified during execution
    assert mock_find_deletions.call_count == 1

    assert len(messages_to_insert) == 1
    assert messages_to_insert[0].message_id == "new_msg"

    assert len(messages_to_update) == 1
    assert messages_to_update[0].message_id == "update_msg"

    assert len(message_ids_to_delete) == 1
    assert message_ids_to_delete[0] == "missing_msg"


@pytest.mark.anyio
async def test_apply_diff(store, mocker):
    # Mock upsert_message and _delete_messages_from_db
    mock_upsert = mocker.patch.object(store, "upsert_message")
    mock_delete = mocker.patch.object(store, "_delete_messages_from_db")
    mock_log_operation = mocker.patch.object(store, "_log_operation")

    # Create test data
    channel_id = "test_channel"
    new_msg = _create_message_data(message_id="new_msg")
    update_msg = _create_message_data(
        message_id="update_msg", content="Updated Content"
    )
    message_ids_to_delete = ["delete_msg"]

    # Call the method being tested
    await store._apply_diff(
        channel_id,
        messages_to_insert=[new_msg],
        messages_to_update=[update_msg],
        message_ids_to_delete=message_ids_to_delete,
    )

    # Verify that the methods were called correctly
    assert mock_upsert.call_count == 2  # Once for insertion, once for update
    mock_delete.assert_called_once_with(message_ids_to_delete)

    # Check _log_operation was called for deletions
    assert mock_log_operation.call_count == len(message_ids_to_delete)
    mock_log_operation.assert_called_with(
        operation=MessageChangelog.Operation.DELETE,
        message_id="delete_msg",
        channel_id=channel_id,
    )


# tests for the changelog functionality


@pytest.mark.anyio
async def test_log_operation_creates_changelog_entry(store, async_db_session):
    message_id = "test_msg"
    channel_id = "test_channel"

    # Call _log_operation
    await store._log_operation(
        operation=MessageChangelog.Operation.INSERT,
        message_id=message_id,
        channel_id=channel_id,
    )

    # Commit to ensure the entry is saved
    await async_db_session.commit()

    # Query the changelog
    stmt = select(MessageChangelog).filter(
        MessageChangelog.message_id == message_id,
        MessageChangelog.channel_id == channel_id,
        MessageChangelog.tenant_id == store.tenant_id,
        MessageChangelog.source == store.source,
    )
    result = await async_db_session.execute(stmt)
    changelog_entry = result.scalar_one_or_none()

    # Verify the entry exists and has correct values
    assert changelog_entry is not None
    assert changelog_entry.operation == MessageChangelog.Operation.INSERT.value
    assert changelog_entry.message_id == message_id
    assert changelog_entry.channel_id == channel_id
    assert changelog_entry.tenant_id == store.tenant_id
    assert changelog_entry.source == store.source
    assert changelog_entry.created_at is not None


@pytest.mark.anyio
async def test_log_operation_skipped_when_changelog_disabled(
    store_without_changelog, async_db_session
):
    message_id = "test_msg"
    channel_id = "test_channel"

    # Call _log_operation
    await store_without_changelog._log_operation(
        operation=MessageChangelog.Operation.INSERT,
        message_id=message_id,
        channel_id=channel_id,
    )

    # Commit to ensure any potential entries would be saved
    await async_db_session.commit()

    # Query the changelog
    stmt = select(MessageChangelog).filter(
        MessageChangelog.message_id == message_id,
        MessageChangelog.channel_id == channel_id,
        MessageChangelog.tenant_id == store_without_changelog.tenant_id,
    )
    result = await async_db_session.execute(stmt)
    changelog_entry = result.scalar_one_or_none()

    # Verify no entry was created
    assert changelog_entry is None


@pytest.mark.anyio
async def test_insert_creates_changelog_entry(store, async_db_session):
    message = _create_message_data(message_id="new_message", channel_id="test_channel")

    # Insert the message
    await store.upsert_message(message)

    # Query the changelog
    stmt = select(MessageChangelog).filter(
        MessageChangelog.message_id == message.message_id,
        MessageChangelog.operation == MessageChangelog.Operation.INSERT.value,
    )
    result = await async_db_session.execute(stmt)
    changelog_entry = result.scalar_one_or_none()

    # Verify the entry exists
    assert changelog_entry is not None
    assert changelog_entry.operation == MessageChangelog.Operation.INSERT.value


@pytest.mark.anyio
async def test_update_creates_changelog_entry(store, async_db_session):
    # Insert original message
    message = _create_message_data(message_id="update_message", content="Original")
    await store.upsert_message(message)

    # Clear the session to ensure clean state
    async_db_session.expunge_all()

    # Update the message
    updated_message = _create_message_data(
        message_id="update_message", content="Updated"
    )
    await store.upsert_message(updated_message)

    # Query the changelog for update entry
    stmt = select(MessageChangelog).filter(
        MessageChangelog.message_id == message.message_id,
        MessageChangelog.operation == MessageChangelog.Operation.UPDATE.value,
    )
    result = await async_db_session.execute(stmt)
    changelog_entry = result.scalar_one_or_none()

    # Verify the entry exists
    assert changelog_entry is not None
    assert changelog_entry.operation == MessageChangelog.Operation.UPDATE.value


@pytest.mark.anyio
async def test_delete_creates_changelog_entry(store, async_db_session):
    # Insert original message
    message = _create_message_data(message_id="delete_message")
    await store.upsert_message(message)

    # Clear the session to ensure clean state
    async_db_session.expunge_all()

    # Delete the message
    await store.delete_message("delete_message")

    # Query the changelog for delete entry
    stmt = select(MessageChangelog).filter(
        MessageChangelog.message_id == message.message_id,
        MessageChangelog.operation == MessageChangelog.Operation.DELETE.value,
    )
    result = await async_db_session.execute(stmt)
    changelog_entry = result.scalar_one_or_none()

    # Verify the entry exists
    assert changelog_entry is not None
    assert changelog_entry.operation == MessageChangelog.Operation.DELETE.value


@pytest.mark.anyio
async def test_reconcile_creates_appropriate_changelog_entries(store, async_db_session):
    channel_id = "reconcile_channel"
    now = datetime.datetime.now()

    # Create some initial messages
    existing_msg = _create_message_data(
        message_id="existing",
        channel_id=channel_id,
        sent_at=now - datetime.timedelta(minutes=30),
    )
    update_msg = _create_message_data(
        message_id="update_target",
        channel_id=channel_id,
        content="Original",
        sent_at=now - datetime.timedelta(minutes=30),
    )
    delete_msg = _create_message_data(
        message_id="delete_target",
        channel_id=channel_id,
        sent_at=now - datetime.timedelta(minutes=30),
    )

    # Store initial messages
    await store.upsert_message(existing_msg)
    await store.upsert_message(update_msg)
    await store.upsert_message(delete_msg)

    # Clear changelog table to start fresh
    await async_db_session.execute(delete(MessageChangelog))
    await async_db_session.commit()

    # Create source slice for reconciliation with updated and new messages, but not the delete target
    source_msgs = [
        existing_msg,  # Unchanged
        _create_message_data(
            message_id="update_target",
            channel_id=channel_id,
            content="Updated",
            sent_at=now - datetime.timedelta(minutes=30),
        ),  # Updated
        _create_message_data(
            message_id="new_message",
            channel_id=channel_id,
            sent_at=now - datetime.timedelta(minutes=15),
        ),  # New
    ]

    source_slice = ChannelDataSlice(
        channel_id=channel_id,
        messages=source_msgs,
        from_time=now - datetime.timedelta(hours=1),
        to_time=now,
    )

    # Perform reconciliation
    await store.reconcile_channel_messages(source_slice)

    # Query changelog entries
    insert_stmt = select(MessageChangelog).filter(
        MessageChangelog.channel_id == channel_id,
        MessageChangelog.operation == MessageChangelog.Operation.INSERT.value,
    )
    insert_result = await async_db_session.execute(insert_stmt)
    insert_entries = insert_result.scalars().all()

    update_stmt = select(MessageChangelog).filter(
        MessageChangelog.channel_id == channel_id,
        MessageChangelog.operation == MessageChangelog.Operation.UPDATE.value,
    )
    update_result = await async_db_session.execute(update_stmt)
    update_entries = update_result.scalars().all()

    delete_stmt = select(MessageChangelog).filter(
        MessageChangelog.channel_id == channel_id,
        MessageChangelog.operation == MessageChangelog.Operation.DELETE.value,
    )
    delete_result = await async_db_session.execute(delete_stmt)
    delete_entries = delete_result.scalars().all()

    # Verify appropriate entries were created
    assert len(insert_entries) == 1
    assert insert_entries[0].message_id == "new_message"

    assert len(update_entries) == 1
    assert update_entries[0].message_id == "update_target"

    assert len(delete_entries) == 1
    assert delete_entries[0].message_id == "delete_target"


@pytest.mark.anyio
async def test_get_channel_changelog(store, async_db_session):
    channel_id = "test_channel"

    changelog_entries = [
        MessageChangelog(
            operation=MessageChangelog.Operation.INSERT.value,
            message_id="msg1",
            channel_id=channel_id,
            tenant_id=store.tenant_id,
            source=store.source,
        ),
        MessageChangelog(
            operation=MessageChangelog.Operation.UPDATE.value,
            message_id="msg2",
            channel_id=channel_id,
            tenant_id=store.tenant_id,
            source=store.source,
        ),
        MessageChangelog(
            operation=MessageChangelog.Operation.DELETE.value,
            message_id="msg3",
            channel_id=channel_id,
            tenant_id=store.tenant_id,
            source=store.source,
        ),
    ]

    for entry in changelog_entries:
        async_db_session.add(entry)
    await async_db_session.commit()

    # Refresh objects to get the generated cursor_ids
    for entry in changelog_entries:
        await async_db_session.refresh(entry)

    cursor_ids = [entry.cursor_id for entry in changelog_entries]

    changes = await store.get_channel_changelog(
        channel_id=channel_id, last_cursor_position=cursor_ids[0] - 1, batch_size=3
    )

    assert len(changes) == 3
    assert changes[0].message_id == "msg1"
    assert changes[0].operation == "I"
    assert changes[1].message_id == "msg2"
    assert changes[1].operation == "U"
    assert changes[2].message_id == "msg3"
    assert changes[2].operation == "D"


@pytest.mark.anyio
async def test_get_channel_changelog_different_channels(store, async_db_session):
    changelog_entries = [
        MessageChangelog(
            operation=MessageChangelog.Operation.INSERT.value,
            message_id="msg1",
            channel_id="channel1",
            tenant_id=store.tenant_id,
            source=store.source,
        ),
        MessageChangelog(
            operation=MessageChangelog.Operation.UPDATE.value,
            message_id="msg2",
            channel_id="channel2",
            tenant_id=store.tenant_id,
            source=store.source,
        ),
        MessageChangelog(
            operation=MessageChangelog.Operation.DELETE.value,
            message_id="msg3",
            channel_id="channel1",
            tenant_id=store.tenant_id,
            source=store.source,
        ),
    ]

    for entry in changelog_entries:
        async_db_session.add(entry)
    await async_db_session.commit()

    # Refresh objects to get the generated cursor_ids
    for entry in changelog_entries:
        await async_db_session.refresh(entry)

    cursor_ids = [entry.cursor_id for entry in changelog_entries]

    changes = await store.get_channel_changelog(
        channel_id="channel1", last_cursor_position=cursor_ids[0] - 1, batch_size=3
    )

    assert len(changes) == 2
    assert changes[0].message_id == "msg1"
    assert changes[0].channel_id == "channel1"
    assert changes[1].message_id == "msg3"
    assert changes[1].channel_id == "channel1"


@pytest.mark.anyio
async def test_get_channel_changelog_batch_size_limit(store, async_db_session):
    channel_id = "test_channel"

    changelog_entries = [
        MessageChangelog(
            operation=MessageChangelog.Operation.INSERT.value,
            message_id=f"msg{i}",
            channel_id=channel_id,
            tenant_id=store.tenant_id,
            source=store.source,
        )
        for i in range(10)
    ]

    for entry in changelog_entries:
        async_db_session.add(entry)
    await async_db_session.commit()

    # Refresh objects to get the generated cursor_ids
    for entry in changelog_entries:
        await async_db_session.refresh(entry)

    cursor_ids = [entry.cursor_id for entry in changelog_entries]

    changes = await store.get_channel_changelog(
        channel_id=channel_id, last_cursor_position=cursor_ids[0] - 1, batch_size=3
    )

    assert len(changes) == 3
    assert changes[0].message_id == "msg0"
    assert changes[1].message_id == "msg1"
    assert changes[2].message_id == "msg2"


@pytest.mark.anyio
async def test_get_channel_changelog_no_changes(store):
    channel_id = "test_channel"

    changes = await store.get_channel_changelog(
        channel_id=channel_id, last_cursor_position=999999, batch_size=10
    )

    assert len(changes) == 0


@pytest.mark.anyio
async def test_get_channel_changelog_different_tenant(store, async_db_session):
    channel_id = "test_channel"

    different_tenant_id = uuid.uuid4()
    changelog_entries = [
        MessageChangelog(
            operation=MessageChangelog.Operation.INSERT.value,
            message_id="msg1",
            channel_id=channel_id,
            tenant_id=different_tenant_id,
            source=store.source,
        )
    ]

    for entry in changelog_entries:
        async_db_session.add(entry)
    await async_db_session.commit()

    changes = await store.get_channel_changelog(
        channel_id=channel_id, last_cursor_position=0, batch_size=10
    )

    assert len(changes) == 0


# Tests to ensure proper data isolation across tenant_id and source.


@pytest.mark.anyio
async def test_data_isolation_by_tenant_id(async_db_session):
    tenant_a = uuid.uuid4()
    tenant_b = uuid.uuid4()
    source = IntegrationSource.SLACK

    store_a = PostgresMessageStore(
        tenant_id=tenant_a, source=source, session=async_db_session
    )
    store_b = PostgresMessageStore(
        tenant_id=tenant_b, source=source, session=async_db_session
    )

    msg = MessageData(
        message_id="msg-tenant-1",
        channel_id="channel-1",
        content="Hello",
        sent_at=datetime.datetime.now(),
        tombstone=False,
    )

    await store_a.upsert_message(msg)

    assert await store_b.get_message("msg-tenant-1") is None
    assert await store_a.get_message("msg-tenant-1") is not None


@pytest.mark.anyio
async def test_data_isolation_by_source(async_db_session):
    tenant_id = uuid.uuid4()
    store_slack = PostgresMessageStore(
        tenant_id=tenant_id, source=IntegrationSource.SLACK, session=async_db_session
    )
    store_teams = PostgresMessageStore(
        tenant_id=tenant_id, source="teams", session=async_db_session
    )

    msg = MessageData(
        message_id="msg-source-1",
        channel_id="channel-1",
        content="Hello",
        sent_at=datetime.datetime.now(),
        tombstone=False,
    )

    await store_slack.upsert_message(msg)

    assert await store_teams.get_message("msg-source-1") is None
    assert await store_slack.get_message("msg-source-1") is not None


def _create_message_data(**kwargs) -> MessageData:
    return MessageData(
        message_id=kwargs.get("message_id", "msg1"),
        channel_id=kwargs.get("channel_id", "general"),
        content=kwargs.get("content", "Hello World"),
        sent_at=kwargs.get("sent_at", datetime.datetime.now(datetime.UTC)),
        last_edit_at=kwargs.get("last_edit_at", None),
        tombstone=kwargs.get("tombstone", False),
        author=kwargs.get("author", "user1"),
        thread_id=kwargs.get("thread_id", None),
        parent_id=kwargs.get("parent_id", None),
    )
