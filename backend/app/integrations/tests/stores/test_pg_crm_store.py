import uuid

import pytest
from sqlalchemy import select

from app.integrations.models import CRMAccountAccess
from app.integrations.schemas import CRMAccountAccessData, CRMAccountAccessSlice
from app.integrations.stores.pg_crm_store import PostgresCRMStore
from app.integrations.types import IntegrationSource


@pytest.fixture
def crm_store(async_db_session):
    return PostgresCRMStore(
        tenant_id=uuid.uuid4(),
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )


@pytest.mark.anyio
async def test_store_account_access_replaces_existing(crm_store, async_db_session):
    """Test that account sync properly handles account replacement scenarios."""
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_2",
        accounts=[
            CRMAccountAccessData(
                account_id="initial_account",
                account_name="Initial Account",
                access_type="owner",
                access_role="Initial Role",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)

    # Sync with completely different accounts (simulates user losing access to old account)
    new_slice = CRMAccountAccessSlice(
        user_id="test_user_2",
        accounts=[
            CRMAccountAccessData(
                account_id="new_account_1",
                account_name="New Account 1",
                access_type="team",
                access_role="New Role 1",
            ),
            CRMAccountAccessData(
                account_id="new_account_2",
                account_name="New Account 2",
                access_type="territory",
                access_role="New Role 2",
            ),
        ],
    )

    stored_count, deleted_count = await crm_store.store_account_access(new_slice)

    assert stored_count == 2  # Two new accounts added
    assert deleted_count == 1  # One old account removed

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_2",
    )
    result = await async_db_session.execute(stmt)
    db_records = result.scalars().all()

    assert len(db_records) == 2
    assert {record.crm_account_id for record in db_records} == {
        "new_account_1",
        "new_account_2",
    }


@pytest.mark.anyio
async def test_get_user_account_access(crm_store):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_3",
        accounts=[
            CRMAccountAccessData(
                account_id="account_3_1",
                account_name="Test Account 3.1",
                access_type="owner",
                access_role="Sales Manager",
            ),
            CRMAccountAccessData(
                account_id="account_3_2",
                account_name="Test Account 3.2",
                access_type="team",
                access_role="Account Member",
            ),
        ],
    )
    await crm_store.store_account_access(initial_slice)

    retrieved_slice = await crm_store.get_user_account_access("test_user_3")

    assert retrieved_slice.user_id == "test_user_3"
    assert len(retrieved_slice.accounts) == 2

    retrieved_account_ids = {account.account_id for account in retrieved_slice.accounts}
    assert retrieved_account_ids == {"account_3_1", "account_3_2"}

    for account in retrieved_slice.accounts:
        if account.account_id == "account_3_1":
            assert account.access_type == "owner"
            assert account.access_role == "Sales Manager"
        elif account.account_id == "account_3_2":
            assert account.access_type == "team"


@pytest.mark.anyio
async def test_store_account_summary(crm_store, async_db_session):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_5",
        accounts=[
            CRMAccountAccessData(
                account_id="account_5_1",
                account_name="Test Account 5.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)
    await crm_store.store_account_summary(
        "test_user_5", "account_5_1", "Summary of account 5.1"
    )

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_5",
        crm_account_id="account_5_1",
    )

    result = await async_db_session.execute(stmt)
    access_record = result.scalar_one()
    assert access_record.summary == "Summary of account 5.1"


@pytest.mark.anyio
async def test_get_account_summary(crm_store):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_6",
        accounts=[
            CRMAccountAccessData(
                account_id="account_6_1",
                account_name="Test Account 6.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)
    await crm_store.store_account_summary(
        "test_user_6", "account_6_1", "Test summary for account 6.1"
    )

    summary = await crm_store.get_account_summary("test_user_6", "account_6_1")
    assert summary == "Test summary for account 6.1"


@pytest.mark.anyio
async def test_get_account_summary_not_found(crm_store):
    summary = await crm_store.get_account_summary(
        "nonexistent_user", "nonexistent_account"
    )
    assert summary is None


@pytest.mark.anyio
async def test_clear_user_account_access(crm_store, async_db_session):
    initial_slice = CRMAccountAccessSlice(
        user_id="test_user_4",
        accounts=[
            CRMAccountAccessData(
                account_id="account_4_1",
                account_name="Test Account 4.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await crm_store.store_account_access(initial_slice)

    await crm_store.clear_user_account_access("test_user_4")

    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id="test_user_4",
    )
    result = await async_db_session.execute(stmt)
    db_records = result.scalars().all()

    assert len(db_records) == 0


@pytest.mark.anyio
async def test_data_isolation_by_tenant_id(async_db_session):
    tenant_a = uuid.uuid4()
    tenant_b = uuid.uuid4()

    store_a = PostgresCRMStore(
        tenant_id=tenant_a,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )
    store_b = PostgresCRMStore(
        tenant_id=tenant_b,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )

    slice_a = CRMAccountAccessSlice(
        user_id="test_user_5",
        accounts=[
            CRMAccountAccessData(
                account_id="account_5_1",
                account_name="Test Account 5.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await store_a.store_account_access(slice_a)

    retrieved_slice = await store_b.get_user_account_access("test_user_5")

    assert len(retrieved_slice.accounts) == 0


@pytest.mark.anyio
async def test_data_isolation_by_source(async_db_session):
    tenant_id = uuid.uuid4()

    store_salesforce = PostgresCRMStore(
        tenant_id=tenant_id,
        source=IntegrationSource.SALESFORCE,
        session=async_db_session,
    )
    store_hubspot = PostgresCRMStore(
        tenant_id=tenant_id,
        source=IntegrationSource.HUBSPOT,
        session=async_db_session,
    )

    slice_salesforce = CRMAccountAccessSlice(
        user_id="test_user_6",
        accounts=[
            CRMAccountAccessData(
                account_id="account_6_1",
                account_name="Test Account 6.1",
                access_type="owner",
                access_role="Sales Manager",
            )
        ],
    )
    await store_salesforce.store_account_access(slice_salesforce)

    retrieved_slice = await store_hubspot.get_user_account_access("test_user_6")

    assert len(retrieved_slice.accounts) == 0


@pytest.mark.anyio
async def test_store_account_access_preserves_summaries(crm_store, async_db_session):
    """Test that account sync preserves existing summary data while handling updates and deletions."""
    user_id = "test_user_summary"

    # 1. Initial setup: Create accounts with summaries
    initial_slice = CRMAccountAccessSlice(
        user_id=user_id,
        accounts=[
            CRMAccountAccessData(
                account_id="account_keep",
                account_name="Account to Keep",
                access_type="owner",
                access_role="Manager",
            ),
            CRMAccountAccessData(
                account_id="account_update",
                account_name="Account to Update",
                access_type="owner",
                access_role="Manager",
            ),
            CRMAccountAccessData(
                account_id="account_delete",
                account_name="Account to Delete",
                access_type="owner",
                access_role="Manager",
            ),
        ],
    )
    await crm_store.store_account_access(initial_slice)

    # Add summaries to all accounts
    await crm_store.store_account_summary(
        user_id, "account_keep", "Summary for kept account"
    )
    await crm_store.store_account_summary(
        user_id, "account_update", "Summary for updated account"
    )
    await crm_store.store_account_summary(
        user_id, "account_delete", "Summary for deleted account"
    )

    # 2. Sync with updated data: keep one, update one, delete one, add one
    updated_slice = CRMAccountAccessSlice(
        user_id=user_id,
        accounts=[
            # Keep this account unchanged
            CRMAccountAccessData(
                account_id="account_keep",
                account_name="Account to Keep",
                access_type="owner",
                access_role="Manager",
            ),
            # Update this account's name and role
            CRMAccountAccessData(
                account_id="account_update",
                account_name="Updated Account Name",
                access_type="team",
                access_role="Updated Role",
            ),
            # Add a new account
            CRMAccountAccessData(
                account_id="account_new",
                account_name="New Account",
                access_type="territory",
                access_role="New Role",
            ),
            # Note: account_delete is not included, so it should be deleted
        ],
    )

    stored_count, deleted_count = await crm_store.store_account_access(updated_slice)

    # Verify counts
    assert stored_count == 3  # keep + update + new
    assert deleted_count == 1  # deleted account

    # 3. Verify the results
    stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id=user_id,
    )
    result = await async_db_session.execute(stmt)
    db_records = {record.crm_account_id: record for record in result.scalars().all()}

    # Should have 3 accounts now
    assert len(db_records) == 3
    assert set(db_records.keys()) == {"account_keep", "account_update", "account_new"}

    # Verify kept account: unchanged data, summary preserved
    kept_record = db_records["account_keep"]
    assert kept_record.crm_account_name == "Account to Keep"
    assert kept_record.crm_access_type == "owner"
    assert kept_record.crm_access_role == "Manager"
    assert kept_record.summary == "Summary for kept account"

    # Verify updated account: data updated, summary preserved
    updated_record = db_records["account_update"]
    assert updated_record.crm_account_name == "Updated Account Name"
    assert updated_record.crm_access_type == "team"
    assert updated_record.crm_access_role == "Updated Role"
    assert updated_record.summary == "Summary for updated account"  # Summary preserved!

    # Verify new account: no summary yet
    new_record = db_records["account_new"]
    assert new_record.crm_account_name == "New Account"
    assert new_record.crm_access_type == "territory"
    assert new_record.crm_access_role == "New Role"
    assert new_record.summary is None

    # Verify deleted account is gone
    deleted_stmt = select(CRMAccountAccess).filter_by(
        tenant_id=crm_store.tenant_id,
        source=crm_store.source,
        crm_user_id=user_id,
        crm_account_id="account_delete",
    )
    deleted_result = await async_db_session.execute(deleted_stmt)
    assert deleted_result.scalar_one_or_none() is None
