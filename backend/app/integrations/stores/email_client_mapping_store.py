from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.models.email_client_mapping import (
    EmailClientMapping,
    EmailMappingType,
)


class EmailClientMappingStore:
    """Store for email client mapping queries"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_account_email_mappings(
        self, tenant_id: str, crm_account_id: str
    ) -> list[EmailClientMapping]:
        """Get all email client mappings for a CRM account"""
        stmt = select(EmailClientMapping).where(
            EmailClientMapping.tenant_id == tenant_id,
            EmailClientMapping.crm_account_id == crm_account_id,
        )
        result = await self.session.execute(stmt)
        return list(result.scalars().all())

    async def get_domain_mapping_for_account(
        self, tenant_id: str, crm_account_id: str, sender_domain: str
    ) -> EmailClientMapping | None:
        """Check if domain mapping already exists for a CRM account"""
        stmt = select(EmailClientMapping).where(
            EmailClientMapping.tenant_id == tenant_id,
            EmailClientMapping.crm_account_id == crm_account_id,
            EmailClientMapping.domain == sender_domain,
            EmailClientMapping.mapping_type == EmailMappingType.DOMAIN,
        )
        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()

    async def get_existing_mapping_with_fields(
        self, mapping_data: dict
    ) -> EmailClientMapping | None:
        """Check if mapping already exists with specific field conditions"""
        stmt = select(EmailClientMapping).where(
            EmailClientMapping.tenant_id == mapping_data["tenant_id"],
            EmailClientMapping.crm_account_id == mapping_data["crm_account_id"],
            EmailClientMapping.mapping_type == mapping_data["mapping_type"],
        )

        # Add specific field conditions based on mapping type
        if mapping_data["mapping_type"] == EmailMappingType.DOMAIN:
            stmt = stmt.where(EmailClientMapping.domain == mapping_data["domain"])
        elif mapping_data["mapping_type"] == EmailMappingType.ADDRESS:
            stmt = stmt.where(
                EmailClientMapping.email_address == mapping_data["email_address"]
            )
        elif mapping_data["mapping_type"] == EmailMappingType.CONTACT_NAME:
            stmt = stmt.where(
                EmailClientMapping.contact_name_pattern
                == mapping_data["contact_name_pattern"]
            )

        result = await self.session.execute(stmt)
        return result.scalar_one_or_none()
