from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.models.email_processing_run import EmailProcessingRun


class EmailProcessingRunStore:
    """Store for email processing run queries"""

    def __init__(self, session: AsyncSession):
        self.session = session

    async def get_processing_run_by_id(
        self, processing_run_id: UUID
    ) -> EmailProcessingRun:
        """Get an email processing run by ID"""
        stmt = select(EmailProcessingRun).where(
            EmailProcessingRun.id == processing_run_id
        )
        result = await self.session.execute(stmt)
        return result.scalar_one()
