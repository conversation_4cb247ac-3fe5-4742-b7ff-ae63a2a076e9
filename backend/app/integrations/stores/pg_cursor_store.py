from uuid import UUID

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.cursor_store import ICursorStore
from app.integrations.models.changelog_cursor import ChangelogCursor


class PostgresCursorStore(ICursorStore):
    def __init__(self, tenant_id: UUID, session: AsyncSession) -> None:
        self._tenant_id = tenant_id
        self._session = session

    async def get_position(self, cursor_id: str) -> int:
        cursor = await self._get_cursor(cursor_id)
        if cursor is None:
            return 0
        return cursor.cursor_position

    async def update_position(self, cursor_id: str, new_position: int) -> None:
        cursor = await self._get_cursor(cursor_id)

        if cursor is None:
            cursor = ChangelogCursor(
                cursor_id=cursor_id,
                tenant_id=self._tenant_id,
                cursor_position=new_position,
            )
            self._session.add(cursor)
        else:
            cursor.cursor_position = new_position

        await self._session.commit()

    async def _get_cursor(self, cursor_id: str) -> ChangelogCursor | None:
        stmt = select(ChangelogCursor).where(
            ChangelogCursor.tenant_id == self._tenant_id,
            ChangelogCursor.cursor_id == cursor_id,
        )

        result = await self._session.execute(stmt)
        return result.scalar_one_or_none()
