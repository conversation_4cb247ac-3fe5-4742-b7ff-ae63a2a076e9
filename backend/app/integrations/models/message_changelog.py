import enum

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON>, Identity, Index, String
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel
from app.integrations.types import IntegrationSource


class MessageChangelog(TenantModel):
    __tablename__ = "message_changelog"

    class Operation(str, enum.Enum):
        INSERT = "I"
        UPDATE = "U"
        DELETE = "D"

        @property
        def label(self) -> str:
            labels = {"I": "Insert", "U": "Update", "D": "Delete"}
            return labels.get(self.value, self.value)

    source: Mapped[IntegrationSource] = mapped_column(
        StringEnum(IntegrationSource), nullable=False
    )
    operation: Mapped[Operation] = mapped_column(
        StringEnum(Operation, length=1), nullable=False
    )
    message_id: Mapped[str] = mapped_column(String, nullable=False)
    channel_id: Mapped[str] = mapped_column(String, nullable=False)
    cursor_id: Mapped[int] = mapped_column(
        BigInteger, Identity(always=True), nullable=False, index=True
    )

    __table_args__ = (
        Index(
            "idx_message_changelog_tenant_channel_cursor",
            "tenant_id",
            "channel_id",
            "cursor_id",
        ),
        Index("idx_message_changelog_tenant_cursor", "tenant_id", "cursor_id"),
    )

    @property
    def operation_label(self) -> str:
        try:
            op = MessageChangelog.Operation(self.operation)
            return op.label
        except ValueError:
            return f"Unknown ({self.operation})"
