import enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Float, String, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column

from app.common.orm.types import StringEnum
from app.integrations.base.tenant_model import TenantModel


class EmailMappingType(str, enum.Enum):
    DOMAIN = "domain"
    ADDRESS = "address"
    CONTACT_NAME = "contact_name"


class EmailClientMapping(TenantModel):
    __tablename__ = "email_client_mapping"

    crm_account_id: Mapped[str] = mapped_column(String, nullable=False, index=True)
    domain: Mapped[str | None] = mapped_column(String, nullable=True)
    email_address: Mapped[str | None] = mapped_column(String, nullable=True)
    contact_name_pattern: Mapped[str | None] = mapped_column(String, nullable=True)
    mapping_type: Mapped[EmailMappingType] = mapped_column(
        StringEnum(EmailMappingType), nullable=False
    )
    confidence_score: Mapped[float] = mapped_column(Float, nullable=False, default=1.0)
    created_by_user: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=False
    )

    __table_args__ = (
        # Simple constraint on tenant, account, and type
        UniqueConstraint(
            "tenant_id",
            "crm_account_id",
            "mapping_type",
            "domain",
            name="uq_email_mapping_domain",
        ),
        UniqueConstraint(
            "tenant_id",
            "crm_account_id",
            "mapping_type",
            "email_address",
            name="uq_email_mapping_address",
        ),
        UniqueConstraint(
            "tenant_id",
            "crm_account_id",
            "mapping_type",
            "contact_name_pattern",
            name="uq_email_mapping_contact_name",
        ),
    )
