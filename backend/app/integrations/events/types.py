from enum import Enum
from typing import Any
from uuid import UUID

from pydantic import BaseModel


class IntegrationEventType(str, Enum):
    CRM_CONNECTED = "crm_connected"
    EMAIL_CONNECTED = "email_connected"
    CRM_ACCOUNT_SYNC_COMPLETED = "crm_account_sync_completed"


class IntegrationEvent(BaseModel):
    event_type: IntegrationEventType
    tenant_id: UUID
    user_id: UUID | None = None  # Optional for system-level events
    integration_type: str
    metadata: dict[str, Any] = {}
