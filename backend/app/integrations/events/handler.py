import asyncio
import logging
from collections.abc import Awaitable, Callable
from uuid import UUI<PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import AsyncSessionLocal
from app.integrations.events.types import IntegrationEvent, IntegrationEventType
from app.integrations.services.email_sync_service import EmailSyncService
from app.workspace.integrations.user_integrations import create_user_integrations

logger = logging.getLogger(__name__)


class IntegrationEventHandler:
    def __init__(self):
        self._handlers: dict[
            IntegrationEventType, list[Callable[[IntegrationEvent], Awaitable[None]]]
        ] = {}
        self._sync_service = EmailSyncService()

    def register_handler(
        self,
        event_type: IntegrationEventType,
        handler: Callable[[IntegrationEvent], Awaitable[None]],
    ):
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)

    async def emit_event(self, event: IntegrationEvent):
        logger.info(f"Emitting event: {event.event_type} for tenant {event.tenant_id}")

        handlers = self._handlers.get(event.event_type, [])
        if handlers:
            # Run handlers concurrently
            await asyncio.gather(
                *[handler(event) for handler in handlers], return_exceptions=True
            )

    async def handle_crm_connected(self, event: IntegrationEvent):
        """Handle CRM connected event - trigger email sync if email integration exists"""
        try:
            if not event.user_id:
                logger.warning(
                    f"No user_id in CRM connected event for tenant {event.tenant_id}"
                )
                return

            async with AsyncSessionLocal() as session:
                environment_id = event.metadata.get("environment_id")
                if not environment_id:
                    logger.warning(
                        "No environment_id in event metadata for CRM connected event"
                    )
                    return

                user_integrations = await create_user_integrations(
                    user_id=event.user_id,
                    environment_id=UUID(environment_id),
                    db_session=session,
                )

                # Check if email integration exists
                email_integration = await user_integrations.email()
                if email_integration:
                    logger.info(
                        f"CRM connected and email integration exists - triggering initial email sync for tenant {event.tenant_id}"
                    )
                    await self._trigger_initial_email_sync(event.tenant_id, session)

        except Exception as e:
            logger.exception(f"Error handling CRM connected event: {e}")

    async def handle_email_connected(self, event: IntegrationEvent):
        """Handle email connected event - trigger email sync if CRM integration exists"""
        try:
            if not event.user_id:
                logger.warning(
                    f"No user_id in email connected event for tenant {event.tenant_id}"
                )
                return

            async with AsyncSessionLocal() as session:
                environment_id = event.metadata.get("environment_id")
                if not environment_id:
                    logger.warning(
                        "No environment_id in event metadata for email connected event"
                    )
                    return

                user_integrations = await create_user_integrations(
                    user_id=event.user_id,
                    environment_id=UUID(environment_id),
                    db_session=session,
                )

                # Check if CRM integration exists
                crm_integration = await user_integrations.crm()
                if crm_integration:
                    logger.info(
                        f"Email connected and CRM integration exists - triggering initial email sync for tenant {event.tenant_id}"
                    )
                    await self._trigger_initial_email_sync(event.tenant_id, session)

        except Exception as e:
            logger.exception(f"Error handling email connected event: {e}")

    async def handle_crm_account_sync_completed(self, event: IntegrationEvent):
        """Handle CRM account sync completed event - trigger incremental email sync"""
        try:
            logger.info(
                f"Handling CRM account sync completed event for tenant {event.tenant_id}"
            )
            # Run incremental email sync directly without background task to maintain async context
            await self._sync_service.sync_all_accounts(
                tenant_id=event.tenant_id,
                lookback_days=7,  # Incremental sync - only recent emails
            )
            logger.info(
                f"Completed incremental email sync after CRM update for tenant {event.tenant_id}"
            )

        except Exception as e:
            logger.exception(f"Error handling CRM account sync completed event: {e}")

    async def _trigger_initial_email_sync(
        self, tenant_id: UUID, _session: AsyncSession
    ):
        # Run directly without background task to maintain async context
        await self._sync_service.sync_all_accounts(
            tenant_id=tenant_id, lookback_days=365
        )


# Global event handler instance
integration_event_handler = IntegrationEventHandler()

# Register default handlers
integration_event_handler.register_handler(
    IntegrationEventType.CRM_CONNECTED, integration_event_handler.handle_crm_connected
)
integration_event_handler.register_handler(
    IntegrationEventType.EMAIL_CONNECTED,
    integration_event_handler.handle_email_connected,
)
integration_event_handler.register_handler(
    IntegrationEventType.CRM_ACCOUNT_SYNC_COMPLETED,
    integration_event_handler.handle_crm_account_sync_completed,
)
