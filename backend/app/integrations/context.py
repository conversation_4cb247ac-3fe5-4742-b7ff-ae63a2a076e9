import uuid
from collections.abc import Callable

from sqlalchemy.ext.asyncio import AsyncSession

from app.integrations.base.context import BaseContext
from app.integrations.base.credentials_resolver import ICredentialsResolver


class ManagedAsyncSession:
    def __init__(self, session: AsyncSession, can_close: bool = True):
        self._session = session
        self._can_close = can_close

    def __getattr__(self, name):
        return getattr(self._session, name)

    async def close(self):
        if self._can_close:
            await self._session.close()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


class IntegrationContext(BaseContext):
    def __init__(
        self,
        tenant_id: uuid.UUID,
        db_session_factory: Callable[[], ManagedAsyncSession],
        credentials_resolver: ICredentialsResolver | None = None,
    ):
        super().__init__(tenant_id)
        self.db_session_factory = db_session_factory
        self.credentials_resolver = credentials_resolver


def create_context(
    tenant_id: uuid.UUID,
    credentials_resolver: ICredentialsResolver | None = None,
    db_session: AsyncSession | None = None,
    db_session_factory: Callable[[], AsyncSession] | None = None,
) -> IntegrationContext:
    if (db_session is None) == (db_session_factory is None):
        raise ValueError(
            "Exactly one of db_session or db_session_factory must be provided"
        )

    if db_session:
        # Shared session - create a factory that returns non-closable sessions
        def managed_factory():
            return ManagedAsyncSession(db_session, can_close=False)
    else:
        # Session factory - create a factory that returns closable sessions
        def managed_factory():
            return ManagedAsyncSession(db_session_factory(), can_close=True)

    return IntegrationContext(
        tenant_id=tenant_id,
        db_session_factory=managed_factory,
        credentials_resolver=credentials_resolver,
    )
