from datetime import datetime
from typing import Any

from pydantic import BaseModel


class MessageData(BaseModel):
    message_id: str
    channel_id: str
    content: str
    sent_at: datetime
    last_edit_at: datetime | None = None
    tombstone: bool | None = False
    author: str | None = None
    thread_id: str | None = None
    parent_id: str | None = None


class MessageChangelogData(BaseModel):
    channel_id: str
    operation: str
    message_id: str
    cursor_id: int
    created_at: datetime


class ChannelDataSlice(BaseModel):
    channel_id: str
    messages: list[MessageData]
    from_time: datetime
    to_time: datetime


class ReconciliationStats(BaseModel):
    inserts: int
    updates: int
    deletes: int


class ChannelIngestionResult(BaseModel):
    messages_count: int
    inserts: int
    updates: int
    deletes: int
    from_time: datetime
    to_time: datetime


class ChannelProcessingResult(BaseModel):
    processed_changes: int
    regenerated_documents: int
    deleted_documents: int


class DocumentData(BaseModel):
    id: str
    content: str
    source_timestamp: datetime
    tags: set[str]


class CRMAccountAccessData(BaseModel):
    account_id: str
    account_name: str
    access_type: str
    access_role: str | None


class CRMAccountAccessSlice(BaseModel):
    user_id: str
    accounts: list[CRMAccountAccessData]


class CRMAccountAccessSyncResult(BaseModel):
    new_access_count: int
    old_access_count: int


class FileData(BaseModel):
    id: str
    name: str
    size: int
    time_created: datetime
    last_modified: datetime
    md5_hash: str
    content_type: str


class FileProcessingResult(BaseModel):
    processed_files: int
    deleted_documents: int


class CRMMetrics(BaseModel):
    quota: int
    closed_won: int
    pipeline: int
    forecast: int


class EmailAttachment(BaseModel):
    id: str
    filename: str
    mime_type: str
    size: int


class EmailMessage(BaseModel):
    id: str
    thread_id: str
    subject: str
    sender: str
    recipients: str
    cc: str | None = None
    bcc: str | None = None
    date: datetime | None = None
    body: str
    snippet: str
    labels: list[str] = []
    attachments: list[EmailAttachment] = []
    size_estimate: int = 0
    raw_data: dict[str, Any] | None = None


class EmailThread(BaseModel):
    id: str
    snippet: str
    messages: list[EmailMessage]
    labels: list[str] = []
    history_id: str | None = None


class CRMAccount(BaseModel):
    id: str
    name: str
    website: str | None = None
    industry: str | None = None
    annual_revenue: int | float | None = None
    employee_count: int | None = None
    phone: str | None = None
    owner_id: str | None = None
    account_type: str | None = None
    instance_url: str | None = None
    domain: str | None = None


class CRMContact(BaseModel):
    id: str
    first_name: str | None = None
    last_name: str | None = None
    name: str | None = None
    email: str | None = None
    phone: str | None = None
    account_id: str | None = None


class CRMAccountData(BaseModel):
    account_id: str
    account: CRMAccount
    contacts: list[CRMContact]
