from app.common.helpers.logger import get_logger
from app.integrations.base.parser import IParser
from app.integrations.processors.parsers.mistral_ocr import MistralOCR
from app.integrations.types import ExtensionType

logger = get_logger()


class FileParser(IParser):
    def __init__(self, mistral_api_key: str):
        self.mistral_ocr = MistralOCR(api_key=mistral_api_key)
        self.mistral_ocr_extensions = {
            ExtensionType.PNG,
            ExtensionType.JPEG,
            ExtensionType.JPG,
            ExtensionType.AVIF,
            ExtensionType.PDF,
            ExtensionType.PPTX,
            ExtensionType.DOCX,
        }

    async def parse(
        self,
        file_data: bytes,
        file_extension: ExtensionType,
        content_type: str,
    ) -> str:
        if not file_data:
            raise ValueError("File data is empty")

        if file_extension == ExtensionType.TXT:
            return file_data.decode("utf-8")
        elif file_extension in self.mistral_ocr_extensions:
            return await self.mistral_ocr.parse(file_data, content_type)
        else:
            raise ValueError(f"Unsupported file extension: {file_extension}")
