import html
import re

from app.integrations.schemas import EmailMessage


class EmailContentFormatter:
    def __init__(self, max_content_length: int = 50000):
        self.max_content_length = max_content_length

    def format_email_for_embedding(self, email: EmailMessage) -> str:
        clean_body = self._clean_email_body(email.body)

        if len(clean_body) < 50:
            return ""

        content_parts = [
            f"Subject: {email.subject}",
            f"From: {email.sender}",
            f"To: {email.recipients}",
        ]

        if email.date:
            content_parts.append(f"Date: {email.date.strftime('%Y-%m-%d %H:%M')}")

        if email.cc:
            content_parts.append(f"CC: {email.cc}")

        content_parts.append(f"Content: {clean_body}")

        content = "\n\n".join(content_parts)

        if len(content) > self.max_content_length:
            content = content[: self.max_content_length] + "..."

        return content

    def _clean_email_body(self, body: str) -> str:
        if not body:
            return ""

        # Remove HTML tags and decode entities
        body = html.unescape(body)
        body = re.sub(r"<[^>]+>", "", body)

        # Only remove obvious system-generated signatures (language-agnostic)
        system_signatures = [
            r"Sent from my iPhone.*$",
            r"Sent from my Android.*$",
            r"Sent from Outlook.*$",
            r"Get Outlook for.*$",
            r"Sent from my Samsung.*$",
            r"Sent from my iPad.*$",
        ]

        for pattern in system_signatures:
            body = re.sub(
                pattern, "", body, flags=re.MULTILINE | re.DOTALL | re.IGNORECASE
            )

        # Clean up excessive whitespace but preserve structure
        body = re.sub(r"\n\s*\n\s*\n", "\n\n", body)
        body = re.sub(r"[ \t]+", " ", body)  # Multiple spaces/tabs to single space
        body = body.strip()

        return body
