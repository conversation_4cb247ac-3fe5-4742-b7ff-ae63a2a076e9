import re
from collections.abc import Async<PERSON>enerator
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession

from app.common.helpers.logger import get_logger
from app.integrations.base.email_adapter import BaseEmailAdapter
from app.integrations.models.email_client_mapping import (
    EmailClientMapping,
    EmailMappingType,
)
from app.integrations.schemas import CRMAccountData, EmailMessage
from app.integrations.stores.email_client_mapping_store import EmailClientMappingStore
from app.utils import extract_domain_from_url

logger = get_logger()


class EmailClientFilter:
    def __init__(
        self,
        email_adapter: BaseEmailAdapter,
        db_session: AsyncSession,
        tenant_id: str,
        crm_account_data: CRMAccountData | None = None,
    ):
        self.email_adapter = email_adapter
        self.db_session = db_session
        self.tenant_id = tenant_id
        self.crm_account_data = crm_account_data

    async def get_account_emails(
        self,
        crm_account_id: str,
        since: datetime,
        max_results: int = 1000,
    ) -> AsyncGenerator[EmailMessage, None]:
        mappings = await self._get_account_email_mappings(crm_account_id)

        if not mappings:
            return

        search_queries = self._build_search_queries(mappings, since)

        for query in search_queries:
            try:
                emails = await self.email_adapter.list_messages(
                    query=query, max_results=max_results, include_spam_trash=False
                )

                for email in emails:
                    is_relevant = await self._validate_email_relevance(
                        email, crm_account_id, mappings
                    )
                    if is_relevant:
                        yield email
            except Exception as e:
                logger.exception(
                    f"Error getting account emails for {crm_account_id}: {e}"
                )
                continue

    async def _get_account_email_mappings(
        self, crm_account_id: str
    ) -> list[EmailClientMapping]:
        mapping_store = EmailClientMappingStore(self.db_session)
        mappings = await mapping_store.get_account_email_mappings(
            self.tenant_id, crm_account_id
        )
        created_or_updated_mappings = await self._create_or_update_mappings(
            crm_account_id
        )
        mappings.extend(created_or_updated_mappings)

        return mappings

    def _build_search_queries(
        self, mappings: list[EmailClientMapping], since: datetime
    ) -> list[str]:
        queries = []
        date_filter = f"after:{since.strftime('%Y/%m/%d')}"

        for mapping in mappings:
            if mapping.mapping_type == EmailMappingType.DOMAIN and mapping.domain:
                query = f"(from:{mapping.domain} OR to:{mapping.domain}) {date_filter}"
                queries.append(query)
            elif (
                mapping.mapping_type == EmailMappingType.ADDRESS
                and mapping.email_address
            ):
                query = f"(from:{mapping.email_address} OR to:{mapping.email_address}) {date_filter}"
                queries.append(query)

        return queries

    async def _validate_email_relevance(
        self,
        email: EmailMessage,
        _crm_account_id: str,
        mappings: list[EmailClientMapping],
    ) -> bool:
        for mapping in mappings:
            if mapping.mapping_type == EmailMappingType.DOMAIN and mapping.domain:
                if mapping.domain in email.sender or mapping.domain in email.recipients:
                    return True
            elif (
                mapping.mapping_type == EmailMappingType.ADDRESS
                and mapping.email_address
            ):
                if (
                    mapping.email_address in email.sender
                    or mapping.email_address in email.recipients
                ):
                    return True
            elif (
                mapping.mapping_type == EmailMappingType.CONTACT_NAME
                and mapping.contact_name_pattern
            ):
                pattern = re.compile(mapping.contact_name_pattern, re.IGNORECASE)
                if pattern.search(email.sender) or pattern.search(email.recipients):
                    return True

        return False

    async def ensure_mapping(self, email: EmailMessage, crm_account_id: str) -> None:
        sender_domain = email.sender.split("@")[-1] if "@" in email.sender else None

        if sender_domain:
            mapping_store = EmailClientMappingStore(self.db_session)
            existing = await mapping_store.get_domain_mapping_for_account(
                self.tenant_id, crm_account_id, sender_domain
            )

            if not existing:
                mapping = EmailClientMapping(
                    tenant_id=self.tenant_id,
                    crm_account_id=crm_account_id,
                    domain=sender_domain,
                    mapping_type=EmailMappingType.DOMAIN,
                    confidence_score=0.7,
                    created_by_user=True,
                )
                self.db_session.add(mapping)
                await self.db_session.commit()

    async def _create_or_update_mappings(
        self, crm_account_id: str
    ) -> list[EmailClientMapping]:
        if (
            not self.crm_account_data
            or self.crm_account_data.account_id != crm_account_id
        ):
            logger.debug(f"No CRM data available for account {crm_account_id}")
            return []

        try:
            logger.debug(
                f"Creating automatic mappings for CRM account {crm_account_id}"
            )
            # Use provided typed CRM data
            account = self.crm_account_data.account
            contacts = self.crm_account_data.contacts

            mappings_to_insert = []

            # Extract domain from account website
            if account.website:
                domain = extract_domain_from_url(account.website)
                if domain:
                    mappings_to_insert.append(
                        {
                            "tenant_id": self.tenant_id,
                            "crm_account_id": crm_account_id,
                            "domain": domain,
                            "email_address": None,
                            "contact_name_pattern": None,
                            "mapping_type": EmailMappingType.DOMAIN,
                            "confidence_score": 0.9,
                            "created_by_user": False,
                        }
                    )

            # Extract email addresses from contacts
            for contact in contacts:
                if contact.email and "@" in contact.email:
                    mappings_to_insert.append(
                        {
                            "tenant_id": self.tenant_id,
                            "crm_account_id": crm_account_id,
                            "domain": None,
                            "email_address": contact.email.lower(),
                            "contact_name_pattern": None,
                            "mapping_type": EmailMappingType.ADDRESS,
                            "confidence_score": 1.0,
                            "created_by_user": False,
                        }
                    )

            # Use standard SQLAlchemy approach instead of PostgreSQL-specific operations
            created_mappings = []

            if mappings_to_insert:
                for mapping_data in mappings_to_insert:
                    try:
                        # Check if mapping already exists to avoid duplicates
                        mapping_store = EmailClientMappingStore(self.db_session)
                        existing = await mapping_store.get_existing_mapping_with_fields(
                            mapping_data
                        )

                        if not existing:
                            # Create new mapping
                            mapping = EmailClientMapping(**mapping_data)
                            self.db_session.add(mapping)
                            created_mappings.append(mapping)

                    except Exception as e:
                        logger.warning(
                            f"Failed to create mapping for {mapping_data}: {e}"
                        )
                        continue

                # Commit all new mappings at once
                if created_mappings:
                    await self.db_session.commit()

            logger.debug(
                f"Created {len(created_mappings)} automatic mappings for account {crm_account_id}"
            )
            return created_mappings

        except Exception as e:
            logger.error(
                f"Failed to create automatic mappings for account {crm_account_id}: {e}"
            )
            return []
