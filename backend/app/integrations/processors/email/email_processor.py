import re
from datetime import datetime
from uuid import UUID

from app.common.helpers.logger import get_logger
from app.integrations.base.chunker import <PERSON><PERSON>hunker
from app.integrations.base.document_store import IDocumentStore
from app.integrations.base.embedder import IEmbedder
from app.integrations.processors.email.content_formatter import EmailContentFormatter
from app.integrations.schemas import DocumentData, EmailMessage

logger = get_logger()


class EmailProcessor:
    def __init__(
        self,
        embedder: "IEmbedder",
        document_store: "IDocumentStore",
        chunker: "IChunker",
        formatter: EmailContentFormatter | None = None,
    ):
        self.embedder = embedder
        self.document_store = document_store
        self.chunker = chunker
        self.formatter = formatter or EmailContentFormatter()

    async def process_emails_batch(
        self,
        emails: list[EmailMessage],
        crm_account_id: str,
    ) -> int:
        processed_count = 0

        for email in emails:
            if await self._email_exists(email.id):
                continue

            try:
                await self._process_single_email(email, crm_account_id)
                processed_count += 1
            except Exception:
                logger.exception(f"Error processing email {email.id}")
                continue

        return processed_count

    async def process_single_email(
        self,
        email: EmailMessage,
        crm_account_id: str,
        processing_run_id: UUID | None = None,
        force_reprocess: bool = False,
    ) -> bool:
        if not force_reprocess and await self._email_exists(email.id):
            return False

        await self._process_single_email(email, crm_account_id, processing_run_id)

        return True

    async def _process_single_email(
        self,
        email: EmailMessage,
        crm_account_id: str,
        _processing_run_id: UUID | None = None,
    ) -> None:
        try:
            content = self.formatter.format_email_for_embedding(email)

            if not content or len(content.strip()) < 50:
                return

            # Chunk the email content
            chunks = self.chunker.chunk(content)

            for index, chunk in enumerate(chunks):
                if not chunk.strip():
                    continue

                try:
                    embedding = await self.embedder.embed_text(chunk)

                    tags = self._build_email_tags(email, crm_account_id, index)

                    document = DocumentData(
                        id=f"{email.id}_chunk_{index}",
                        content=chunk,
                        source_timestamp=email.date or datetime.now(),
                        tags=set(tags),
                    )

                    await self.document_store.store_document(document, embedding)
                except Exception as e:
                    logger.exception(f"Error processing chunk {index}: {e}")
                    raise

        except Exception as e:
            logger.exception(f"Error processing email {email.id}: {e}")
            raise

    async def _email_exists(self, email_id: str) -> bool:
        try:
            tag = f"email_id:{email_id}"
            document_ids = await self.document_store.find_document_ids_by_tag(tag)
            return len(document_ids) > 0
        except Exception as e:
            logger.exception(f"Error checking if email exists {email_id}: {e}")
            raise

    def _build_email_tags(
        self, email: EmailMessage, crm_account_id: str, chunk_index: int
    ) -> list[str]:
        tags = [
            "email",
            f"crm_account_id:{crm_account_id}",
            f"thread_id:{email.thread_id}",
            f"email_id:{email.id}",
            f"chunk_index:{chunk_index}",
        ]

        # Extract clean sender email and domain
        sender_email = self._extract_email_from_sender(email.sender)
        if sender_email:
            tags.append(f"sender_email:{sender_email}")
            sender_username = (
                sender_email.split("@")[0] if "@" in sender_email else sender_email
            )
            tags.append(f"sender_username:{sender_username}")

            sender_domain = sender_email.split("@")[1] if "@" in sender_email else None
            if sender_domain:
                tags.append(f"sender_domain:{sender_domain}")

        # Extract sender name
        sender_name = self._extract_name_from_sender(email.sender)
        if sender_name:
            tags.append(f"sender_name:{sender_name}")

        if email.date:
            tags.append(f"date:{email.date.strftime('%Y-%m-%d')}")
            tags.append(f"month:{email.date.strftime('%Y-%m')}")

        if email.labels:
            for label in email.labels[:5]:
                tags.append(f"label:{label.lower()}")

        if email.attachments:
            tags.append("has_attachments:true")
        else:
            tags.append("has_attachments:false")

        if any(
            keyword in email.subject.lower()
            for keyword in ["urgent", "asap", "emergency"]
        ):
            tags.append("urgent:true")

        return tags

    def _extract_email_from_sender(self, sender: str) -> str | None:
        if not sender:
            return None

        # Robust email regex pattern
        email_pattern = r"\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b"

        # Find all email matches in the sender string
        matches = re.findall(email_pattern, sender)

        if matches:
            # Return the first valid email found
            return matches[0].strip()

        return None

    def _extract_name_from_sender(self, sender: str) -> str | None:
        if not sender:
            return None

        # Remove email address first to isolate the name part
        email_pattern = r"\b[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}\b"
        name_part = re.sub(email_pattern, "", sender)

        # Remove common formatting characters like <, >, quotes, parentheses
        name_part = re.sub(r'[<>"\(\)]+', "", name_part)

        # Clean up extra whitespace
        name_part = re.sub(r"\s+", " ", name_part).strip()

        # Only return if we have a meaningful name (at least 2 characters, contains letter)
        if name_part and len(name_part) >= 2 and re.search(r"[a-zA-Z]", name_part):
            return name_part

        return None
