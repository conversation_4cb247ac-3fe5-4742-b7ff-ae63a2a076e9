from app.common.helpers.logger import get_logger
from app.integrations.base.document_store import IDocumentStore
from app.integrations.base.embedder import IEmbedder
from app.integrations.base.message_doc_formatter import IMessageDocumentFormatter
from app.integrations.base.message_doc_indexer import (
    DocumentChangeSet,
    IMessageToDocumentIndexer,
)
from app.integrations.base.message_store import IMessageStore
from app.integrations.models import MessageChangelog
from app.integrations.schemas import DocumentData, MessageChangelogData

logger = get_logger()


class MessageWithContextToDocumentIndexer(IMessageToDocumentIndexer):
    """
    A indexer for creating documents from messages with context.

    This class manages the process of generating documents by incorporating
    contextual information such as thread replies or preceding messages. It
    handles document creation, changelog operations, and document rebuilding
    strategies.

    The indexer works with these key components:
    - A message store for retrieving message data
    - A document store for storing and finding documents
    - A document formatter for formatting document content
    - An embedder for generating vector embeddings of document content

    The indexer supports:
    - Generating documents with thread-level or context-level information
    - Cleaning and processing changelog operations
    - Detecting documents that need to be rebuilt based on message changes
    - Embedding document content for vector search capabilities
    """

    def __init__(
        self,
        message_store: IMessageStore,
        document_store: IDocumentStore,
        document_formatter: IMessageDocumentFormatter,
        embedder: IEmbedder,
        context_window_size: int = 10,
    ):
        self.message_store = message_store
        self.document_store = document_store
        self.document_formatter = document_formatter
        self.embedder = embedder
        self.context_window_size = context_window_size

    async def determine_document_changes(
        self, changes: list[MessageChangelogData]
    ) -> DocumentChangeSet:
        """
        Detects which documents need to be rebuilt based on the changes
        """
        document_changeset = DocumentChangeSet()

        # Clean and deduplicate operations
        sorted_changes = sorted(changes, key=lambda x: x.cursor_id)
        final_operations = self._clean_changelog_operations(sorted_changes)

        for message_id, operation in final_operations.items():
            try:
                if operation.operation == MessageChangelog.Operation.DELETE:
                    document_changeset.documents_to_invalidate.add(message_id)
                    # Find documents to rebuild
                    document_changeset.documents_to_rebuild.update(
                        await self._find_document_ids_to_rebuild(message_id)
                    )
                else:  # INSERT or UPDATE
                    document_changeset.documents_to_rebuild.add(message_id)
                    # If UPDATE, find impacted documents
                    if operation.operation == MessageChangelog.Operation.UPDATE:
                        document_changeset.documents_to_rebuild.update(
                            await self._find_document_ids_to_rebuild(message_id)
                        )

            except Exception:
                logger.exception(
                    f"Error detecting documents to rebuild for message {message_id}"
                )

        return document_changeset

    async def generate_document(self, message_id: str) -> DocumentData | None:
        """
        Generates a document for a main message
        """
        main_message = await self.message_store.get_message(message_id)
        if not main_message:
            raise ValueError(f"Message not found: {message_id}")

        # todo: it can be optimized by adding the thread_id in the changelog table and filter in determine_document_changes
        is_reply = (
            main_message.thread_id and main_message.thread_id != main_message.message_id
        )
        if is_reply:
            logger.debug(
                f"Skipping document generation for reply message {main_message.message_id} in thread {main_message.thread_id}"
            )
            return None

        context_messages = None
        replies = None

        tags: set[str] = {f"channel_id:{main_message.channel_id}"}

        if main_message.thread_id:
            replies = await self.message_store.get_thread_replies(
                channel_id=main_message.channel_id,
                message_id=message_id,
            )
            if replies:
                tags.update(reply.message_id for reply in replies)
        else:
            context_messages = await self.message_store.get_main_messages_before(
                channel_id=main_message.channel_id,
                message_id=message_id,
                limit=self.context_window_size,
            )
            if context_messages:
                tags.update(msg.message_id for msg in context_messages)

        content = self.document_formatter.format_document_content(
            main_message=main_message,
            context_messages=context_messages,
            replies=replies,
        )

        embedding = None
        try:
            embedding = await self.embedder.embed_text(content)
        except Exception:
            logger.exception(f"Error generating embedding for message {message_id}")

        document = DocumentData(
            id=message_id,
            content=content,
            source_timestamp=main_message.last_edit_at or main_message.sent_at,
            tags=tags,
        )

        await self.document_store.store_document(document, embedding)
        return document

    async def invalidate_document(self, document_id: str) -> None:
        await self.document_store.delete_document(document_id)

    def _clean_changelog_operations(
        self, changes: list[MessageChangelogData]
    ) -> dict[str, MessageChangelogData]:
        """
        Cleans and validates changelog operations.
        Operations are processed in cursor_id order with these rules:

        1. When processing a DELETE:
        - If previous operation was INSERT: both operations are removed
        - If previous operation was UPDATE: DELETE replaces it
        - If no previous operation: DELETE is kept

        2. When processing an INSERT:
        - If any previous operation exists: error is logged and previous operation is kept
        - If no previous operation: INSERT is kept

        3. When processing an UPDATE:
        - Always ignored if a previous operation exists
        - Only kept if no previous operation

        Logs errors for invalid operation sequences.
        """
        #  Dictionary order is guaranteed to be insertion order since Python 3.7
        #  https://docs.python.org/3/library/stdtypes.html#dict-views
        operations: dict[str, MessageChangelogData] = {}

        for change in changes:
            message_id = change.message_id
            existing_op = operations.get(message_id)

            # No existing operation, just store it
            if not existing_op:
                operations[message_id] = change
                continue

            match change.operation:
                case MessageChangelog.Operation.DELETE:
                    if existing_op.operation == MessageChangelog.Operation.INSERT:
                        # DELETE cancels previous INSERT
                        operations.pop(message_id)
                    elif existing_op.operation == MessageChangelog.Operation.UPDATE:
                        # DELETE wins over UPDATE
                        operations[message_id] = change

                case MessageChangelog.Operation.INSERT:
                    if existing_op:  # Any existing operation before INSERT is an error
                        logger.error(
                            f"Invalid INSERT operation for message {message_id}: "
                            f"found after {existing_op.operation} "
                            f"(INSERT cursor={change.cursor_id}, "
                            f"existing cursor={existing_op.cursor_id})"
                        )
                        # Keep existing operation
                case MessageChangelog.Operation.UPDATE:
                    # UPDATE always loses against existing operations
                    pass

        return operations

    async def _find_document_ids_to_rebuild(self, message_id: str) -> set[str]:
        """
        Finds document ids to rebuild that are impacted by a change on message_id
        """
        return await self.document_store.find_document_ids_by_tag(message_id)
