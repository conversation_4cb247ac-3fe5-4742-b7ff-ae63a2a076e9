from langchain.text_splitter import RecursiveCharacterTextSplitter

from app.integrations.base.chunker import IChunker


class RecursiveChunker(IChunker):
    """
    This chunker splits text recursively using multiple separators, trying to keep
    chunks as close to the target size as possible while respecting natural boundaries.
    """

    def __init__(
        self,
        chunk_size: int = 512,
        chunk_overlap: int = 50,
        separators: list[str] | None = None,
    ):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

        if separators is None:
            separators = ["\n\n", "\n", ". ", " ", ""]

        self.separators = separators

        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=separators,
            length_function=len,
        )

    def chunk(self, text: str) -> list[str]:
        return self.text_splitter.split_text(text.strip())
