from enum import Enum


class IntegrationSource(str, Enum):
    SLACK = "slack"
    TEAMS = "teams"
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"
    GCS = "gcs"
    GOOGLE_CALENDAR = "google_calendar"
    OUTLOOK_CALENDAR = "outlook_calendar"
    GMAIL = "gmail"


class BackendType(str, Enum):
    CRM = "crm"
    MESSAGING = "messaging"
    FILE = "file"
    CALENDAR = "calendar"
    EMAIL = "email"


class ExtensionType(str, Enum):
    TXT = "txt"
    PDF = "pdf"
    PPTX = "pptx"
    DOCX = "docx"
    PNG = "png"
    JPEG = "jpeg"
    JPG = "jpg"
    AVIF = "avif"
    MP4 = "mp4"


class SalesforceObjectType(str, Enum):
    ACCOUNT = "Account"
    OPPORTUNITY = "Opportunity"
    CONTACT = "Contact"
    TASK = "Task"
    EVENT = "Event"


class HubSpotObjectType(str, Enum):
    COMPANY = "company"
    DEAL = "deal"
    CONTACT = "contact"
    TASK = "task"
    MEETING = "meeting"
    GOAL = "goal"
