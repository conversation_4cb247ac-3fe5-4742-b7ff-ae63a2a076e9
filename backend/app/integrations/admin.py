from typing import cast
from uuid import UUID

from wtforms import SelectField

from app.admin import BaseModelView
from app.integrations.base.tenant_model import TenantModel
from app.integrations.models import (
    ChangelogCursor,
    CRMAccountAccess,
    CRMAccountAccessSyncRun,
    Document,
    EmailClientMapping,
    EmailProcessingRun,
    FileProcessingRun,
    MessageChangelog,
    MessageRawData,
    MessagingIngestionRun,
    MessagingProcessingRun,
)
from app.integrations.models.email_client_mapping import EmailMappingType
from app.integrations.types import IntegrationSource


class _IntegrationsModelView(BaseModelView):
    category = "Integrations"
    column_formatters = {
        "tenant_id": lambda m, _: f"{str(cast('TenantModel', m).tenant_id)[:5]}..."
    }
    form_overrides = {"source": SelectField}
    form_args = {
        "source": {
            "choices": [(e.value, e.name) for e in IntegrationSource],
            "coerce": IntegrationSource,
        }
    }


class _MessageIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / Message"


class _CrmIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / CRM"


class _FileIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / File"


class _EmailIntegrationsModelView(_IntegrationsModelView):
    category = "Integrations / Email"


class MessageRawDataAdmin(_MessageIntegrationsModelView, model=MessageRawData):
    autodiscover_order = 10
    column_list = [
        MessageRawData.tenant_id,
        MessageRawData.source,
        MessageRawData.channel_id,
        MessageRawData.message_id,
        MessageRawData.thread_id,
        MessageRawData.parent_id,
        "is_reply",
        MessageRawData.tombstone,
        MessageRawData.sent_at,
        MessageRawData.last_edit_at,
    ]
    column_searchable_list = [MessageRawData.message_id, MessageRawData.thread_id]
    column_default_sort = (MessageRawData.sent_at, True)


class MessageChangelogAdmin(_MessageIntegrationsModelView, model=MessageChangelog):
    autodiscover_order = 15
    column_list = [
        MessageChangelog.tenant_id,
        MessageChangelog.cursor_id,
        MessageChangelog.source,
        MessageChangelog.channel_id,
        MessageChangelog.message_id,
        "operation_label",
        MessageChangelog.created_at,
    ]
    column_default_sort = (MessageChangelog.cursor_id, True)
    column_searchable_list = [MessageChangelog.message_id]
    form_overrides = {
        "operation": SelectField,
        **_MessageIntegrationsModelView.form_overrides,
    }
    form_args = {
        "operation": {
            "choices": [(e.value, e.name) for e in MessageChangelog.Operation],
            "coerce": MessageChangelog.Operation,
        },
        **_MessageIntegrationsModelView.form_args,
    }


class ChangelogCursorAdmin(_MessageIntegrationsModelView, model=ChangelogCursor):
    autodiscover_order = 20
    column_list = [
        ChangelogCursor.tenant_id,
        ChangelogCursor.cursor_id,
        ChangelogCursor.cursor_position,
    ]
    column_default_sort = (MessageChangelog.updated_at, True)


class DocumentAdmin(_MessageIntegrationsModelView, model=Document):
    autodiscover_order = 25
    column_list = [
        Document.tenant_id,
        Document.source,
        Document.document_id,
        Document.source_timestamp,
    ]
    column_details_exclude_list = [Document.embedding]
    form_excluded_columns = [Document.embedding]
    column_default_sort = (Document.source_timestamp, True)
    column_searchable_list = [Document.document_id]


class MessagingIngestionRunAdmin(
    _MessageIntegrationsModelView, model=MessagingIngestionRun
):
    autodiscover_order = 30
    column_list = [
        MessagingIngestionRun.tenant_id,
        MessagingIngestionRun.source,
        MessagingIngestionRun.channel_id,
        MessagingIngestionRun.status,
        MessagingIngestionRun.run_start,
        MessagingIngestionRun.run_end,
        MessagingIngestionRun.messages_processed,
        MessagingIngestionRun.inserts,
        MessagingIngestionRun.updates,
        MessagingIngestionRun.deletes,
    ]
    column_default_sort = (MessagingIngestionRun.run_start, True)
    column_labels = {
        MessagingIngestionRun.messages_processed: "processed",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in MessagingIngestionRun.Status],
            "coerce": MessagingIngestionRun.Status,
        }
    }


class MessagingProcessingRunAdmin(
    _MessageIntegrationsModelView, model=MessagingProcessingRun
):
    autodiscover_order = 30
    column_list = [
        MessagingProcessingRun.tenant_id,
        MessagingProcessingRun.source,
        MessagingProcessingRun.channel_id,
        MessagingProcessingRun.status,
        MessagingProcessingRun.run_start,
        MessagingProcessingRun.run_end,
        MessagingProcessingRun.processed_changes,
        MessagingProcessingRun.regenerated_documents,
        MessagingProcessingRun.deleted_documents,
    ]
    column_default_sort = (MessagingProcessingRun.run_start, True)
    column_labels = {
        MessagingProcessingRun.processed_changes: "processed",
        MessagingProcessingRun.regenerated_documents: "regenerated docs",
        MessagingProcessingRun.deleted_documents: "deleted docs",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in MessagingProcessingRun.Status],
            "coerce": MessagingProcessingRun.Status,
        }
    }


class CRMAccountAccessAdmin(_CrmIntegrationsModelView, model=CRMAccountAccess):
    name = "CRM Account Access"
    name_plural = "CRM Account Access"
    autodiscover_order = 10
    column_list = [
        CRMAccountAccess.tenant_id,
        CRMAccountAccess.source,
        CRMAccountAccess.crm_user_id,
        CRMAccountAccess.crm_account_id,
        CRMAccountAccess.crm_account_name,
        CRMAccountAccess.crm_access_type,
        CRMAccountAccess.crm_access_role,
        CRMAccountAccess.created_at,
    ]
    column_default_sort = (MessageRawData.created_at, True)
    column_searchable_list = [
        CRMAccountAccess.crm_user_id,
        CRMAccountAccess.crm_account_id,
        CRMAccountAccess.crm_account_name,
    ]


class CRMAccessSyncRunAdmin(_CrmIntegrationsModelView, model=CRMAccountAccessSyncRun):
    name = "CRM Account Access"
    name_plural = "CRM Account Access Sync Runs"
    autodiscover_order = 20
    column_list = [
        CRMAccountAccessSyncRun.tenant_id,
        CRMAccountAccessSyncRun.source,
        CRMAccountAccessSyncRun.crm_user_id,
        CRMAccountAccessSyncRun.status,
        CRMAccountAccessSyncRun.run_start,
        CRMAccountAccessSyncRun.run_end,
        CRMAccountAccessSyncRun.new_access_count,
        CRMAccountAccessSyncRun.old_access_count,
    ]
    column_default_sort = (CRMAccountAccessSyncRun.run_start, True)
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in CRMAccountAccessSyncRun.Status],
            "coerce": CRMAccountAccessSyncRun.Status,
        }
    }


class FileProcessingRunAdmin(_FileIntegrationsModelView, model=FileProcessingRun):
    autodiscover_order = 30
    column_list = [
        FileProcessingRun.tenant_id,
        FileProcessingRun.source,
        FileProcessingRun.bucket_name,
        FileProcessingRun.status,
        FileProcessingRun.files_processed,
        FileProcessingRun.deleted_documents,
        FileProcessingRun.run_start,
        FileProcessingRun.run_end,
    ]
    column_default_sort = (FileProcessingRun.run_start, True)
    column_labels = {
        FileProcessingRun.files_processed: "processed",
        FileProcessingRun.deleted_documents: "deleted docs",
    }
    form_overrides = {"status": SelectField}
    form_args = {
        "status": {
            "choices": [(e.value, e.name) for e in FileProcessingRun.Status],
            "coerce": FileProcessingRun.Status,
        }
    }


class EmailClientMappingAdmin(_EmailIntegrationsModelView, model=EmailClientMapping):
    autodiscover_order = 10
    column_list = [
        EmailClientMapping.tenant_id,
        EmailClientMapping.crm_account_id,
        EmailClientMapping.mapping_type,
        EmailClientMapping.domain,
        EmailClientMapping.email_address,
        EmailClientMapping.contact_name_pattern,
        EmailClientMapping.confidence_score,
        EmailClientMapping.created_by_user,
        EmailClientMapping.created_at,
    ]
    column_default_sort = (EmailClientMapping.created_at, True)
    column_searchable_list = [
        EmailClientMapping.crm_account_id,
        EmailClientMapping.domain,
        EmailClientMapping.email_address,
        EmailClientMapping.contact_name_pattern,
    ]
    column_labels = {
        EmailClientMapping.crm_account_id: "CRM Account ID",
        EmailClientMapping.mapping_type: "Type",
        EmailClientMapping.domain: "Domain",
        EmailClientMapping.email_address: "Address",
        EmailClientMapping.contact_name_pattern: "Name Pattern",
        EmailClientMapping.confidence_score: "Confidence",
        EmailClientMapping.created_by_user: "User Created",
    }
    # Exclude auto-managed fields from forms
    form_excluded_columns = [
        EmailClientMapping.created_by_user,
        EmailClientMapping.created_at,
        EmailClientMapping.updated_at,
    ]
    form_overrides = {
        "mapping_type": SelectField,
        "tenant_id": SelectField,
        "crm_account_id": SelectField,
    }

    @property
    def form_args(self):
        args = {
            "mapping_type": {
                "choices": [(e.value, e.name) for e in EmailMappingType],
                "coerce": EmailMappingType,
            },
            "tenant_id": {
                "choices": self._get_tenant_choices(),
                "coerce": UUID,
            },
            "crm_account_id": {
                "choices": self._get_crm_account_choices(),
                "coerce": str,
            },
        }
        return args

    def _get_tenant_choices(self):
        try:
            from sqlalchemy import select

            from app.core.database import SessionLocal
            from app.workspace.models import Environment, Organization

            with SessionLocal() as session:
                stmt = (
                    select(Environment.id, Organization.name)
                    .join(Organization, Environment.organization_id == Organization.id)
                    .where(Organization.is_active == True)
                    .order_by(Organization.name)
                )
                result = session.execute(stmt)
                return [
                    (str(env_id), f"{org_name} ({env_id})")
                    for env_id, org_name in result.all()
                ]
        except Exception:
            # Fallback to empty choices if there's an issue
            return []

    def _get_crm_account_choices(self):
        try:
            from sqlalchemy import select

            from app.core.database import SessionLocal

            with SessionLocal() as session:
                stmt = (
                    select(
                        CRMAccountAccess.crm_account_id,
                        CRMAccountAccess.crm_account_name,
                    )
                    .distinct()
                    .order_by(CRMAccountAccess.crm_account_name)
                )
                result = session.execute(stmt)
                return [
                    (account_id, f"{account_name} ({account_id})")
                    for account_id, account_name in result.all()
                ]
        except Exception:
            # Fallback to empty choices if there's an issue
            return []

    async def insert_model(self, request, data):
        """Override to always set created_by_user to True when creating records."""
        data["created_by_user"] = True
        return await super().insert_model(request, data)


class EmailProcessingRunAdmin(_EmailIntegrationsModelView, model=EmailProcessingRun):
    autodiscover_order = 20
    # Make this admin read-only
    can_create = False
    can_edit = False
    can_delete = False

    column_list = [
        EmailProcessingRun.tenant_id,
        EmailProcessingRun.source,
        EmailProcessingRun.crm_account_id,
        EmailProcessingRun.status,
        EmailProcessingRun.emails_processed,
        EmailProcessingRun.emails_filtered,
        EmailProcessingRun.sync_window_start,
        EmailProcessingRun.sync_window_end,
        EmailProcessingRun.run_start,
        EmailProcessingRun.run_end,
    ]
    column_default_sort = (EmailProcessingRun.run_start, True)
    column_searchable_list = [EmailProcessingRun.crm_account_id]
    column_labels = {
        EmailProcessingRun.crm_account_id: "CRM Account ID",
        EmailProcessingRun.emails_processed: "Processed",
        EmailProcessingRun.emails_filtered: "Filtered",
        EmailProcessingRun.sync_window_start: "Sync Start",
        EmailProcessingRun.sync_window_end: "Sync End",
    }
