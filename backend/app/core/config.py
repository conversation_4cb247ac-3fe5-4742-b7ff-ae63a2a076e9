import os
from typing import Literal

from pydantic import AnyUrl, HttpUrl, PostgresDsn, Field, field_validator, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from app.core.env import Environment


class BaseConfig(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")


class SalesforceConfig(BaseConfig):
    auth_url: HttpUrl | None = None
    token_url: HttpUrl | None = None
    redirect_uri: AnyUrl | None = None

    model_config = SettingsConfigDict(env_prefix="SALESFORCE_")


class GoogleConfig(BaseConfig):
    auth_url: HttpUrl | None = None
    token_url: HttpUrl | None = None
    redirect_uri: AnyUrl | None = None

    model_config = SettingsConfigDict(env_prefix="GOOGLE_")


class HubSpotConfig(BaseConfig):
    auth_url: HttpUrl | None = None
    token_url: HttpUrl | None = None
    redirect_uri: AnyUrl | None = None

    model_config = SettingsConfigDict(env_prefix="HUBSPOT_")


class MailerConfig(BaseConfig):
    email_client: Literal["customerio", "local"] = "local"
    from_email: str = "<EMAIL>"
    customerio_api_key: str = ""
    customerio_region: str = "us"

    model_config = SettingsConfigDict(env_prefix="MAILER_")


class AuthConfig(BaseConfig):
    jwt_secret_key: str = ""
    jwt_algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    refresh_token_expire_days: int = 7
    max_refresh_tokens: int = 5
    one_time_credentials_expire_minutes: int = 10

    model_config = SettingsConfigDict(env_prefix="AUTH_")


class DatabaseConfig(BaseConfig):
    database_url: PostgresDsn = PostgresDsn(
        "postgresql://pearl_user:pearl_pass@localhost:5432/pearl"
    )
    async_database_url: str = "postgresql+asyncpg://pearl_user:pearl_pass@localhost:5432/pearl"
    test_database_url: PostgresDsn = PostgresDsn(
        "postgresql://pearl_user:pearl_pass@localhost:5432/pearl_test"
    )

    @field_validator("database_url", mode="before")
    @classmethod
    def fix_postgres_scheme(cls, v: str) -> str:
        if isinstance(v, str) and v.startswith("postgres://"):
            # Heroku-style URL isn't supported by SQLAlchemy >= 1.4
            return v.replace("postgres://", "postgresql://", 1)
        return v

    @field_validator("test_database_url", mode="before")
    @classmethod
    def fix_test_postgres_scheme(cls, v: str) -> str:
        if isinstance(v, str) and v.startswith("postgres://"):
            return v.replace("postgres://", "postgresql://", 1)
        return v



    @model_validator(mode="after")
    def validate_database_urls(self):
        db_url = self.database_url
        test_db_url = self.test_database_url
        if db_url == test_db_url:
            raise ValueError("database_url and test_database_url must be different")
        return self





class AppConfig(BaseConfig):
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    secret_key: str = ""
    project_title: str = "Pearl API"
    project_version: str = "0.0.1"
    log_level: str = ""
    sqlalchemy_log_level: str = ""
    database: DatabaseConfig = DatabaseConfig()
    frontend_url: str = "http://localhost:3000"
    openai_api_key: str = ""
    mistral_api_key: str = ""
    gemini_api_key: str = ""
    linkup_api_key: str = ""
    langfuse_public_key: str = ""
    langfuse_secret_key: str = ""
    langfuse_host: str = "https://cloud.langfuse.com"
    salesforce: SalesforceConfig = SalesforceConfig()
    hubspot: HubSpotConfig = HubSpotConfig()
    google: GoogleConfig = GoogleConfig()
    mailer: MailerConfig = MailerConfig()
    auth: AuthConfig = AuthConfig()
    session_secret_key: str = ""
    google_oauth_client_id: str = ""
    google_oauth_client_secret: str = ""

    @model_validator(mode="after")
    def set_log_level_if_empty(self):
        if self.log_level == "":
            self.log_level = "DEBUG" if self.debug else "INFO"
        if self.sqlalchemy_log_level == "":
            self.sqlalchemy_log_level = "DEBUG" if self.debug else "INFO"
        return self


class DevConfig(AppConfig):
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = True


class StagingConfig(AppConfig):
    environment: Environment = Environment.STAGING


class ProdConfig(AppConfig):
    environment: Environment = Environment.PRODUCTION


class TestConfig(AppConfig):
    environment: Environment = Environment.TEST
    log_level: str = "WARNING"

    @model_validator(mode="after")
    def set_log_level_if_empty(self):
        # For test config, always ensure log_level is WARNING unless explicitly overridden via env
        if not hasattr(self, "_log_level_set_by_env"):
            self.log_level = "WARNING"
        if self.sqlalchemy_log_level == "":
            self.sqlalchemy_log_level = "WARNING"
        return self


env = os.getenv("APP_ENV", "development")
config: AppConfig
match env:
    case Environment.PRODUCTION:
        config = ProdConfig()
    case Environment.STAGING:
        config = StagingConfig()
    case Environment.TEST:
        config = TestConfig()
    case _:
        config = DevConfig()
