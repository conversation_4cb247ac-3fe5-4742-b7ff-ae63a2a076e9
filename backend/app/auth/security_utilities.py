import secrets
from enum import Enum

import jwt

from app.auth.exceptions import AuthTokenDecodeError
from app.core.config import config


class TokenType(Enum):
    ACCESS = "access"
    REFRESH = "refresh"
    LOGIN = "login"


def create_jwt_token(data: dict) -> str:
    """Create a JWT token."""
    return jwt.encode(
        data, config.auth.jwt_secret_key, algorithm=config.auth.jwt_algorithm
    )


def decode_token(token: str, token_type: TokenType) -> dict:
    """Decode a JWT token."""
    try:
        token_content = jwt.decode(
            token,
            config.auth.jwt_secret_key,
            algorithms=[config.auth.jwt_algorithm],
            options=dict(require_exp=True),
        )
    except jwt.DecodeError:
        raise AuthTokenDecodeError("Token is invalid.")
    except jwt.ExpiredSignatureError:
        raise AuthTokenDecodeError("Token has expired.")

    if token_content["type"] != token_type.value:
        raise AuthTokenDecodeError("Inconsistent token type.")
    return token_content


def generate_auth_code() -> str:
    """Generate a 6-digit auth code.
    Adding 100000 to a random number between 0 and 899999
    ensures that the result is always a 6-digit number.
    """
    return str(secrets.randbelow(900000) + 100000)
