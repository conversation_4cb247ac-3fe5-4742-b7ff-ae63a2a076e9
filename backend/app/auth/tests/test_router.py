from unittest.mock import MagicMock
from uuid import uuid4

import pytest

from app.auth.dependencies import get_auth_service
from app.auth.exceptions import AuthTokenDecodeError
from app.auth.schemas import UserOtcToken, UserTokens
from app.auth.service import AuthService
from app.main import app


@pytest.fixture
def override_auth_service():
    mock_service = MagicMock(spec=AuthService)
    app.dependency_overrides[get_auth_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_auth_service)


@pytest.mark.anyio
async def test_refresh_success(async_client, override_auth_service):
    override_auth_service.revoke_refresh_token_and_create_new_tokens.return_value = (
        UserTokens(
            access_token="access_token",  # noqa: S106
            refresh_token="refresh_token",  # noqa: S106
        )
    )

    response = await async_client.post(
        app.url_path_for("refresh"),
        json={"refresh_token": "valid_refresh_token"},
    )

    assert response.status_code == 200
    assert response.json() == {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
    }


@pytest.mark.anyio
async def test_refresh_invalid_token(async_client):
    response = await async_client.post(
        app.url_path_for("refresh"),
        json={"refresh_token": "invalid_refresh_token"},
    )
    assert response.status_code == 401
    assert response.json() == {"detail": "Token is invalid."}


@pytest.mark.anyio
async def test_protected_route_success(async_client, override_auth_service):
    user_id = uuid4()
    override_auth_service.get_user_id_from_access_token.return_value = user_id

    response = await async_client.get(
        app.url_path_for("protected"),
        headers={"Authorization": "Bearer valid_access_token"},
    )

    assert response.status_code == 200
    assert response.json() == {
        "message": "This is a protected route",
        "user_id": str(user_id),
    }


@pytest.mark.anyio
async def test_protected_route_unauthorized(async_client, override_auth_service):
    # Without Authorization header
    response = await async_client.get(
        app.url_path_for("protected"),
    )
    assert response.status_code == 401

    # Raising an exception in the auth service
    override_auth_service.get_user_id_from_access_token.side_effect = (
        AuthTokenDecodeError("Authentication token error.")
    )

    response = await async_client.get(
        app.url_path_for("protected"),
        headers={"Authorization": "Bearer access_token"},
    )
    assert response.status_code == 401


@pytest.mark.anyio
async def test_logout_success(async_client, override_auth_service):
    override_auth_service.get_user_id_from_access_token.return_value = uuid4()
    override_auth_service.logout.return_value = None

    response = await async_client.post(
        app.url_path_for("logout"),
        headers={"Authorization": "Bearer valid_access_token"},
        json={"refresh_token": "valid_refresh_token"},
    )

    assert response.status_code == 204


@pytest.mark.anyio
async def test_request_otc_(async_client, override_auth_service):
    override_auth_service.maybe_create_and_send_via_email_one_time_credentials.return_value = UserOtcToken(
        token="otc_token"  # noqa: S106
    )

    response = await async_client.post(
        app.url_path_for("request_otc"),
        json={"email": "<EMAIL>"},
    )

    assert response.status_code == 200
    assert response.json() == {"token": "otc_token"}


@pytest.mark.anyio
async def test_verify_otc_token_success(async_client, override_auth_service):
    override_auth_service.verify_one_time_credentials_token.return_value = None

    response = await async_client.post(
        app.url_path_for("verify_otc_token"),
        json={"token": "valid_otc_token"},
    )

    assert response.status_code == 204
    override_auth_service.verify_one_time_credentials_token.assert_called_once_with(
        "valid_otc_token"
    )


@pytest.mark.anyio
async def test_verify_otc_token_invalid(async_client, override_auth_service):
    override_auth_service.verify_one_time_credentials_token.side_effect = (
        AuthTokenDecodeError("Authentication token error.")
    )

    response = await async_client.post(
        app.url_path_for("verify_otc_token"),
        json={"token": "invalid_otc_token"},
    )

    assert response.status_code == 401
    assert response.json() == {"detail": "Authentication token error."}


@pytest.mark.anyio
async def test_verify_otc_code_success(async_client, override_auth_service):
    user_id = uuid4()
    override_auth_service.verify_one_time_credentials_code.return_value = user_id
    override_auth_service.create_new_tokens.return_value = UserTokens(
        access_token="access_token",  # noqa: S106
        refresh_token="refresh_token",  # noqa: S106
    )

    response = await async_client.post(
        app.url_path_for("verify_otc_code"),
        json={"token": "valid_otc_token", "code": "valid_code"},
    )

    assert response.status_code == 200
    assert response.json() == {
        "access_token": "access_token",
        "refresh_token": "refresh_token",
    }


@pytest.mark.anyio
async def test_verify_otc_code_invalid(async_client, override_auth_service):
    override_auth_service.verify_one_time_credentials_code.side_effect = (
        AuthTokenDecodeError("Authentication token error.")
    )

    response = await async_client.post(
        app.url_path_for("verify_otc_code"),
        json={"token": "invalid_otc_token", "code": "invalid_code"},
    )

    assert response.status_code == 401
    assert response.json() == {"detail": "Authentication token error."}
