from pydantic import BaseModel, EmailStr


class UserRequestOtc(BaseModel):
    email: EmailStr


class UserVerifyOtcToken(BaseModel):
    token: str


class UserVerifyOtcCode(BaseModel):
    code: str
    token: str


class UserOtcToken(BaseModel):
    token: str


class UserRefresh(BaseModel):
    refresh_token: str


class UserLogout(BaseModel):
    refresh_token: str


class UserTokens(BaseModel):
    access_token: str
    refresh_token: str
