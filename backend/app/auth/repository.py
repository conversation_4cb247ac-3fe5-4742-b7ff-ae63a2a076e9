from collections.abc import Sequence
from uuid import UUID

from sqlalchemy.ext.asyncio import AsyncSession

from app.auth.models import OneTimeCredentials, RefreshToken, User
from app.common.orm.base_async_repository import BaseAsyncRepository


class UserRepository(BaseAsyncRepository[User]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, User)

    async def get_by_email(self, email: str) -> User | None:
        res = await self._get_by_attrs(email=email)
        return res[0] if res else None


class RefreshTokenRepository(BaseAsyncRepository[RefreshToken]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, RefreshToken)

    async def get_non_revoked_by_user_id(self, user_id: UUID) -> Sequence[RefreshToken]:
        return await self._get_by_attrs(
            user_id=user_id,
            revoked_at=None,
        )

    async def get_by_token(self, token: str) -> RefreshToken | None:
        res = await self._get_by_attrs(token=token)
        return res[0] if res else None


class OneTimeCredentialsRepository(BaseAsyncRepository[OneTimeCredentials]):
    def __init__(self, db_session: AsyncSession):
        super().__init__(db_session, OneTimeCredentials)

    async def get_by_token(self, token: str) -> OneTimeCredentials | None:
        res = await self._get_by_attrs(token=token)
        return res[0] if res else None
