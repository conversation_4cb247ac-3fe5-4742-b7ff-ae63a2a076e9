from fastapi import APIRouter, status

from app.auth.dependencies import (
    AccessTokenDep,
    AuthenticatedUserIdDep,
    AuthServiceDep,
)
from app.auth.schemas import (
    User<PERSON>ogout,
    UserOtcToken,
    UserRefresh,
    UserRequestOtc,
    UserTokens,
    UserVerifyOtcCode,
    UserVerifyOtcToken,
)

router = APIRouter()


"""Otc stands for One Time Credentials"""


@router.post(
    "/request_otc",
    name="request_otc",
    response_model=UserOtcToken,
)
async def otc_request(request_otc: UserRequestOtc, service: AuthServiceDep):
    return await service.maybe_create_and_send_via_email_one_time_credentials(
        request_otc.email,
    )


@router.post(
    "/verify_otc_token",
    name="verify_otc_token",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def verify_otc_token(
    verify_otc_token: UserVerifyOtcToken, service: AuthServiceDep
):
    await service.verify_one_time_credentials_token(verify_otc_token.token)
    return None


@router.post(
    "/verify_otc_code",
    name="verify_otc_code",
    response_model=UserTokens,
)
async def verify_otc_code(verify_otc_code: UserVerifyOtcCode, service: AuthServiceDep):
    user_id = await service.verify_one_time_credentials_code(
        verify_otc_code.token, verify_otc_code.code
    )
    return await service.create_new_tokens(user_id)


@router.post("/refresh", name="refresh", response_model=UserTokens)
async def refresh(
    refresh: UserRefresh,
    service: AuthServiceDep,
):
    refresh_token = refresh.refresh_token
    return await service.revoke_refresh_token_and_create_new_tokens(refresh_token)


@router.post(
    "/logout",
    name="logout",
    status_code=status.HTTP_204_NO_CONTENT,
)
async def logout(
    logout: UserLogout,
    access_token: AccessTokenDep,
    service: AuthServiceDep,
):
    refresh_token = logout.refresh_token
    await service.logout(access_token, refresh_token)
    return None


@router.get("/protected", name="protected", include_in_schema=False)
async def protected(
    user_id: AuthenticatedUserIdDep,
):
    """Dummy protected route for testing."""
    return {"message": "This is a protected route", "user_id": user_id}
