#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Database Seeder

This script seeds the database with development data for the Pearl application.

Usage:
    python scripts/seed.py                  # default: prompt user for firstname, lastname and email
    python scripts/seed.py -s | --skip-user # skip user creation (short flag)
    python scripts/seed.py -c | --clear     # clear database before seeding (short flag)
"""

import argparse
import sys
import os

# Add the app directory to Python path so we can import models
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import IntegrityError

from app.core.config import config
from app.auth.models import User
from app.workspace.models.organization import Organization
from app.workspace.models.environment import Environment
from app.workspace.models.organization_member import OrganizationMember
from app.workspace.models.integration_config import IntegrationConfig
from app.workspace.models.salesforce_field_mapping import SalesforceFieldMapping
from app.workspace.types import EnvironmentType, IntegrationType
from app.integrations.types import IntegrationSource


def get_user_input() -> tuple[str, str, str]:
    print("🌱 Setting up development user...")
    print("Please provide your details for the development environment:")
    
    try:
        first_name = input("First name: ").strip()
        last_name = input("Last name: ").strip()
        email = input("Email: ").strip()
    except EOFError:
        # Handle non-interactive environments
        print("⚠️ Non-interactive environment detected. Using default test user.")
        first_name = "John"
        last_name = "Doe"
        email = "<EMAIL>"
    
    if not all([first_name, last_name, email]):
        print("❌ All fields are required!")
        sys.exit(1)
    
    if "@" not in email:
        print("❌ Please enter a valid email address!")
        sys.exit(1)
    
    return first_name, last_name, email


def create_user(session, first_name: str, last_name: str, email: str) -> User:
    existing_user = session.query(User).filter(User.email == email).first()
    if existing_user:
        print(f"✅ User already exists: {existing_user.full_name} ({existing_user.email})")
        return existing_user
    
    user = User(
        first_name=first_name,
        last_name=last_name,
        email=email
    )
    session.add(user)
    session.flush()
    print(f"✅ Created user: {user.full_name} ({user.email})")
    return user


def create_organization(session, owner: User) -> Organization:
    org_name = "Pearl Test Org"
    existing_org = session.query(Organization).filter(Organization.name == org_name).first()
    if existing_org:
        print(f"✅ Organization already exists: {existing_org.name}")
        return existing_org
    
    organization = Organization(
        name=org_name,
        domain=None,
        is_active=True,
        owner_id=owner.id
    )
    session.add(organization)
    session.flush()
    print(f"✅ Created organization: {organization.name}")
    return organization


def create_environment(session, organization: Organization) -> Environment:
    existing_env = session.query(Environment).filter(
        Environment.organization_id == organization.id,
        Environment.type == EnvironmentType.PROD
    ).first()
    
    if existing_env:
        print(f"✅ Environment already exists: {existing_env.type} for {organization.name}")
        return existing_env
    
    environment = Environment(
        organization_id=organization.id,
        type=EnvironmentType.PROD
    )
    session.add(environment)
    session.flush()
    print(f"✅ Created environment: {environment.type} for {organization.name}")
    return environment


def create_organization_member(session, user: User, organization: Organization) -> OrganizationMember:
    existing_member = session.query(OrganizationMember).filter(
        OrganizationMember.user_id == user.id,
        OrganizationMember.organization_id == organization.id
    ).first()
    
    if existing_member:
        print(f"✅ Organization member already exists: {user.full_name} in {organization.name}")
        return existing_member
    
    member = OrganizationMember(
        user_id=user.id,
        organization_id=organization.id,
        is_admin=True
    )
    session.add(member)
    session.flush()
    print(f"✅ Created organization member: {user.full_name} in {organization.name} (admin)")
    return member


def create_integration_configs(session, environment: Environment) -> list[IntegrationConfig]:
    configs = []
    
    # Salesforce CRM Integration
    salesforce_config_data = {
        "source": IntegrationSource.SALESFORCE,
        "integration_type": IntegrationType.CRM,
        "credentials": {
            # TODO: We're seeding for dev, but maybe we should hide the credentials that are below
            "client_id": "3MVG9suI4ZYS8sz4GMFTU54EYWPJ_zgWnD7Gw5NNl73lpxV6a2CJ.n2PXeyPyUs2ESMYJosoATXPKd_3.bweo",
            "client_secret": "EEDD454A813CB5F506D670987FEB3A462E55B35F68BBCA1ED4108850D998D165"
        },
        "is_active": True
    }
    
    # Gmail Email Integration
    gmail_config_data = {
        "source": IntegrationSource.GMAIL,
        "integration_type": IntegrationType.EMAIL,
        "credentials": {
            "client_id": "*********************************************.apps.googleusercontent.com",
            "client_secret": "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",
            "token_uri": "https://oauth2.googleapis.com/token",
            "scopes": [
                "https://www.googleapis.com/auth/gmail.readonly",
                "https://www.googleapis.com/auth/gmail.send",
                "https://www.googleapis.com/auth/gmail.modify"
            ]
        },
        "is_active": True
    }
    
    # Google Calendar Integration
    calendar_config_data = {
        "source": IntegrationSource.GOOGLE_CALENDAR,
        "integration_type": IntegrationType.CALENDAR,
        "credentials": {
            "client_id": "*********************************************.apps.googleusercontent.com",
            "client_secret": "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",
            "token_uri": "https://oauth2.googleapis.com/token",
            "scopes": ["https://www.googleapis.com/auth/calendar"]
        },
        "is_active": True
    }
    
    integration_configs_data = [salesforce_config_data, gmail_config_data, calendar_config_data]
    
    for config_data in integration_configs_data:
        # Check if integration already exists
        existing_config = session.query(IntegrationConfig).filter(
            IntegrationConfig.environment_id == environment.id,
            IntegrationConfig.source == config_data["source"]
        ).first()
        
        if existing_config:
            print(f"✅ Integration config already exists: {config_data['source']} for {environment.organization.name}")
            configs.append(existing_config)
            continue
        
        integration_config = IntegrationConfig(
            environment_id=environment.id,
            source=config_data["source"],
            integration_type=config_data["integration_type"],
            credentials=config_data["credentials"],
            is_active=config_data["is_active"]
        )
        session.add(integration_config)
        session.flush()
        configs.append(integration_config)
        print(f"✅ Created integration config: {config_data['source']} for {environment.organization.name}")
    
    return configs


def create_salesforce_field_mappings(session, organization: Organization) -> list[SalesforceFieldMapping]:
    mappings = []
    
    # Check if mapping already exists (there should be only one per organization)
    existing_mapping = session.query(SalesforceFieldMapping).filter(
        SalesforceFieldMapping.organization_id == organization.id
    ).first()
    
    if existing_mapping:
        print(f"✅ Salesforce field mapping already exists for {organization.name}")
        mappings.append(existing_mapping)
        return mappings
    
    # Create default Salesforce field mapping for the organization
    field_mapping = SalesforceFieldMapping(
        organization_id=organization.id,
        opportunity_amount_field="Amount",
        opportunity_stage_field="StageName",
        opportunity_owner_field="OwnerId",
        opportunity_probability_field="Probability",
        closed_won_stage_pattern="%Closed Won%",
        closed_lost_stage_pattern="%Closed Lost%",
        forecast_probability_multiplier=0.5,
        use_probability_field=False,
        quota_object="ForecastingQuota",
        quota_amount_field="QuotaAmount",
        quota_user_field="QuotaOwnerId",
        metrics_months_limit=3
    )
    session.add(field_mapping)
    session.flush()
    mappings.append(field_mapping)
    print(f"✅ Created Salesforce field mapping with default settings for {organization.name}")
    
    return mappings


def check_database_connectivity():
    try:
        engine = create_engine(str(config.database.database_url))
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            result.fetchone()
        print("✅ Database connection successful")
        return engine
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        print("Make sure your database is running and the connection string is correct.")
        sys.exit(1)


def check_tables_exist(session):
    try:
        # Try to query a core table
        session.execute(text("SELECT 1 FROM \"user\" LIMIT 1"))
        print("✅ Database tables exist")
    except Exception as e:
        print(f"❌ Database tables not found: {e}")
        print("Please run 'alembic upgrade head' to create the database schema first.")
        sys.exit(1)


def is_database_empty(session) -> bool:
    user_count = session.query(User).count()
    return user_count == 0


def clear_database(session):
    print("🗑️  Clearing database...")
    
    # List of tables to clear to avoid clearing stuff that shouldn't be cleared
    tables_to_clear = [
        'salesforce_field_mapping',
        'integration_config', 
        'organization_member',
        'environment',
        'refresh_token',
        'one_time_credentials',
        'organization',
        '"user"'
    ]
    
    try:
        for table_name in tables_to_clear:
            try:
                session.execute(text(f"TRUNCATE TABLE {table_name} CASCADE;"))
                clean_name = table_name.replace('"', '')
                print(f"  ✅ Cleared table: {clean_name}")
            except Exception as e:
                clean_name = table_name.replace('"', '')
                print(f"  ⚠️ Could not clear table {clean_name}: {e}")
        
        session.commit()
        print("✅ Database cleared successfully!")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error clearing database: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description='Seed the development database')
    parser.add_argument('--skip-user', '-s', action='store_true', 
                      help='Skip user creation and prompting')
    parser.add_argument('--clear', '-c', action='store_true',
                      help='Clear all data from database before seeding')
    args = parser.parse_args()
    
    print("🌱 Pearl Development Database Seeder")
    print("=" * 50)
    
    # Check database connectivity
    engine = check_database_connectivity()
    
    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = SessionLocal()
    
    try:
        # Check if tables exist
        check_tables_exist(session)
        
        # Clear database if requested
        if args.clear:
            clear_database(session)
        
        # Handle user creation
        user = None
        if not args.skip_user:
            if is_database_empty(session):
                first_name, last_name, email = get_user_input()
                user = create_user(session, first_name, last_name, email)
            else:
                print("✅ Database already has users, skipping user prompting")
                # Use the first user as the organization owner
                user = session.query(User).first()
                print(f"✅ Using existing user: {user.full_name} ({user.email})")
        else:
            print("✅ Skipping user creation (--skip-user flag)")
            if not is_database_empty(session):
                user = session.query(User).first()
                print(f"✅ Using existing user: {user.full_name} ({user.email})")
            else:
                print("⚠️ Database is empty but --skip-user flag was used.")
                print("Please run without --skip-user flag to create a user first.")
                return
        
        # Create organization and related data (requires a user as owner)
        if user:
            organization = create_organization(session, user)
            environment = create_environment(session, organization)
            create_organization_member(session, user, organization)
            integration_configs = create_integration_configs(session, environment)
            field_mappings = create_salesforce_field_mappings(session, organization)
            
            # Commit all changes
            session.commit()
            print("\n" + "=" * 50)
            print("🎉 Database seeding completed successfully!")
            print("=" * 50)
            print(f"👤 User: {user.full_name} ({user.email})")
            print(f"🏢 Organization: {organization.name}")
            print(f"🌍 Environment: {environment.type}")
            print(f"🔌 Integration Configs: {len(integration_configs)} created")
            print(f"🗂️  Field Mappings: {len(field_mappings)} created")
        else:
            print("⚠️ No user available for organization creation.")
            print("Please run without --skip-user flag to create a user first.")
        
    except IntegrityError as e:
        session.rollback()
        print(f"❌ Database integrity error: {e}")
        print("This usually means the data already exists or there's a constraint violation.")
    except Exception as e:
        session.rollback()
        print(f"❌ Error during seeding: {e}")
        raise
    finally:
        session.close()


if __name__ == "__main__":
    main()
