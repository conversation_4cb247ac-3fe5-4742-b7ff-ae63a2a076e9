from time import sleep

from poc.sfdc.data_fetcher import SalesforceDataFetcher, get_salesforce_client
from poc.sfdc.db import init_db
from poc.sfdc.field_sync import sync_fields
from poc.sfdc.object_sync import load_all_objects, sync_objects

if __name__ == "__main__":
    con = init_db()
    data_fetcher = SalesforceDataFetcher(sf_client=get_salesforce_client())

    sync_fields(con, data_fetcher)
    load_all_objects(con, data_fetcher)

    # Run every minute to simulate a periodic job
    while True:
        sync_objects(con, data_fetcher)
        sleep(60)
