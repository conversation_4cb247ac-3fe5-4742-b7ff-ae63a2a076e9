from datetime import datetime
from json import dumps
from sqlite3 import Connection

import pytz

from poc.sfdc.data_fetcher import SalesforceDataFetcher, SalesforceTable
from poc.sfdc.db import (
    bulk_insert_objects,
    delete_sfdc_object,
    get_last_synced_at,
    get_sfdc_object,
    insert_sfdc_object,
    update_last_synced_at,
    update_sfdc_object,
)


def load_all_objects(con: Connection, data_fetcher: SalesforceDataFetcher) -> None:
    for table, records in [
        (SalesforceTable.ACCOUNT, data_fetcher.get_all_account_records()),
        (SalesforceTable.CONTACT, data_fetcher.get_all_contact_records()),
        (SalesforceTable.OPPORTUNITY, data_fetcher.get_all_opportunity_records()),
        (SalesforceTable.NOTE, data_fetcher.get_all_note_records()),
    ]:
        now = datetime.now(pytz.utc)
        bulk_insert_objects(
            con,
            table.value,
            [{"id": record["Id"], "data": dumps(record)} for record in records],
            now,
        )
        print(f"Loaded {len(records)} {table.value} records")


def sync_objects(con: Connection, data_fetcher: SalesforceDataFetcher) -> None:
    now = datetime.now(pytz.utc)
    last_synced_at = datetime.fromisoformat(get_last_synced_at(con))
    data_fetcher.set_time_boundaries(latest_sync_datetime=last_synced_at, now=now)
    print(f"last_synced_at: {last_synced_at}")
    for table, records in [
        (SalesforceTable.ACCOUNT, data_fetcher.get_updated_account_records()),
        (SalesforceTable.CONTACT, data_fetcher.get_updated_contact_records()),
        (SalesforceTable.OPPORTUNITY, data_fetcher.get_updated_opportunity_records()),
        (SalesforceTable.NOTE, data_fetcher.get_updated_note_records()),
    ]:
        _sync_object_records(con, table, records)

    for table, ids in [
        (SalesforceTable.ACCOUNT, data_fetcher.get_deleted_account_ids()),
        (SalesforceTable.CONTACT, data_fetcher.get_deleted_contact_ids()),
        (SalesforceTable.OPPORTUNITY, data_fetcher.get_deleted_opportunity_ids()),
        (SalesforceTable.NOTE, data_fetcher.get_deleted_note_ids()),
    ]:
        _delete_object_records(con, table, ids)
    update_last_synced_at(con, now)
    print(f"Updated last_synced_at to {now}")


def _sync_object_records(
    con: Connection, table: SalesforceTable, records: list[dict]
) -> None:
    for record in records:
        now = datetime.now(pytz.utc)
        record_as_json = dumps(record)
        existing_record = get_sfdc_object(con, record["Id"])
        if existing_record is not None:
            print(f"Updating {table.value} record {record['Id']}")
            update_sfdc_object(
                con=con,
                id=record["Id"],
                object_name=table.value,
                data=record_as_json,
                updated_at=now,
            )
        else:
            print(f"Inserting {table.value} record {record['Id']}")
            insert_sfdc_object(
                con=con,
                id=record["Id"],
                object_name=table.value,
                data=record_as_json,
                created_at=now,
                updated_at=now,
            )


def _delete_object_records(
    con: Connection, table: SalesforceTable, ids: list[str]
) -> None:
    for id in ids:
        print(f"Deleting {table.value} record {id}")
        delete_sfdc_object(con, id)
