import os
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import cast

from simple_salesforce import (
    Salesforce as SalesforceClient,
    SFType,
    format_soql,
)


class SalesforceTable(Enum):
    ACCOUNT = "Account"
    CONTACT = "Contact"
    OPPORTUNITY = "Opportunity"
    NOTE = "Note"


@dataclass
class SalesforceField:
    name: str
    label: str


class SalesforceDataFetcher:
    latest_sync_datetime: datetime
    now: datetime

    def __init__(self, sf_client: SalesforceClient) -> None:
        self.sf_client = sf_client

    def set_time_boundaries(
        self, latest_sync_datetime: datetime, now: datetime
    ) -> None:
        sandbox_creation_datetime = datetime.fromisoformat("2025-03-19T00:00:00+00:00")
        self.latest_sync_datetime = max(latest_sync_datetime, sandbox_creation_datetime)
        self.now = now

    def get_all_account_records(self) -> list[dict]:
        return self._get_all_object_records(table=SalesforceTable.ACCOUNT)

    def get_updated_account_records(self) -> list[dict]:
        return self._get_updated_object_records(table=SalesforceTable.ACCOUNT)

    def get_deleted_account_ids(self) -> list[str]:
        return self._get_deleted_object_ids(table=SalesforceTable.ACCOUNT)

    def get_all_contact_records(self) -> list[dict]:
        return self._get_all_object_records(table=SalesforceTable.CONTACT)

    def get_updated_contact_records(self) -> list[dict]:
        return self._get_updated_object_records(table=SalesforceTable.CONTACT)

    def get_deleted_contact_ids(self) -> list[str]:
        return self._get_deleted_object_ids(table=SalesforceTable.CONTACT)

    def get_all_opportunity_records(self) -> list[dict]:
        return self._get_all_object_records(table=SalesforceTable.OPPORTUNITY)

    def get_updated_opportunity_records(self) -> list[dict]:
        return self._get_updated_object_records(table=SalesforceTable.OPPORTUNITY)

    def get_deleted_opportunity_ids(self) -> list[str]:
        return self._get_deleted_object_ids(table=SalesforceTable.OPPORTUNITY)

    def get_all_note_records(self) -> list[dict]:
        return self._get_all_object_records(table=SalesforceTable.NOTE)

    def get_updated_note_records(self) -> list[dict]:
        return self._get_updated_object_records(table=SalesforceTable.NOTE)

    def get_deleted_note_ids(self) -> list[str]:
        return self._get_deleted_object_ids(table=SalesforceTable.NOTE)

    def _get_object_client_from_table_name(self, table_name: str) -> SFType:
        if self.sf_client is None:
            raise RuntimeError("Salesforce client is not initialized.")
        table_name_to_object_client = {
            SalesforceTable.ACCOUNT.value: self.sf_client.Account,
            SalesforceTable.CONTACT.value: self.sf_client.Contact,
            SalesforceTable.OPPORTUNITY.value: self.sf_client.Opportunity,
            SalesforceTable.NOTE.value: self.sf_client.Note,
        }
        return cast("SFType", table_name_to_object_client[table_name])

    def _get_deleted_object_ids(self, table: SalesforceTable) -> list[str]:
        res = self._get_object_client_from_table_name(table.value).deleted(
            start=self.latest_sync_datetime, end=self.now
        )
        return [r["id"] for r in res["deletedRecords"]]

    def _get_updated_object_ids(self, table: SalesforceTable) -> list[str]:
        return self._get_object_client_from_table_name(table.value).updated(
            start=self.latest_sync_datetime, end=self.now
        )["ids"]

    def _get_updated_object_records(self, table: SalesforceTable) -> list[dict]:
        object_ids = self._get_updated_object_ids(
            table=table,
        )
        if len(object_ids) == 0:
            return []
        # TODO: use bulk + lazy_operation for large number of records
        res = self.sf_client.query(
            format_soql(
                "SELECT FIELDS(ALL) FROM {table_name:literal} WHERE Id IN {ids}",
                table_name=table.value,
                ids=object_ids,
            )
        )
        return res["records"]

    def _get_all_object_records(self, table: SalesforceTable) -> list[dict]:
        res = self.sf_client.query_all(
            format_soql(
                "SELECT FIELDS(ALL) FROM {table_name:literal} LIMIT 200",
                table_name=table.value,
            )
        )
        return res["records"]

    def get_account_fields(self) -> list[SalesforceField]:
        return self._get_object_fields(table=SalesforceTable.ACCOUNT)

    def get_contact_fields(self) -> list[SalesforceField]:
        return self._get_object_fields(table=SalesforceTable.CONTACT)

    def get_opportunity_fields(self) -> list[SalesforceField]:
        return self._get_object_fields(table=SalesforceTable.OPPORTUNITY)

    def get_note_fields(self) -> list[SalesforceField]:
        return self._get_object_fields(table=SalesforceTable.NOTE)

    def _get_object_fields(self, table: SalesforceTable) -> list[SalesforceField]:
        raw_fields = self._get_object_client_from_table_name(table.value).describe()[
            "fields"
        ]
        return [SalesforceField(name=f["name"], label=f["label"]) for f in raw_fields]


def get_salesforce_client() -> SalesforceClient:
    # TODO: get token via OAuth2?
    username = os.environ["SFDC_USERNAME"]
    password = os.environ["SFDC_PASSWORD"]
    security_token = os.environ["SFDC_SECURITY_TOKEN"]
    sf_client = SalesforceClient(
        username=username, password=password, security_token=security_token
    )
    return sf_client


def get_data_fetcher() -> SalesforceDataFetcher:
    salesforce_client = get_salesforce_client()
    return SalesforceDataFetcher(
        sf_client=salesforce_client,
    )
