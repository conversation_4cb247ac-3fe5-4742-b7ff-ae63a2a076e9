# Salesforce Syncer - Proof of concept

## Run it

1. Export Salesforce credentials

```
export SFDC_USERNAME="<username>"
export SFDC_PASSWORD="<password>"
export SFDC_SECURITY_TOKEN="<security_token>"
```

2. In the repository root directory, run the following
```
$ poetry run python -m poc.sfdc.run
```

## How it works

- The poc is using the Salesforce REST API to fetch the data
- The data is then stored in a local SQLite database
- 3 Salesforce objects are fetched: Account, Contact, and Opportunity
- Both records and field descriptions (actually, the label) are fetched for each object
- Feeding a RAG prompt with a text dump of both a given record and the field description as the context should allow the model to answer questions about the record
