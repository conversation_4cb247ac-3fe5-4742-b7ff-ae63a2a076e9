import os
import threading
import time

from slack_sdk import <PERSON><PERSON><PERSON>
from slack_sdk.socket_mode import SocketModeClient
from slack_sdk.socket_mode.request import SocketModeRequest
from slack_sdk.socket_mode.response import SocketModeResponse

from poc.slack_ingestion.db import (
    delete_message,
    init_db,
    insert_message,
    load_all_messages,
    update_message,
)


def get_last_edit_timestamp(msg):
    return float(msg.get("edited", {}).get("ts", msg["ts"]))


def sync_channel_periodically(channel_id, slack_token, conn, interval=600):
    slack_client = WebClient(token=slack_token)
    while True:
        cursor = None
        fetched_messages = {}

        while True:
            response = slack_client.conversations_history(
                channel=channel_id, cursor=cursor
            )
            messages = response.get("messages", [])
            for msg in messages:
                subtype = msg.get("subtype")
                if subtype not in (None, "tombstone"):
                    continue

                if subtype == "tombstone":
                    msg["is_tombstone"] = True
                msg_id = msg.get("ts")

                fetched_messages[msg_id] = msg

                if msg.get("thread_ts") == msg_id:
                    replies_response = slack_client.conversations_replies(
                        channel=channel_id, ts=msg_id
                    )
                    replies = replies_response.get("messages", [])
                    for reply in replies[1:]:  # first message is the main message
                        reply_id = reply.get("ts")
                        reply["parent_id"] = msg_id
                        fetched_messages[reply_id] = reply
            cursor = response.get("response_metadata", {}).get("next_cursor")
            if not cursor:
                break

        # not optimised
        db_messages = load_all_messages(conn)
        for msg_id, msg in fetched_messages.items():
            parent_id = msg.get("parent_id")
            msg_updated_at = get_last_edit_timestamp(msg)

            stored = db_messages.get(msg_id)
            is_tombstone = msg.get("is_tombstone", False)
            stored_is_tombstone = stored["is_tombstone"] if stored else False

            if msg_id not in db_messages:
                insert_message(
                    conn,
                    msg_id,
                    parent_id,
                    msg.get("text"),
                    msg_updated_at,
                    is_tombstone,
                )
            else:
                if msg_updated_at > stored.get("updated_at", 0) or (
                    is_tombstone and not stored_is_tombstone
                ):
                    update_message(
                        conn,
                        msg_id,
                        parent_id,
                        msg.get("text"),
                        msg_updated_at,
                        is_tombstone,
                    )

        stored_ids = set(db_messages.keys())
        fetched_ids = set(fetched_messages.keys())
        for msg_id in stored_ids - fetched_ids:
            msg = db_messages[msg_id]
            delete_message(conn, msg_id, msg.get("parent_id"))

        time.sleep(interval)


def process_slack_event(event_data, conn):
    event_type = event_data.get("type")
    subtype = event_data.get("subtype")

    if event_type == "message":
        if subtype is None:
            msg_id = event_data.get("ts")
            thread_ts = event_data.get("thread_ts")
            parent_id = thread_ts if thread_ts != msg_id else None
            if parent_id:
                event_data["parent_id"] = parent_id
            insert_message(conn, msg_id, parent_id, event_data.get("text"), time.time())
        elif subtype == "message_changed":
            updated_message = event_data.get("message")
            msg_id = updated_message.get("ts")
            thread_ts = updated_message.get("thread_ts")
            parent_id = thread_ts if thread_ts != msg_id else None
            if parent_id:
                updated_message["parent_id"] = parent_id
            msg_subtype = updated_message.get("subtype")
            is_tombstone = msg_subtype == "tombstone"
            update_message(
                conn,
                msg_id,
                parent_id,
                updated_message.get("text"),
                time.time(),
                is_tombstone,
            )
        elif subtype == "message_deleted":
            msg_id = event_data.get("deleted_ts")
            previous_message = event_data.get("previous_message")
            thread_ts = previous_message.get("thread_ts")
            parent_id = thread_ts if thread_ts != msg_id else None
            delete_message(conn, msg_id, parent_id)


def slack_event_handler(client, req: SocketModeRequest, conn):
    if req.type == "events_api":
        payload = req.payload
        event_data = payload.get("event", {})
        process_slack_event(event_data, conn)
        response = SocketModeResponse(envelope_id=req.envelope_id)
        client.send_socket_mode_response(response)


def start_event_api_listener(slack_app_token, conn):
    socket_client = SocketModeClient(app_token=slack_app_token)
    socket_client.socket_mode_request_listeners.append(
        lambda client, req: slack_event_handler(client, req, conn)
    )
    socket_client.connect()
    return socket_client


if __name__ == "__main__":
    slack_bot_token = os.getenv(
        "SLACK_TOKEN", "*********************************************************"
    )
    slack_app_token = os.getenv(
        "SLACK_APP_TOKEN",
        "xapp-1-A08K1HCJQP2-8636756503585-0ed616c6a064c4137aa43adfde1e54cfe17a6f600cddd9a76598c9d6c10ee753",
    )
    channel_id = "C08J70KHB6J"
    conn = init_db()

    event_thread = threading.Thread(
        target=start_event_api_listener, args=(slack_app_token, conn), daemon=True
    )
    event_thread.start()

    sync_thread = threading.Thread(
        target=sync_channel_periodically,
        args=(channel_id, slack_bot_token, conn, 600),
        daemon=True,
    )
    sync_thread.start()

    while True:
        time.sleep(10)
