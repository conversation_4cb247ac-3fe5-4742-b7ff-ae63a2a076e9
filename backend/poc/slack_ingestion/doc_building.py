import time

from poc.slack_ingestion.db import (
    delete_doc,
    get_last_oplog_id,
    init_db,
    insert_or_update_doc,
    load_all_messages,
    load_oplog_entries,
    update_last_oplog_id,
)


def build_document_for_message(msg_id, all_msgs, window_size=5):
    main_msg = all_msgs[msg_id]
    main_text = main_msg["data"]
    document_text = main_text

    replies = []
    for m_id, info in all_msgs.items():
        if info.get("parent_id") == msg_id:
            try:
                ts = float(m_id)
            except Exception:
                ts = 0
            replies.append((ts, m_id))
    if replies:
        replies.sort(key=lambda x: x[0])
        replies_text = "\n".join(all_msgs[m_id]["data"] for _, m_id in replies)
        document_text = f"{main_text}\n--- Replies ---\n{replies_text}"
    else:
        standalone_ids = [
            m_id
            for m_id, info in all_msgs.items()
            if not info["parent_id"] and not info["is_tombstone"]
        ]

        try:
            standalone_ids = sorted(standalone_ids, key=lambda m_id: float(m_id))
        except Exception:
            print("Error while getting standalone ids")
            pass

        try:
            idx = standalone_ids.index(msg_id)
        except ValueError:
            idx = 0
        if idx > 0:
            start = max(0, idx - window_size)
            context_texts = []
            for i in standalone_ids[start:idx]:
                text = all_msgs[i]["data"]
                if text:
                    context_texts.append(text)
            if context_texts:
                context_text = "\n".join(context_texts)
                document_text = f"{main_text}\n--- Context ---\n{context_text}"

    return document_text


def process_oplog(conn, window_size=5):
    last_oplog_id = get_last_oplog_id(conn)
    entries = load_oplog_entries(conn, last_oplog_id)

    all_msgs = load_all_messages(conn)

    doc_to_build_ids = set()
    doc_to_delete_ids = []
    for entry in entries:
        oplog_id, op, message_id, parent_id, timestamp = entry
        if op in ("insert", "update", "tombstone"):
            doc_target_id = parent_id or message_id
            doc_to_build_ids.add(doc_target_id)
        elif op == "delete":
            if parent_id:
                doc_to_build_ids.add(parent_id)
            else:
                doc_to_delete_ids.append(message_id)
        last_oplog_id = oplog_id

    for doc_id in doc_to_build_ids:
        doc_text = build_document_for_message(doc_id, all_msgs, window_size)
        if doc_text is not None:
            insert_or_update_doc(conn, doc_id, doc_text, time.time())

    for doc_id in doc_to_delete_ids:
        delete_doc(conn, doc_id)

    update_last_oplog_id(conn, last_oplog_id)


if __name__ == "__main__":
    conn = init_db()
    process_oplog(conn, window_size=5)
