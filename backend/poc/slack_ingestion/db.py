import sqlite3
import time


def init_db(db_path="local_slack.db"):
    conn = sqlite3.connect(db_path, check_same_thread=False)
    c = conn.cursor()
    c.execute("""
        CREATE TABLE IF NOT EXISTS slack_messages (
            id TEXT PRIMARY KEY,
            parent_id TEXT,
            data TEXT,
            is_tombstone BOOLEAN,
            updated_at REAL
        )
    """)
    c.execute("""
        CREATE TABLE IF NOT EXISTS oplog (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            op TEXT,
            message_id TEXT,
            parent_id TEXT,
            timestamp REAL
        )
    """)

    c.execute("""
        CREATE TABLE IF NOT EXISTS docs (
            id TEXT PRIMARY KEY,
            text TEXT,
            updated_at REAL
        )
    """)

    c.execute("""
        CREATE TABLE IF NOT EXISTS doc_build_cursor (
            id INTEGER PRIMARY KEY,
            last_oplog_id INTEGER
        )
    """)
    conn.commit()

    c.execute("SELECT COUNT(*) FROM doc_build_cursor")
    if c.fetchone()[0] == 0:
        c.execute("INSERT INTO doc_build_cursor (id, last_oplog_id) VALUES (1, 0)")
        conn.commit()
    conn.commit()
    return conn


def insert_message(conn, msg_id, parent_id, data, updated_at, is_tombstone=False):
    try:
        op = "tombstone" if is_tombstone else "insert"
        with conn:
            conn.execute(
                """
                INSERT INTO slack_messages (id, parent_id, data, is_tombstone, updated_at)
                VALUES (?, ?, ?, ?, ?)
            """,
                (msg_id, parent_id, data, is_tombstone, updated_at),
            )
            conn.execute(
                """
                INSERT INTO oplog (op, message_id, parent_id, timestamp)
                VALUES (?, ?, ?, ?)
            """,
                (op, msg_id, parent_id, time.time()),
            )
    except sqlite3.Error as e:
        print("Insert error", e)


def update_message(conn, msg_id, parent_id, data, updated_at, is_tombstone=False):
    try:
        op = "tombstone" if is_tombstone else "update"
        with conn:
            conn.execute(
                """
                UPDATE slack_messages
                SET parent_id = ?, data = ?, updated_at = ?, is_tombstone = ?
                WHERE id = ?
            """,
                (parent_id, data, updated_at, is_tombstone, msg_id),
            )
            conn.execute(
                """
                INSERT INTO oplog (op, message_id, parent_id, timestamp)
                VALUES (?, ?, ?, ?)
            """,
                (op, msg_id, parent_id, time.time()),
            )
    except sqlite3.Error as e:
        print("Update error", e)


def delete_message(conn, msg_id, parent_id):
    try:
        with conn:
            conn.execute("DELETE FROM slack_messages WHERE id = ?", (msg_id,))
            conn.execute(
                """
                INSERT INTO oplog (op, message_id, parent_id, timestamp)
                VALUES (?, ?, ?, ?)
            """,
                ("delete", msg_id, parent_id, time.time()),
            )
    except sqlite3.Error as e:
        print("Delete error", e)


def load_all_messages(conn):
    c = conn.cursor()
    c.execute(
        "SELECT id, data, updated_at, parent_id, is_tombstone FROM slack_messages"
    )
    return {
        row[0]: {
            "data": row[1],
            "updated_at": row[2],
            "parent_id": row[3],
            "is_tombstone": row[4],
        }
        for row in c.fetchall()
    }


def insert_or_update_doc(conn, doc_id, text, updated_at):
    c = conn.cursor()
    c.execute(
        """
        INSERT OR REPLACE INTO docs (id, text, updated_at)
        VALUES (?, ?, ?)
    """,
        (doc_id, text, updated_at),
    )
    conn.commit()


def delete_doc(conn, doc_id):
    c = conn.cursor()
    c.execute("DELETE FROM docs WHERE id = ?", (doc_id,))
    conn.commit()


def get_last_oplog_id(conn):
    c = conn.cursor()
    c.execute("SELECT last_oplog_id FROM doc_build_cursor WHERE id = 1")
    row = c.fetchone()
    return row[0] if row else 0


def update_last_oplog_id(conn, last_oplog_id):
    c = conn.cursor()
    c.execute(
        "UPDATE doc_build_cursor SET last_oplog_id = ? WHERE id = 1", (last_oplog_id,)
    )
    conn.commit()


def load_oplog_entries(conn, since_id):
    c = conn.cursor()
    c.execute(
        "SELECT id, op, message_id, parent_id, timestamp FROM oplog WHERE id > ? ORDER BY id ASC",
        (since_id,),
    )
    return c.fetchall()
