# Slack Document Builder PoC

## Introduction

This Proof-of-Concept (PoC) demonstrates a system designed to replicate Slack channel messages into a local database and incrementally build enriched conversation documents. The system captures message changes via both real-time event processing (using <PERSON><PERSON><PERSON>'s Socket Mode) and periodic synchronization (using Slack’s Conversations API). Changes are tracked through an oplog, which then drives the reconstruction of documents that combine the main (standalone) message with either its thread replies or a contextual window of preceding standalone messages.

*Note: This PoC uses SQLite and performs many operations in memory. For production, consider a more scalable solution (e.g., NoSQL database and/or Queue system) to support efficient Change Data Capture (CDC) and dependency tracking.*

## How Slack Identifies and Manages Messages

- **Message IDs as Timestamps:**  
  Slack assigns a unique timestamp (`ts`) to each message, ensuring uniqueness even when messages are sent rapidly.

- **Thread Management:**  
  - **Main Message:** The message that starts a thread has its `thread_ts` equal to its own `ts`.  
  - **Replies:** Replies have a unique `ts` but share the same `thread_ts` as the main message, allowing all replies to be associated with their parent.
  
- **Tombstone State:**  
  When a main message is deleted but has active replies, <PERSON><PERSON><PERSON> may mark it with a subtype `"tombstone"`. This indicates that while the original message is removed, its thread (replies) remains intact. In our system, tombstoned main messages are updated (not deleted) so that the conversation context is preserved.

## Architecture Overview

### Data Replication

- **Real-time Event API (Socket Mode):**  
  - Listens for Slack events (inserts, updates, tombstones, and deletions) to update the local store.
  - *Note: In this PoC, Socket Mode is used; however, in production, you might prefer to use webhooks for improved scalability and easier integration.*
  - *Additional Note:* Some events are not sent by the Event API – for example, updating the main message when a new reply is added may not trigger a separate event.

- **Periodic Sync (Conversations API):**  
  - Bootstraps the local store and catches up on missed events.
  - **Note:** Be mindful of Slack’s rate limits ([Slack API Rate Limits](https://api.slack.com/apis/rate-limits)).

### Local Data Storage & Oplog

- **Storage:**  
  - Slack messages are stored in a local SQLite database (for the PoC).

- **Oplog:**  
  - Records atomic changes (insert, update, tombstone, delete) for idempotent, incremental updates.
  - The oplog currently stores only the message ID (and optionally the parent_id) to track dependencies.

### Document Building

- **Incremental Rebuild:**  
  - A separate process reads the oplog to rebuild conversation documents incrementally.

- **Document Construction:**  
  - **For Threads:**  
    - A document is built only for the main (standalone) message of a thread (where `thread_ts` equals `ts`).
    - All replies (where `thread_ts` is different from `ts`) are integrated into the document of the main message.
  - **For Context:**  
    - For standalone messages, a document is constructed by combining the main message with a contextual window of preceding standalone messages.
  - **Context Dependency Note:**  
    - Dependency tracking for context messages is not fully implemented yet. For example, if document “F” is built using messages B, C, D, and E as context, deleting C should trigger a rebuild of F’s context (potentially replacing C with another message, such as A).  While document is well rebuilt when thread (replies) is changing.

### Modular Architecture

- **`slack/db.py`:**  
  - Handles all database operations for Slack messages, the oplog, and document storage.

- **`slack/sync.py`:**  
  - Contains synchronization logic for both real-time events (Socket Mode) and periodic API-based sync.

- **`slack/doc_building.py`:**  
  - Manages document reconstruction based on the oplog and current data from the local store.

### Scalability Considerations

- **Current PoC:**  
  - Uses SQLite with many in-memory operations.
  
- **For Production:**  
  - Consider a more scalable database solution to efficiently support CDC and dependency tracking.
  - In production, you might also switch from using Slack's Socket Mode to webhooks for better scalability and integration.

## Important Questions

- **Slack API Rate Limits:**  
  - How will the system handle Slack API rate limits, especially during periodic synchronization?  
  - What strategies (e.g., backoff, batching) should be implemented to avoid hitting these limits?

- **Data Retention:**  
  - How long will the local database retain replicated Slack data?  
  - What policies are needed for purging or archiving old messages?

- **Event Loss and Recovery:**  
  - Given that some events (e.g., main message updates when a new reply is added) might not be sent by the Event API, how can we ensure that no important changes are missed?  
  - What recovery or reconciliation mechanisms can be implemented to maintain data consistency?  
  - **Acceptable Loss?** What level of event loss is acceptable for the system, and how will this affect the overall conversation reconstruction?

- **Context Dependency:**  
  - How can the system track dependencies for context messages effectively?  
  - When a message used as context is modified or deleted, how can we efficiently trigger updates in all affected documents? **Should we do it?**

## Conclusion

This PoC provides a modular, incremental approach to replicating Slack channel data and building enriched conversation documents. It leverages Slack's unique message identification (using timestamps) and thread structure to accurately reconstruct conversations, even handling cases like tombstoned main messages with active threads. While implemented with SQLite for rapid development and testing, the design lays the foundation for a production-ready system by emphasizing the need for robust CDC, dependency tracking, and scalable storage solutions.
