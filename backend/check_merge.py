import argparse
import re
import subprocess
import sys


def get_alembic_heads():
    try:
        result = subprocess.run(
            ["alembic", "heads"], capture_output=True, text=True, check=True
        )
    except subprocess.CalledProcessError as e:
        print("Error while executing 'alembic heads':", e.stderr)
        sys.exit(1)

    pattern = re.compile(r"^([a-f0-9]+)")
    heads = []
    for line in result.stdout.splitlines():
        match = pattern.search(line.strip())
        if match:
            heads.append(match.group(1))
    return heads


def is_valid_merge_message(message):
    pattern = r"^[\w\s.,!?-]+$"
    return re.match(pattern, message) is not None


def interactive_merge(heads):
    print("Multiple heads found:")
    for head in heads:
        print(" -", head)
    answer = input("Do you want to merge these heads? [y/N] ")
    if answer.lower() == "y":
        merge_message = input("Enter merge message: ")
        if not is_valid_merge_message(merge_message):
            print("Merge message contains invalid characters. Aborting.")
            sys.exit(1)
        merge_command = ["alembic", "merge"] + heads + ["-m", merge_message]
        try:
            subprocess.run(merge_command, check=True)
            print("Merge successful!")
        except subprocess.CalledProcessError as e:
            print("Merge failed:", e)
            sys.exit(1)
    else:
        print("Merge aborted by user.")


def automatic_merge(heads, merge_message):
    if not is_valid_merge_message(merge_message):
        print("Merge message contains invalid characters. Aborting.")
        sys.exit(1)
    merge_command = ["alembic", "merge"] + heads + ["-m", merge_message]
    try:
        subprocess.run(merge_command, check=True)
        print("Merge successful!")
    except subprocess.CalledProcessError as e:
        print("Merge failed:", e)
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Check Alembic heads and optionally merge them."
    )
    parser.add_argument(
        "--fail-if-multiple-heads",
        action="store_true",
        help="Exit with error if multiple heads are detected",
    )
    parser.add_argument(
        "--merge", action="store_true", help="Automatically merge the detected heads"
    )
    parser.add_argument(
        "--message",
        type=str,
        default="merge",
        help="Merge message to use when merging heads automatically (default: 'merge')",
    )
    args = parser.parse_args()

    heads = get_alembic_heads()

    if len(heads) > 1:
        if args.fail_if_multiple_heads:
            print("Error: Multiple heads detected:")
            for head in heads:
                print(" -", head)
            sys.exit(1)
        if args.merge:
            automatic_merge(heads, args.message)
        else:
            interactive_merge(heads)
    else:
        print("Only one head found. No merge is necessary.")


if __name__ == "__main__":
    main()
