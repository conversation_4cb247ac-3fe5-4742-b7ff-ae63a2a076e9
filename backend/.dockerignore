# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# IDE and editor files
.idea/
.vscode/
.cursor/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Testing
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Environments
.env
.env.*
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Logs
*.log

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Poetry (legacy)
poetry.lock
poetry.toml

# Development tools
.pre-commit-config.yaml
Brewfile

# Claude
CLAUDE.md
