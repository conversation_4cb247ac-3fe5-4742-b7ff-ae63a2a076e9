version: '3.8'

services:
  # PostgreSQL Database with pgvector extension
  db:
    image: pgvector/pgvector:pg16
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      TZ: UTC
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - pearl-network

  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - ASYNC_DATABASE_URL=${ASYNC_DATABASE_URL}
      - TZ=UTC
      - PYTHONPATH=/app
      - UV_CACHE_DIR=/tmp/uv_cache
      - UV_PROJECT_ENVIRONMENT=/tmp/.venv
      - SECRET_KEY=${SECRET_KEY}
      - SESSION_SECRET_KEY=${SESSION_SECRET_KEY}
      - AUTH_JWT_SECRET_KEY=${AUTH_JWT_SECRET_KEY}
      - AUTH_JWT_ALGORITHM=${AUTH_JWT_ALGORITHM:-HS256}
      - AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=${AUTH_ACCESS_TOKEN_EXPIRE_MINUTES:-30}
      - AUTH_REFRESH_TOKEN_EXPIRE_DAYS=${AUTH_REFRESH_TOKEN_EXPIRE_DAYS:-7}
      - AUTH_MAX_REFRESH_TOKENS=${AUTH_MAX_REFRESH_TOKENS:-5}
      - AUTH_ONE_TIME_CREDENTIALS_EXPIRE_MINUTES=${AUTH_ONE_TIME_CREDENTIALS_EXPIRE_MINUTES:-10}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - LINKUP_API_KEY=${LINKUP_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - SALESFORCE_REDIRECT_URI=${SALESFORCE_REDIRECT_URI}
      - SALESFORCE_AUTH_URL=${SALESFORCE_AUTH_URL}
      - SALESFORCE_TOKEN_URL=${SALESFORCE_TOKEN_URL}
      - MAILER_EMAIL_CLIENT=${MAILER_EMAIL_CLIENT:-local}
      - MAILER_CUSTOMERIO_API_KEY=${MAILER_CUSTOMERIO_API_KEY}
      - LANGFUSE_PUBLIC_KEY=${LANGFUSE_PUBLIC_KEY}
      - LANGFUSE_SECRET_KEY=${LANGFUSE_SECRET_KEY}
      - GOOGLE_OAUTH_CLIENT_ID=${GOOGLE_OAUTH_CLIENT_ID}
      - GOOGLE_OAUTH_CLIENT_SECRET=${GOOGLE_OAUTH_CLIENT_SECRET}
      - GOOGLE_AUTH_URL=${GOOGLE_AUTH_URL}
      - GOOGLE_TOKEN_URL=${GOOGLE_TOKEN_URL}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    volumes:
      - ./backend:/app:cached
      - backend_cache:/app/.cache
    depends_on:
      db:
        condition: service_healthy
    networks:
      - pearl-network
    command: >
      bash -c "
        uv sync --dev &&
        uv run alembic upgrade head &&
        uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
      "

  # Frontend Web Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    volumes:
      - ./frontend:/app:cached
      - frontend_node_modules:/app/node_modules
      - frontend_cache:/app/.cache
    depends_on:
      - backend
    networks:
      - pearl-network

volumes:
  db_data:
    driver: local
  backend_cache:
    driver: local
  frontend_node_modules:
    driver: local
  frontend_cache:
    driver: local

networks:
  pearl-network:
    driver: bridge
