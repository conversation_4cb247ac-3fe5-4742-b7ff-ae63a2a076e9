name: CI

on:
  pull_request:
    branches: ["*"]
  push:
    branches: ["main"]
  merge_group:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

env:
  FORCE_COLOR: 3

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup
        uses: ./tools/github/setup

      # - name: Copy env
      #   shell: bash
      #   run: cp .env.example .env

      - name: Lint
        run: pnpm lint && pnpm lint:ws

  format:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup
        uses: ./tools/github/setup

      - name: Format
        run: pnpm format

  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup
        uses: ./tools/github/setup

      - name: Typecheck
        run: pnpm typecheck
