name: Create new build
on:
  workflow_call:
    inputs:
      type:
        description: Release type
        required: true
        type: string
        default: development-device
      platform:
        description: Target platform
        required: true
        type: string
        default: ios
  workflow_dispatch:
    inputs:
      type:
        description: Profile type
        required: true
        type: choice
        options:
          - development-device
          - development-simulator
          - preview
          - production
        default: development-device
      platform:
        description: Target platform
        required: true
        type: choice
        options:
          - ios
          - android
          - both
        default: ios

jobs:
  ios:
    name: iOS - Create new build
    runs-on: ubuntu-latest
    if: ${{ inputs.platform == 'ios' || inputs.platform == 'both' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: "10.10.0"
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          packager: pnpm

      - name: Install dependencies
        run: pnpm --filter '*' install --frozen-lockfile

      - name: Run EAS build
        id: build
        working-directory: apps/mobile
        run: |
          BUILD_OUTPUT=$(eas build --platform ios --profile ${{ inputs.type }} --non-interactive --json  --no-wait)
          echo "build_id=$(echo $BUILD_OUTPUT | jq -r '.[].id')" >> $GITHUB_OUTPUT

      - name: Get build details from EAS
        id: get-build-version
        working-directory: apps/mobile
        run: |
          sleep 10
          BUILD_DETAILS=$(eas build:view ${{ steps.build.outputs.build_id }} --json)
          VERSION_NUMBER=$(echo "$BUILD_DETAILS" | jq -r '.appVersion')
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "version=$VERSION_NUMBER" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: Send Slack notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: https://expo.dev/accounts/heypearl/projects/pearl/builds/${{ steps.build.outputs.build_id }}
          MSG_MINIMAL: true
          SLACK_USERNAME: Pearl app
          SLACK_TITLE: 🍏 New build available - iOS / ${{ steps.get-build-version.outputs.version }} / ${{ inputs.type }} / ${{ steps.get-build-version.outputs.branch }}
          SLACK_FOOTER: iOS - ${{ inputs.type }} - ${{ steps.get-build-version.outputs.branch }}

  android:
    name: Android - Create new build
    runs-on: ubuntu-latest
    if: ${{ inputs.platform == 'android' || inputs.platform == 'both' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.14.0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4.1.0
        with:
          version: "10.10.0"
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Setup Expo
        uses: expo/expo-github-action@v8
        with:
          eas-version: latest
          token: ${{ secrets.EXPO_TOKEN }}
          packager: pnpm

      - name: Install dependencies
        run: pnpm --filter '*' install --frozen-lockfile

      - name: Run EAS build
        id: build
        working-directory: apps/mobile
        run: |
          BUILD_OUTPUT=$(eas build --platform android --profile ${{ inputs.type }} --non-interactive --json  --no-wait)
          echo "build_id=$(echo $BUILD_OUTPUT | jq -r '.[].id')" >> $GITHUB_OUTPUT

      - name: Get build details from EAS
        id: get-build-version
        working-directory: apps/mobile
        run: |
          sleep 10
          BUILD_DETAILS=$(eas build:view ${{ steps.build.outputs.build_id }} --json)
          VERSION_NUMBER=$(echo "$BUILD_DETAILS" | jq -r '.appVersion')
          BRANCH_NAME=${GITHUB_REF#refs/heads/}
          echo "version=$VERSION_NUMBER" >> $GITHUB_OUTPUT
          echo "branch=$BRANCH_NAME" >> $GITHUB_OUTPUT

      - name: Send Slack notification
        uses: rtCamp/action-slack-notify@v2
        env:
          SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
          SLACK_MESSAGE: https://expo.dev/accounts/heypearl/projects/pearl/builds/${{ steps.build.outputs.build_id }}
          MSG_MINIMAL: true
          SLACK_USERNAME: Pearl app
          SLACK_TITLE: 🤖 New build available - Android / ${{ steps.get-build-version.outputs.version }} / ${{ inputs.type }} / ${{ steps.get-build-version.outputs.branch }}
          SLACK_FOOTER: Android - ${{ inputs.type }} - ${{ steps.get-build-version.outputs.branch }}
