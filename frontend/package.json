{"name": "pearl-frontend", "private": true, "engines": {"node": "22.14.0", "pnpm": "10.11.1"}, "packageManager": "pnpm@10.11.1", "scripts": {"build": "turbo run build", "clean": "turbo run clean --continue --force && git clean -xdf .turbo node_modules", "format": "turbo run format --continue -- --cache --cache-location .cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location .cache/.prettiercache", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location .cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "postinstall": "pnpm lint:ws", "typecheck": "turbo run typecheck", "heroku-postbuild": "./release-tasks.sh"}, "devDependencies": {"@pearl/prettier-config": "workspace:*", "prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.11", "turbo": "^2.5.4", "typescript": "catalog:"}, "pnpm": {"overrides": {"@radix-ui/react-dismissable-layer": "1.1.10", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "chalk": "^4.1.2", "whatwg-url": "14.2.0"}, "peerDependencyRules": {"allowedVersions": {"@types/react": "19", "@types/react-dom": "19", "@typescript-eslint/eslint-plugin": "8", "eslint": "9", "next": "15", "react": "19", "react-dom": "19"}}}, "prettier": "@pearl/prettier-config"}