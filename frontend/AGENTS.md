# Pearl Frontend Agent Guidelines

## Build/Lint/Test Commands

```bash
# Frontend (Next.js/React Native)
pnpm lint                    # Lint all packages
pnpm lint:fix               # Fix linting issues
pnpm format:fix             # Format code with Prettier
pnpm typecheck              # Type check all packages
pnpm test                   # Run tests (mobile only)
pnpm test:changed           # Run tests for changed files

# Single package commands (run from package directory)
cd apps/web && pnpm lint    # Lint web app only
cd apps/mobile && pnpm test # Run mobile tests
```

## Code Style Guidelines

### TypeScript/React

- Functional components only with named exports
- Directory names: lowercase-with-dashes
- File structure: imports → component → helpers → types
- Use React 19 features and Next.js 15 App Router
- Tailwind CSS for styling with cn() utility
- Strict TypeScript with noUncheckedIndexedAccess
- Descriptive boolean names (isLoading, hasError)
- Prefer optional chaining and nullish coalescing
- Use type inference where possible
