{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    /** Base options **/
    "target": "ES2022",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowJs": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,

    /** Keep TSC performance in monorepos **/
    "incremental": true,
    "disableSourceOfProjectReferenceRedirect": true,
    "tsBuildInfoFile": "${configDir}/.cache/tsbuildinfo.json",

    /** Strictness **/
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "checkJs": true,
    "strictNullChecks": true,

    /** Transpilation **/
    "module": "Preserve",
    "moduleResolution": "Bundler",
    "noEmit": true,
    "allowImportingTsExtensions": true,
    "preserveWatchOutput": true
  },
  "exclude": ["node_modules", "build", "dist"]
}
