{"name": "@pearl/prettier-config", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./prettier.config.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --cache --cache-location .cache/.prettiercache", "format:fix": "prettier --write . --ignore-path ../../.gitignore --cache --cache-location .cache/.prettiercache", "typecheck": "tsc --noEmit"}, "dependencies": {"prettier": "catalog:", "prettier-plugin-tailwindcss": "^0.6.11"}, "devDependencies": {"@pearl/tsconfig": "workspace:*", "@types/node": "catalog:", "typescript": "catalog:"}, "prettier": "@pearl/prettier-config"}