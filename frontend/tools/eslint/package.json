{"name": "@pearl/eslint-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./base": "./eslint.base.js", "./react-native": "./eslint.react-native.js", "./react": "./eslint.react.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore --cache --cache-location .cache/.prettiercache", "format:fix": "prettier --write . --ignore-path ../../.gitignore --cache --cache-location .cache/.prettiercache", "typecheck": "tsc --noEmit"}, "prettier": "@pearl/prettier-config", "dependencies": {"@eslint/compat": "^1.2.6", "@eslint/js": "^9.24.0", "@next/eslint-plugin-next": "^15.3.0", "@react-native/eslint-config": "^0.76.6", "@tanstack/eslint-plugin-query": "^5.74.7", "eslint": "catalog:", "eslint-config-expo": "^9.2.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-turbo": "^2.4.0", "eslint-plugin-unused-imports": "^4.1.4", "typescript-eslint": "^8.29.0"}, "devDependencies": {"@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "@types/eslint__js": "8.42.3", "@types/node": "catalog:", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "catalog:", "typescript": "catalog:"}}