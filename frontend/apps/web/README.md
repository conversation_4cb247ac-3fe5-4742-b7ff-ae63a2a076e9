# Pearl Web App

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Installation

### Pre-requisites

- Node.js
  - Recommended installation/version management tool: [nvm](https://github.com/nvm-sh/nvm)
- pnpm

```bash
brew install pnpm
```

### Install dependencies

After cloning the repository, install the dependencies:

```bash
pnpm install
```

## Getting Started

First, run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.
