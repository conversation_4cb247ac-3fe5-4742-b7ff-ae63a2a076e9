import { useQueryClient } from "@tanstack/react-query";
import { useEffect, useRef } from "react";
import { toast } from "sonner";

import { handleRedirectOAuthSuccess } from "~/features/integrations/hooks/useIntegrationActions";

export interface OAuthCallbackConfig {
  integrationId: string;
  callbackPath: string;
  displayName?: string;
}

export const useOAuthAutoCallback = (config: OAuthCallbackConfig): void => {
  const { integrationId, callbackPath, displayName } = config;
  const queryClient = useQueryClient();
  const hasProcessed = useRef(false);

  useEffect(() => {
    const processCallback = async () => {
      if (hasProcessed.current) return;

      const currentUrl = new URL(window.location.href);
      const code = currentUrl.searchParams.get("code");
      const state = currentUrl.searchParams.get("state");

      // Only process if we have OAuth parameters and are on the correct callback path
      if (code && state && currentUrl.pathname === callbackPath) {
        hasProcessed.current = true;

        try {
          // Dynamically import API client to avoid circular dependencies
          const { apiClient } = await import("~/services/api/ApiClient");

          const encodedCode = encodeURIComponent(code);
          const encodedState = encodeURIComponent(state);

          // Call the integration-specific OAuth callback endpoint
          await apiClient.get(
            `/workspace/${integrationId}/callback?code=${encodedCode}&state=${encodedState}`,
          );

          // Sync accounts across all integrations
          await apiClient.get("/workspace/sync_accounts");

          // Clean up OAuth parameters from URL
          const newSearchParams = new URLSearchParams(currentUrl.search);
          newSearchParams.delete("code");
          newSearchParams.delete("state");

          const newUrl = newSearchParams.toString()
            ? `${callbackPath}?${newSearchParams.toString()}`
            : callbackPath;

          window.history.replaceState({}, "", newUrl);

          // Trigger shared success handling (toast notification, query invalidation)
          await handleRedirectOAuthSuccess(integrationId, queryClient);
        } catch (error) {
          console.error(`${integrationId} callback error:`, error);

          const errorDisplayName = displayName || integrationId;
          toast.error(
            `Failed to connect ${errorDisplayName}. Please try again.`,
          );
        }
      }
    };

    void processCallback();
  }, [integrationId, callbackPath, displayName, queryClient]);
};
