import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useGetHubspotAuthUrl } from "@pearl/api-hooks";

import { useOAuthAutoCallback } from "~/features/integrations/hooks/useOAuthAutoCallback";
import type { IntegrationStrategy } from "~/features/integrations/types";

interface Extra {
  authUrl: string;
}

export const hubspotStrategy: IntegrationStrategy<Extra> = {
  id: "hubspot",
  displayName: "HubSpot",
  oauthFlowType: "redirect",
  onboarding: true,

  useExtraData: () => {
    useOAuthAutoCallback({
      integrationId: "hubspot",
      callbackPath: "/onboarding/hubspot/callback",
      displayName: "HubSpot",
    });

    return useGetHubspotAuthUrl();
  },

  connect: ({
    extra,
  }: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) =>
    new Promise<void>((_, reject) => {
      if (!extra?.authUrl) {
        reject(new Error("No auth URL available"));
        return;
      }

      window.location.href = extra.authUrl;
    }),
};
