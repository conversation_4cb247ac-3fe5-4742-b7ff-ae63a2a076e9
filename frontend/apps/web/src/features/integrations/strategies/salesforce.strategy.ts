import type { ApiClient } from "@pearl/api-client";
import type { Integration } from "@pearl/api-hooks";
import { useGetSalesforceAuthUrl } from "@pearl/api-hooks";

import { useOAuthAutoCallback } from "~/features/integrations/hooks/useOAuthAutoCallback";
import type { IntegrationStrategy } from "~/features/integrations/types";

interface Extra {
  authUrl: string;
}

export const salesforceStrategy: IntegrationStrategy<Extra> = {
  id: "salesforce",
  displayName: "Salesforce",
  oauthFlowType: "redirect",
  onboarding: true,

  useExtraData: () => {
    useOAuthAutoCallback({
      integrationId: "salesforce",
      callbackPath: "/onboarding/salesforce/callback",
      displayName: "Salesforce",
    });

    return useGetSalesforceAuthUrl();
  },

  connect: ({
    extra,
  }: {
    apiClient: ApiClient;
    extra: Extra | undefined;
    integration: Integration;
  }) =>
    new Promise<void>((_, reject) => {
      if (!extra?.authUrl) {
        reject(new Error("No auth URL available"));
        return;
      }

      window.location.href = extra.authUrl;
    }),
};
