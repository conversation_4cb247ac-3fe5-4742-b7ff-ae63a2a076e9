import Image from "next/image";

import { SyncingSpinnerIcon } from "~/components/icons";

interface LoadingStepProps {
  integrationId: string;
  title: string;
  stage?: "connecting" | "importing";
}

export function LoadingStep({
  integrationId,
  title,
  stage = "connecting",
}: LoadingStepProps) {
  return (
    <div className="text-center">
      <div className="mb-8 flex items-center justify-center">
        <div className="flex h-[80px] w-[80px] items-center justify-center rounded-lg border bg-card shadow-sm">
          <Image
            src="/pearl-logo.svg"
            alt="Pearl Logo"
            width={48}
            height={48}
            className="max-h-full max-w-full object-contain"
          />
        </div>

        <div className="mx-2 text-2xl font-bold">
          <SyncingSpinnerIcon
            width={24}
            height={24}
            className="text-muted-foreground"
          />
        </div>

        <div className="flex h-[80px] w-[80px] items-center justify-center rounded-lg border bg-card shadow-sm">
          <Image
            src={
              integrationId === "salesforce"
                ? "/integrations/salesforce.png"
                : "/integrations/hubspot.png"
            }
            alt={`${integrationId === "salesforce" ? "Salesforce" : "HubSpot"} Logo`}
            width={48}
            height={48}
            className="max-h-full max-w-full object-contain"
          />
        </div>
      </div>

      <p className="mb-4 text-muted-foreground">{title}</p>

      <p className="text-sm text-muted-foreground">
        {stage === "connecting"
          ? "Establishing connection..."
          : "Synchronizing data..."}
      </p>
    </div>
  );
}
