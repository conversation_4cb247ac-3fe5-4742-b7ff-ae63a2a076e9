"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";

import { Button } from "~/components/ui/Button";
import { Card, CardContent } from "~/components/ui/Card";
import { getAllCRMConfigs } from "~/lib/crm/config";
import { cn } from "~/lib/tailwind/cn";

export function CRMSelectionStep() {
  const router = useRouter();
  const [selectedCRM, setSelectedCRM] = useState<string | null>(null);
  const crmOptions = getAllCRMConfigs();

  const handleCRMSelect = (crmId: string) => {
    setSelectedCRM(crmId);
  };

  const handleContinue = () => {
    if (selectedCRM) {
      router.push(`/onboarding/${selectedCRM}`);
    }
  };

  return (
    <div className="mx-auto flex h-[600px] max-w-2xl flex-col items-center justify-between">
      <div className="w-full text-center">
        <div className="mb-4 flex justify-center">
          <div className="flex h-[90px] w-[90px] items-center justify-center">
            <Image
              src="/pearl-logo.svg"
              alt="Pearl Logo"
              width={56}
              height={56}
              className="max-h-full max-w-full object-contain"
            />
          </div>
        </div>
        <h1 className="mb-4 text-2xl font-bold">Choose your CRM</h1>
        <p className="mb-8 text-muted-foreground">
          Select the CRM system you'd like to connect with Pearl.
          <br />
          You can only connect one CRM at a time.
        </p>
      </div>

      <div className="mb-auto w-full max-w-2xl flex-grow overflow-auto">
        <div className="grid gap-4 p-1 md:grid-cols-2">
          {crmOptions.map((crm) => (
            <Card
              key={crm.id}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md hover:ring-2 hover:ring-primary/40",
                selectedCRM === crm.id ? "ring-2" : "hover:bg-accent/50",
              )}
              onClick={() => handleCRMSelect(crm.id)}
            >
              <CardContent className="p-6">
                <div className="flex h-40 flex-col items-center text-center">
                  <div className="flex flex-1 flex-col items-center justify-center">
                    <div className="mb-4 flex h-16 w-16 items-center justify-center overflow-hidden rounded-lg">
                      <Image
                        src={crm.logoPath}
                        alt={`${crm.displayName} logo`}
                        width={64}
                        height={64}
                        className="h-full w-full object-contain"
                      />
                    </div>
                    <h3 className="mb-2 text-lg font-semibold">
                      {crm.displayName}
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      {crm.description}
                    </p>
                  </div>
                  <div className="flex h-8 items-center justify-center">
                    {selectedCRM === crm.id && (
                      <div className="mt-3 flex items-center text-blue-600 dark:text-blue-400">
                        <div className="mr-2 h-2 w-2 rounded-full bg-blue-600 dark:bg-blue-400"></div>
                        <span className="text-sm font-medium">Selected</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      <div className="h-[60px] w-full max-w-md">
        <Button
          onClick={handleContinue}
          className="w-full"
          size="lg"
          disabled={!selectedCRM}
        >
          Continue with{" "}
          {selectedCRM
            ? crmOptions.find((crm) => crm.id === selectedCRM)?.displayName
            : "CRM"}
        </Button>
      </div>
    </div>
  );
}
