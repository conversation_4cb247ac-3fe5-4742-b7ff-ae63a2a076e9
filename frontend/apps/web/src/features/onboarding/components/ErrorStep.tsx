interface ErrorStepProps {
  errorMessage: string | null;
}

export const ErrorStep = ({ errorMessage }: ErrorStepProps) => {
  return (
    <div className="text-center">
      <h2 className="mb-4 text-xl font-bold text-red-500">Connection Error</h2>
      <p className="mb-6 text-muted-foreground">
        {errorMessage || "An error occurred while connecting to your CRM"}
      </p>
      <p className="text-muted-foreground">Redirecting back to try again...</p>
    </div>
  );
};
