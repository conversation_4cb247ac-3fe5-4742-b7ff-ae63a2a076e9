import { useRouter } from "next/navigation";

import { Button } from "~/components/ui/Button";
import { useCRM } from "~/features/crm/context/CRMContext";

export function SuccessStep() {
  const router = useRouter();
  const { refreshUserCRM } = useCRM();

  function handleOpenPearl() {
    void refreshUserCRM();
    router.push("/home");
  }

  return (
    <>
      <div className="mb-8 w-full max-w-md text-center">
        <h1 className="mb-4 text-2xl font-bold">Pearl is ready</h1>
        <p className="mb-8 text-muted-foreground">
          You're all set. <PERSON> now has the context it needs to help you
          navigate your pipeline, surface insights, and stay one step ahead.
          Let's get to work.
        </p>
      </div>

      <div className="w-full max-w-md">
        <Button onClick={handleOpenPearl} className="w-full" size="lg">
          Open Pearl
        </Button>
      </div>
    </>
  );
}
