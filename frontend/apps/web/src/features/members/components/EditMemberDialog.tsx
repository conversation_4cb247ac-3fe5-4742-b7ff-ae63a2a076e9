"use client";

import { useState } from "react";

import type { MemberProfile } from "@pearl/api-hooks";

import { Button } from "~/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/Dialog";
import { Input } from "~/components/ui/Input";
import { Separator } from "~/components/ui/Separator";
import { getMemberFullName } from "~/utils/member";

interface EditMemberDialogProps {
  member: MemberProfile | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export default function EditMemberDialog({
  member,
  open,
  onOpenChange,
}: EditMemberDialogProps) {
  const [formData, setFormData] = useState({
    email: member?.email || "",
    role: member?.isAdmin ? "Admin" : "Member",
  });

  const handleSave = () => {
    // TODO: Implement save logic using react-hook-form
    console.log("Saving member isn't implemented yet");
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const handleRemove = () => {
    // TODO: Implement remove logic
    console.log("Removing member isn't implemented yet");
    onOpenChange(false);
  };

  const memberName = member ? getMemberFullName(member) : "";

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Member</DialogTitle>
          <DialogDescription>
            Update {memberName}'s email and role. You can also remove them from
            the team.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="email" className="text-sm font-medium">
              Email
            </label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
            />
          </div>
          <div className="grid gap-2">
            <label htmlFor="role" className="text-sm font-medium">
              Role
            </label>
            <select
              id="role"
              value={formData.role}
              onChange={(e) =>
                setFormData({ ...formData, role: e.target.value })
              }
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            >
              <option value="Member">Member</option>
              <option value="Admin">Admin</option>
            </select>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <div className="flex w-full gap-2">
            <Button variant="outline" onClick={handleCancel} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleSave} className="flex-1">
              Save changes
            </Button>
          </div>

          <Separator className="my-2" />

          <Button
            variant="destructive"
            onClick={handleRemove}
            className="w-full"
          >
            Remove from team
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
