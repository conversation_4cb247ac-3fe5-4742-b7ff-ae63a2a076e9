"use client";

import { Search } from "lucide-react";
import { useMemo, useState } from "react";

import type { MemberProfile } from "@pearl/api-hooks";
import { useGetMemberProfiles } from "@pearl/api-hooks";

import { Button } from "~/components/ui/Button";
import { Input } from "~/components/ui/Input";
import { cn } from "~/lib/tailwind/cn";
import { getMemberFullName } from "~/utils/member";

import EditMemberDialog from "./EditMemberDialog";

export default function MemberList() {
  const { data: memberProfiles } = useGetMemberProfiles();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedMember, setSelectedMember] = useState<MemberProfile | null>(
    null,
  );
  const [dialogOpen, setDialogOpen] = useState(false);

  const filteredMembers = useMemo(() => {
    if (!memberProfiles?.members || !searchQuery.trim()) {
      return memberProfiles?.members || [];
    }
    const query = searchQuery.toLowerCase();
    return memberProfiles.members.filter((member) => {
      const fullName = getMemberFullName(member).toLowerCase();
      const email = member.email.toLowerCase();

      return fullName.includes(query) || email.includes(query);
    });
  }, [memberProfiles?.members, searchQuery]);

  const handleEditClick = (member: MemberProfile) => {
    setSelectedMember(member);
    setDialogOpen(true);
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
        <div className="space-y-1">
          <h3 className="text-lg font-semibold">Team members</h3>
          <p className="text-sm text-muted-foreground">
            Manage team members, roles, and permissions.
          </p>
        </div>
        <div className="flex flex-col items-stretch gap-3 sm:flex-row sm:items-center">
          <div className="relative">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search members..."
              className="w-full pl-10 sm:w-64"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button type="button" variant="secondary">
            Add member
          </Button>
        </div>
      </div>
      <div className="-mx-4 sm:-mx-0">
        <table className="min-w-full divide-y divide-border">
          <Header />
          <tbody className="divide-y divide-border bg-transparent">
            {filteredMembers.map((member) => (
              <MemberRow
                key={member.id}
                member={member}
                onEdit={() => handleEditClick(member)}
              />
            ))}
          </tbody>
        </table>
      </div>

      <EditMemberDialog
        member={selectedMember}
        open={dialogOpen}
        onOpenChange={setDialogOpen}
      />
    </div>
  );
}

function Header() {
  return (
    <thead>
      <tr>
        <HeaderCell className="py-3.5 pr-3 pl-4 sm:pl-0">Name</HeaderCell>
        <HeaderCell className="hidden px-3 py-3.5 sm:table-cell">
          Email
        </HeaderCell>
        <HeaderCell className="px-3 py-3.5">Role</HeaderCell>
        <HeaderCell className="relative py-3.5 pr-4 pl-3 sm:pr-0">
          <span className="sr-only">Edit</span>
        </HeaderCell>
      </tr>
    </thead>
  );
}

function HeaderCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return (
    <th
      scope="col"
      className={cn(
        "px-3 py-3.5 text-left text-sm font-semibold text-foreground",
        className,
      )}
    >
      {children}
    </th>
  );
}

function MemberRow({
  member,
  onEdit,
}: Readonly<{
  member: MemberProfile;
  onEdit: () => void;
}>) {
  const fullName = getMemberFullName(member);

  return (
    <tr>
      <MemberCell className="w-full max-w-0 py-4 pr-3 pl-4 text-sm font-medium text-foreground sm:w-auto sm:max-w-none sm:pl-0">
        {fullName}
        <dl className="font-normal sm:hidden">
          <dt className="sr-only sm:hidden">Email</dt>
          <dd className="mt-1 truncate text-muted-foreground sm:hidden">
            {member.email}
          </dd>
        </dl>
      </MemberCell>
      <MemberCell className="hidden px-3 py-4 text-sm text-muted-foreground sm:table-cell">
        {member.email}
      </MemberCell>
      <MemberCell className="px-3 py-4 text-sm text-muted-foreground">
        {member.isAdmin ? "Admin" : "Member"}
      </MemberCell>
      <MemberCell className="py-4 pr-4 pl-3 text-right text-sm font-medium sm:pr-1">
        <button
          onClick={onEdit}
          className="cursor-pointer text-primary hover:underline focus:underline focus:outline-none"
        >
          Edit<span className="sr-only">, {fullName}</span>
        </button>
      </MemberCell>
    </tr>
  );
}

function MemberCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return <td className={cn("whitespace-nowrap", className)}>{children}</td>;
}
