import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export interface AccountDetails {
  id: string;
  name: string;
  employees: number;
  totalRevenue: number;
  existingRevenue: number;
  crmUrl: string;
}

export const useGetAccountDetails = (accountId?: string) => {
  const { apiClient } = useApiClient();

  return useQuery<AccountDetails>({
    queryKey: ["accountDetails", accountId],
    queryFn: () =>
      apiClient.get<AccountDetails>(`/workspace/account/${accountId}/details`),
    enabled: !!accountId,
  });
};
