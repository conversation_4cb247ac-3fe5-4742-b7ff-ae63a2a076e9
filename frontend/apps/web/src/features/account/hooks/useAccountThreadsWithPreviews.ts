import { useQueries } from "@tanstack/react-query";
import removeMarkdown from "remove-markdown";

import { useApiClient } from "@pearl/api-client";
import { useGetAccountThreads } from "@pearl/api-hooks";

/**
 * Hook to fetch account threads with message previews
 *
 * TODO: This is currently doing N+1 queries (threads + message preview for each thread).
 * Should create a dedicated backend endpoint like:
 * GET /api/agent/accounts/{accountId}/threads?include_preview=true
 * This would return threads with their latest message preview in a single request.
 */
export function useAccountThreadsWithPreviews(accountId: string) {
  const { apiClient } = useApiClient();

  const {
    data: accountThreadsResponse,
    isLoading: isLoadingThreads,
    isError,
  } = useGetAccountThreads(accountId);

  // Sort threads by most recent first
  const sortedThreads =
    accountThreadsResponse?.threads.sort(
      (a, b) =>
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    ) ?? [];

  // Fetch message previews for all threads (N+1 query problem - TODO: fix with dedicated endpoint)
  const messageQueries = useQueries({
    queries: sortedThreads.map((thread) => ({
      queryKey: ["threadMessages", thread.id],
      queryFn: () =>
        apiClient.get<{ messages: { content: string }[] }>(
          `/agent/threads/${thread.id}/messages`,
        ),
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
    })),
  });

  const isLoadingPreviews = messageQueries.some((query) => query.isLoading);
  const isLoading = isLoadingThreads || isLoadingPreviews;

  // Transform threads with their previews
  const threadsWithPreviews = sortedThreads.map((thread, index) => {
    let messagePreview = "No messages yet";

    const messageQuery = messageQueries[index];
    if (messageQuery?.isLoading) {
      messagePreview = "Loading preview...";
    } else if (
      messageQuery?.data &&
      "messages" in messageQuery.data &&
      messageQuery.data.messages.length > 0
    ) {
      const lastMessage =
        messageQuery.data.messages[messageQuery.data.messages.length - 1];
      if (lastMessage?.content) {
        const plainText = removeMarkdown(lastMessage.content);
        messagePreview =
          plainText.length > 120 ? plainText.slice(0, 120) + "..." : plainText;
      }
    }

    return {
      ...thread,
      messagePreview,
    };
  });

  return {
    threads: threadsWithPreviews,
    isLoading,
    isError,
  };
}
