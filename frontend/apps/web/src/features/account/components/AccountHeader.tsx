import { BadgeDollarSign, Users } from "lucide-react";

import PageHeader from "~/components/PageHeader";
import { Button } from "~/components/ui/Button";
import { formatNumber } from "~/utils/format";

interface AccountHeaderProps {
  name?: string;
  logoUrl?: string;
  employees?: number;
  annualRevenue?: number;
  existingRevenue?: number;
  crmUrl?: string;
}

export default function AccountHeader({
  name,
  logoUrl = "/placeholder.jpg",
  employees,
  annualRevenue,
  existingRevenue,
  crmUrl,
}: AccountHeaderProps) {
  const descriptionItems = [
    employees
      ? {
          icon: <Users className="size-4" />,
          label: <span>{formatNumber(employees, true)} employees</span>,
        }
      : null,
    annualRevenue
      ? {
          icon: <BadgeDollarSign className="size-4" />,
          label: `$${formatNumber(annualRevenue)}`,
        }
      : null,
    existingRevenue !== undefined
      ? {
          icon: <span className="mx-2 text-muted-foreground/50">|</span>,
          label: (
            <span>Existing revenue: ${formatNumber(existingRevenue)}</span>
          ),
        }
      : null,
  ].filter(Boolean) as Array<{ icon: React.ReactNode; label: React.ReactNode }>;

  return (
    <PageHeader
      title={name || ""}
      logoUrl={logoUrl}
      descriptionItems={descriptionItems}
      action={
        crmUrl ? (
          <Button variant="secondary" asChild>
            <a href={crmUrl} target="_blank" rel="noopener noreferrer">
              View in CRM
            </a>
          </Button>
        ) : null
      }
      variant="enriched"
    />
  );
}
