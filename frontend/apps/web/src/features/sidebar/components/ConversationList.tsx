"use client";
import { MoreHorizontal } from "lucide-react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useState } from "react";

import {
  useDeleteThread,
  useGetThreads,
  useGetUserAccounts,
  useUpdateThreadCRMAccount,
  useUpdateThreadName,
} from "@pearl/api-hooks";

import Loader from "~/components/Loader";
import { Button } from "~/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/Dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "~/components/ui/DropdownMenu";
import { Input } from "~/components/ui/Input";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuAction,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "~/components/ui/Sidebar";

interface Thread {
  id: string;
  name: string;
  createdAt: string;
}

export default function ConversationList() {
  const { isMobile } = useSidebar();
  const router = useRouter();
  const { threadId: activeThreadId } = useParams();

  const { data: threadsResponse, isError, isLoading } = useGetThreads();
  const { data: userAccounts, isLoading: isLoadingAccounts } =
    useGetUserAccounts();
  const { mutate: deleteThread } = useDeleteThread();
  const { mutate: updateThreadName } = useUpdateThreadName();
  const { mutate: updateThreadCRMAccount } = useUpdateThreadCRMAccount();

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    thread: Thread | null;
  }>({
    open: false,
    thread: null,
  });
  const [rename, setRename] = useState<{
    threadId: string | null;
    value: string;
  }>({
    threadId: null,
    value: "",
  });
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const handleDeleteClick = (thread: Thread) => {
    setDeleteDialog({ open: true, thread });
    setOpenDropdown(null);
  };

  const handleDeleteConfirm = () => {
    if (!deleteDialog.thread) return;

    const wasActiveThread = activeThreadId === deleteDialog.thread.id;
    deleteThread(deleteDialog.thread.id);
    setDeleteDialog({ open: false, thread: null });
    setOpenDropdown(null);

    if (wasActiveThread) {
      router.push("/home");
    }
  };

  const handleRenameClick = (thread: Thread) => {
    setRename({
      threadId: thread.id,
      value: thread.name || thread.createdAt.slice(2, 19),
    });
    setOpenDropdown(null);
  };

  const handleRenameSubmit = () => {
    if (rename.threadId && rename.value.trim()) {
      updateThreadName({
        threadId: rename.threadId,
        name: rename.value.trim(),
      });
    }
    setRename({ threadId: null, value: "" });
  };

  const handleRenameKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleRenameSubmit();
    } else if (event.key === "Escape") {
      setRename({ threadId: null, value: "" });
    }
  };

  const handleThreadClick = (thread: Thread) => {
    if (rename.threadId === thread.id) {
      return;
    }
    router.push(`/thread/${thread.id}`);
  };

  const handleRenameInputClick = (event: React.MouseEvent) => {
    event.stopPropagation();
  };

  const handleAddToFolder = (thread: Thread, accountId: string) => {
    updateThreadCRMAccount({
      threadId: thread.id,
      crm_account_id: accountId,
    });
    setOpenDropdown(null);
  };

  const getThreadDisplayName = (thread: Thread) =>
    thread.name || thread.createdAt.slice(2, 19);

  const isThreadActive = (threadId: string) => activeThreadId === threadId;

  const sortedThreads =
    threadsResponse?.threads.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    ) || [];

  if (isLoading) return <Loader />;
  if (!threadsResponse?.threads.length) return null;

  return (
    <>
      <SidebarGroup>
        <SidebarGroupLabel>Conversations</SidebarGroupLabel>
        <SidebarMenu>
          {sortedThreads.map((thread) => (
            <SidebarMenuItem key={thread.id}>
              <SidebarMenuButton
                onClick={() => handleThreadClick(thread)}
                isActive={isThreadActive(thread.id)}
              >
                {rename.threadId === thread.id ? (
                  <Input
                    autoFocus
                    value={rename.value}
                    onChange={(e) =>
                      setRename((prev) => ({ ...prev, value: e.target.value }))
                    }
                    onKeyDown={handleRenameKeyDown}
                    onBlur={handleRenameSubmit}
                    onClick={handleRenameInputClick}
                    className="h-7 px-2 py-1 text-sm"
                  />
                ) : (
                  <span>{getThreadDisplayName(thread)}</span>
                )}
              </SidebarMenuButton>

              <DropdownMenu
                open={openDropdown === thread.id}
                onOpenChange={(open) =>
                  setOpenDropdown(open ? thread.id : null)
                }
              >
                <DropdownMenuTrigger asChild>
                  <SidebarMenuAction showOnHover>
                    <MoreHorizontal />
                    <span className="sr-only">More</span>
                  </SidebarMenuAction>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className="w-48"
                  side={isMobile ? "bottom" : "right"}
                  align={isMobile ? "end" : "start"}
                >
                  <DropdownMenuSub>
                    <DropdownMenuSubTrigger>
                      <span>Add to folder</span>
                    </DropdownMenuSubTrigger>
                    <DropdownMenuSubContent>
                      {isLoadingAccounts ? (
                        <DropdownMenuItem disabled>
                          Loading accounts...
                        </DropdownMenuItem>
                      ) : userAccounts?.length ? (
                        userAccounts.map((account) => (
                          <DropdownMenuItem
                            key={account.crmId}
                            onClick={() =>
                              handleAddToFolder(thread, account.crmId)
                            }
                          >
                            {account.crmName}
                          </DropdownMenuItem>
                        ))
                      ) : (
                        <DropdownMenuItem disabled>
                          No accounts available
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuSubContent>
                  </DropdownMenuSub>
                  <DropdownMenuItem onClick={() => handleRenameClick(thread)}>
                    Rename
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => handleDeleteClick(thread)}>
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
        {isError && <div>Error loading threads</div>}
      </SidebarGroup>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onOpenChange={(open) =>
          setDeleteDialog({ open, thread: open ? deleteDialog.thread : null })
        }
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Conversation</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;
              {deleteDialog.thread && getThreadDisplayName(deleteDialog.thread)}
              &quot;? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, thread: null })}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
