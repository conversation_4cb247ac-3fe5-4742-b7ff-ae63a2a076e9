"use client";
import { Calendar, Home } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
} from "~/components/ui/Sidebar";

export default function MenuItems() {
  const pathname = usePathname();
  const router = useRouter();

  const goToHomePage = () => {
    router.push("/home");
  };

  const goToCalendarPage = () => {
    router.push("/calendar");
  };

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarMenu>
        <SidebarMenuButton
          onClick={goToHomePage}
          isActive={pathname.startsWith("/home")}
          className="cursor-pointer"
        >
          <Home />
          Home
        </SidebarMenuButton>
        <SidebarMenuButton
          onClick={goToCalendarPage}
          isActive={pathname.startsWith("/calendar")}
          className="cursor-pointer"
        >
          <Calendar />
          Calendar
        </SidebarMenuButton>
      </SidebarMenu>
    </SidebarGroup>
  );
}
