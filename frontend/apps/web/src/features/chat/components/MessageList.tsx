import type { UIMessage } from "ai";

import Card<PERSON>ool<PERSON>all from "~/features/chat/components/CardToolCall";
import Message from "~/features/chat/components/Message";

interface MessageListProps {
  messages: UIMessage[];
  addToolResult: (obj: {
    toolCallId: string;
    result: { action: "continue" | "abort" };
  }) => void;
}

// TODO: have this come from the backend
const HUMAN_REVIEW_REQUIRED_TOOLS = [
  "update_opportunity",
  "update_account",
  "create_contact",
  "update_contact",
  "create_task",
  "update_task",
  "create_event",
  "update_event",
];

export default function MessageList({
  messages,
  addToolResult,
}: MessageListProps) {
  return (
    <div className="w-full space-y-4">
      {messages.map((message) => (
        <div key={message.id}>
          {message.parts.map((part, index) => {
            if (part.type === "text") {
              return (
                <Message key={index} content={part.text} role={message.role} />
              );
            } else if (
              part.type === "tool-invocation" &&
              HUMAN_REVIEW_REQUIRED_TOOLS.includes(part.toolInvocation.toolName)
            ) {
              return (
                <CardToolCall
                  key={index}
                  toolInvocation={part.toolInvocation}
                  addToolResult={addToolResult}
                />
              );
            }
            return null; // TODO: Handle other part types if needed
          })}
        </div>
      ))}
    </div>
  );
}
