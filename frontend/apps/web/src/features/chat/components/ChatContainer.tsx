"use client";

import { useEffect, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import {
  useChatStream,
  useGetAccountThreads,
  useGetThreadMessages,
} from "@pearl/api-hooks";

import {
  AIConversation,
  AIConversationContent,
  AIConversationScrollButton,
} from "~/components/ui/kibo/Conversation";
import MessageList from "~/features/chat/components/MessageList";
import MultimodalInput from "~/features/chat/components/MultimodalInput";

interface ChatContainerProps {
  threadId?: string;
  accountId?: string;
  onNewThread?: (threadId: string) => void;
  initialPrompt?: string | null;
}

export default function ChatContainer({
  threadId,
  accountId,
  onNewThread,
  initialPrompt,
}: ChatContainerProps) {
  // Use local state for thread ID to handle new threads
  const [localThreadId, setLocalThreadId] = useState<string>(() => uuidv4());

  useEffect(() => {
    if (threadId) {
      setLocalThreadId(threadId);
    } else {
      // If no thread is selected, generate a new thread ID
      setLocalThreadId(uuidv4());
    }
  }, [threadId, setLocalThreadId]);

  const { refetch: refetchThreads } = useGetAccountThreads(accountId);
  const { data: threadMessages } = useGetThreadMessages(threadId);

  const {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    stop,
    status: chatStatus,
    addToolResult,
  } = useChatStream({
    threadId: threadId || localThreadId,
    crmAccountId: accountId,
    onError: (error) => {
      console.error("Chat error:", error);
    },
    onFinish: () => {
      if (localThreadId !== threadId) {
        // Add new thread to sidebar and select it
        void refetchThreads();
        onNewThread?.(localThreadId);
      }
    },
  });

  // Load existing messages when threadMessages changes
  useEffect(() => {
    setMessages(threadMessages?.messages || []);
  }, [setMessages, threadMessages]);

  // Auto-set initial prompt if provided
  useEffect(() => {
    if (initialPrompt && !threadMessages?.messages.length) {
      setInput(initialPrompt);
    }
  }, [initialPrompt, threadMessages?.messages.length, setInput]);

  return (
    <div className="flex min-h-0 w-full grow flex-col">
      <AIConversation key={threadId} className="h-full w-full flex-1">
        <AIConversationContent className="flex justify-center">
          <div className="w-full max-w-3xl">
            <MessageList messages={messages} addToolResult={addToolResult} />
          </div>
        </AIConversationContent>
        <AIConversationScrollButton className="z-10" />
      </AIConversation>
      <div className="flex-shrink-0">
        <MultimodalInput
          input={input}
          setInput={setInput}
          handleSubmit={handleSubmit}
          stop={stop}
          setMessages={setMessages}
          isLoading={chatStatus === "submitted"}
        />
      </div>
    </div>
  );
}
