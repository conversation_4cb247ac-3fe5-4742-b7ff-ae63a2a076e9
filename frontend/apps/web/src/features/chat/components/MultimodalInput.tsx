"use client";

import type { ChatRequestOptions, Message } from "ai";
import type React from "react";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useEffect,
  useRef,
} from "react";

import { AIInput, AIInputTextarea } from "~/components/ui/kibo/Input";

export default function MultimodalInput({
  input,
  setInput,
  isLoading,
  handleSubmit,
}: {
  input: string;
  setInput: (value: string) => void;
  isLoading: boolean;
  stop: () => void;
  setMessages: Dispatch<SetStateAction<Array<Message>>>;
  handleSubmit: (
    event?: {
      preventDefault?: () => void;
    },
    chatRequestOptions?: ChatRequestOptions,
  ) => void;
  className?: string;
}) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const submitForm = useCallback(
    (e?: React.FormEvent) => {
      if (e) {
        e.preventDefault();
      }
      if (!input.trim() || isLoading) return;

      handleSubmit(undefined, {});

      // Focus the textarea after submission
      setTimeout(() => {
        textareaRef.current?.focus();
      }, 0);
    },
    [handleSubmit, input, isLoading],
  );

  // Focus on mount and when loading state changes
  useEffect(() => {
    if (!isLoading && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isLoading]);

  return (
    <div className="mb-4 flex w-full justify-center pb-4">
      <div className="w-full max-w-[800px]">
        <AIInput onSubmit={submitForm}>
          <AIInputTextarea
            ref={textareaRef}
            value={input}
            onChange={(e) => {
              setInput(e.target.value);
            }}
            placeholder="Send a message..."
            disabled={isLoading}
          />
        </AIInput>
      </div>
    </div>
  );
}
