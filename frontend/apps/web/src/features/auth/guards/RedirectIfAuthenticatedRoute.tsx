"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { useAuth } from "@pearl/api-client";

import Loader from "~/components/Loader";

export default function RedirectIfAuthenticatedRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { authenticated, loading } = useAuth();

  useEffect(() => {
    if (!loading && authenticated) {
      router.push("/home");
    }
  }, [loading, authenticated, router]);

  if (loading) {
    return <Loader />;
  }
  if (authenticated) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
