"use client";

import { useRouter } from "next/navigation";
import type { ReactNode } from "react";
import { useEffect } from "react";

import Loader from "~/components/Loader";
import { useCRM } from "~/features/crm/context/CRMContext";

interface CRMNotConfiguredProps {
  children: ReactNode;
  redirectTo?: string;
}

export default function RedirectIfCRMSetRoute({
  children,
  redirectTo = "/home",
}: CRMNotConfiguredProps) {
  const { userCRM, isLoading } = useCRM();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && userCRM) {
      router.push(redirectTo);
    }
  }, [userCRM, isLoading, router, redirectTo]);

  if (isLoading) {
    return <Loader />;
  }

  if (userCRM) {
    // wait for redirect to redirectTo
    return <Loader />;
  }

  return <>{children}</>;
}
