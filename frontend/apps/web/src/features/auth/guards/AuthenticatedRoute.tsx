"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import { useAuth } from "@pearl/api-client";

import Loader from "~/components/Loader";

export default function AuthenticatedRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { loading, authenticated } = useAuth();

  useEffect(() => {
    if (!loading && !authenticated) {
      router.push("/login/email");
    }
  }, [loading, authenticated, router]);

  if (loading) {
    return <Loader />;
  }
  if (!authenticated) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
