"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "~/components/Loader";
import { useIsAdmin } from "~/features/auth/hooks/useIsAdmin";

export default function AdminOnlyRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { isAdmin, isLoadingIsAdmin } = useIsAdmin();

  useEffect(() => {
    if (!isLoadingIsAdmin && !isAdmin) {
      router.push("/home");
    }
  }, [isLoadingIsAdmin, isAdmin, router]);

  if (isLoadingIsAdmin) {
    return <Loader />;
  }
  if (!isAdmin) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
