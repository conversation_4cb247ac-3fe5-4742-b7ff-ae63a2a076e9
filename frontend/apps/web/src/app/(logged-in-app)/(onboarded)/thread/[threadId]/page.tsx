"use client";

import { useParams } from "next/navigation";

import { SidebarTrigger } from "~/components/ui/Sidebar";
import ChatContainer from "~/features/chat/components/ChatContainer";

export default function Thread() {
  const { threadId } = useParams();

  return (
    <div className="flex h-screen flex-col overflow-hidden">
      <SidebarTrigger className="md:hidden" />
      <ChatContainer threadId={threadId as string} />
    </div>
  );
}
