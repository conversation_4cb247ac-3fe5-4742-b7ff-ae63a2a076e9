"use client";

import { usePathname } from "next/navigation";
import <PERSON>rip<PERSON> from "next/script";
import React, { useEffect } from "react";

import { useSyncUserAccounts } from "@pearl/api-hooks";

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "~/components/ui/Breadcrumb";
import { Separator } from "~/components/ui/Separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/Sidebar";
import CRMRequiredRoute from "~/features/auth/guards/CRMRequiredRoute";
import Sidebar from "~/features/sidebar/components/AppSidebar";
import { useBreadcrumbs } from "~/hooks/useBreadcrumbs";
import { cn } from "~/lib/tailwind/cn";

function SyncUserAccounts({ children }: { children: React.ReactNode }) {
  const { mutate } = useSyncUserAccounts();
  // Trigger synchronization only once when the component mounts
  useEffect(() => {
    mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{children}</>;
}

function isThreadPage(pathname: string): boolean {
  return (
    pathname.startsWith("/thread/") ||
    (pathname.startsWith("/account/") && pathname.split("/").length === 4)
  );
}

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();

  return (
    <CRMRequiredRoute>
      <Script src="https://accounts.google.com/gsi/client" async />
      <SyncUserAccounts>
        <SidebarProvider>
          <Sidebar />
          <SidebarInset
            className={cn(
              pathname === "/home" ? "custom-bg" : "",
              isThreadPage(pathname) ? "overflow-hidden" : "",
            )}
          >
            <Header />
            {isThreadPage(pathname) ? (
              children
            ) : (
              <div className="mx-auto flex w-full max-w-6xl flex-1 flex-col">
                <div className="flex flex-1 flex-col gap-4 p-4 pt-4">
                  {children}
                </div>
              </div>
            )}
          </SidebarInset>
        </SidebarProvider>
      </SyncUserAccounts>
    </CRMRequiredRoute>
  );
}

function Header() {
  const pathname = usePathname();
  const breadcrumbs = useBreadcrumbs();
  const isHomePage = pathname === "/home";

  return (
    <header
      className={cn(
        "sticky top-0 z-20 flex h-16 shrink-0 items-center gap-2",
        !isHomePage &&
          "rounded-t-xl border-b bg-background/40 backdrop-blur-[20px]",
      )}
    >
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        {!isHomePage && breadcrumbs.length > 0 && (
          <>
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                {breadcrumbs.map((breadcrumb, idx) => (
                  <React.Fragment key={breadcrumb.label}>
                    <BreadcrumbItem>
                      {breadcrumb.type === "page" ? (
                        <BreadcrumbPage>{breadcrumb.label}</BreadcrumbPage>
                      ) : (
                        <BreadcrumbLink href={breadcrumb.href}>
                          {breadcrumb.label}
                        </BreadcrumbLink>
                      )}
                    </BreadcrumbItem>
                    {idx < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
                  </React.Fragment>
                ))}
              </BreadcrumbList>
            </Breadcrumb>
          </>
        )}
      </div>
    </header>
  );
}
