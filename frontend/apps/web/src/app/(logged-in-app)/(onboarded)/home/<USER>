"use client";

import { AudioL<PERSON>, Paperclip } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import {
  useChatStream,
  useGetActions,
  useGetMemberProfileMe,
  useGetThreads,
} from "@pearl/api-hooks";

import {
  AIInput,
  AIInputButton,
  AIInputTextarea,
  AIInputTools,
} from "~/components/ui/kibo/Input";
import { ActionCardsSection } from "~/features/home/<USER>/ActionCardsSection";
import { MetricsHeader } from "~/features/home/<USER>/MetricsHeader";
import { useIsMobile } from "~/hooks/useIsMobile";
import { cn } from "~/lib/tailwind/cn";

export default function Home() {
  const router = useRouter();
  const isMobile = useIsMobile();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const [localThreadId, setLocalThreadId] = useState<string>(() => uuidv4());
  const { data: memberProfile } = useGetMemberProfileMe();
  const { refetch: refetchThreads } = useGetThreads();
  const { data: actionData } = useGetActions();

  useEffect(() => {
    setLocalThreadId(uuidv4());
  }, [setLocalThreadId]);

  const {
    handleSubmit,
    input,
    setInput,
    status: chatStatus,
  } = useChatStream({
    threadId: localThreadId,
    onError: (error) => {
      console.error("Chat error:", error);
    },
    onFinish: () => {
      void refetchThreads();
    },
  });

  const handleSubmitForm = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || chatStatus === "submitted") return;

    handleSubmit(undefined, {});
    handleNewThread(localThreadId);
  };

  const handleNewThread = (threadId: string) => {
    router.push(`/thread/${threadId}`);
  };

  const handleActionClick = (prompt: string) => {
    setInput(prompt);
    textareaRef.current?.focus();
  };

  return (
    <div
      className={cn(
        "flex items-center justify-center px-4 sm:px-6 lg:px-8",
        isMobile ? "h-[calc(100vh-120px-80px)] pb-20" : "h-[calc(100vh-120px)]",
      )}
    >
      <div className="w-full max-w-3xl">
        <MetricsHeader firstName={memberProfile?.firstName} />

        {/* AI Input section */}
        <AIInput onSubmit={handleSubmitForm}>
          <AIInputTextarea
            ref={textareaRef}
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="What would you like to discuss today?"
            disabled={chatStatus === "submitted"}
            className="max-h-[200px] min-h-[80px] resize-none"
          />
          <div className="mt-2 flex w-full items-center justify-end">
            <AIInputTools>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <Paperclip className="h-4 w-4" />
              </AIInputButton>
              <AIInputButton
                variant="default"
                disabled={chatStatus === "submitted"}
              >
                <AudioLines className="h-4 w-4" />
              </AIInputButton>
            </AIInputTools>
          </div>
        </AIInput>

        {actionData && actionData.length > 0 && (
          <ActionCardsSection
            actions={actionData}
            onActionClick={handleActionClick}
          />
        )}
      </div>
    </div>
  );
}
