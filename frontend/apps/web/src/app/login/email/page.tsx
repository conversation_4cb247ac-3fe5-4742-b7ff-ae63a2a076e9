"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";

import { useAuth } from "@pearl/api-client";

import Loader from "~/components/Loader";
import CodeAuthForm from "~/features/auth/components/CodeAuthForm";
import EmailAuthForm from "~/features/auth/components/EmailAuthForm";
import RedirectIfAuthenticatedRoute from "~/features/auth/guards/RedirectIfAuthenticatedRoute";

function EmailLogin() {
  const { requestOtc, verifyOtcToken, verifyOtcCode } = useAuth();
  const searchParams = useSearchParams();
  const queryToken = searchParams.get("token");
  const router = useRouter();

  const [loading, setLoading] = useState(false);
  const [token, setOTCToken] = useState<string | null>(null);

  useEffect(() => {
    const validateQueryToken = async () => {
      if (!queryToken) return;
      setLoading(true);
      try {
        await verifyOtcToken(queryToken);
        setOTCToken(queryToken);
      } catch (error) {
        console.error("Error verifying OTC token:", error);
        // Clear the token from the URL query parameters
        const newSearchParams = new URLSearchParams(searchParams);
        newSearchParams.delete("token");
        router.replace(
          `${window.location.pathname}${newSearchParams.toString() ? `?${newSearchParams.toString()}` : ""}`,
        );
      } finally {
        setLoading(false);
      }
    };
    void validateQueryToken();
  }, [queryToken, verifyOtcToken, searchParams, router]);

  const onSubmitEmail = async (email: string) => {
    const response = await requestOtc(email);
    setOTCToken(response.token);
  };

  return (
    <>
      {loading ? (
        <Loader />
      ) : token ? (
        <CodeAuthForm token={token} onSubmit={verifyOtcCode} />
      ) : (
        <EmailAuthForm onSubmit={onSubmitEmail} />
      )}
    </>
  );
}

export default function EmailLoginPage() {
  return (
    <RedirectIfAuthenticatedRoute>
      <Suspense fallback={<Loader />}>
        <EmailLogin />
      </Suspense>
    </RedirectIfAuthenticatedRoute>
  );
}
