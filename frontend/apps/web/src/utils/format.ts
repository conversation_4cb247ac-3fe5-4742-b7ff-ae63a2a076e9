export function formatNumber(value: string | number, showPlus = false): string {
  const num = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(num)) return value.toString();

  const prefix = showPlus && num > 0 ? "+" : "";

  if (num >= 1_000_000_000) {
    return `${prefix}${Math.round(num / 1_000_000_000)}B`;
  }
  if (num >= 1_000_000) {
    return `${prefix}${Math.round(num / 1_000_000)}M`;
  }
  if (num >= 1000) {
    return `${prefix}${Math.round(num / 1000)}k`;
  }
  return `${prefix}${Math.round(num).toLocaleString()}`;
}
