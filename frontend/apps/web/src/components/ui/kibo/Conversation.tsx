"use client";

import { ArrowDownIcon } from "lucide-react";
import type { ComponentProps } from "react";
import { useCallback } from "react";
import { StickToBottom, useStickToBottomContext } from "use-stick-to-bottom";

import { Button } from "~/components/ui/Button";
import { cn } from "~/lib/tailwind/cn";

export type AIConversationProps = ComponentProps<typeof StickToBottom>;
export const AIConversation = ({
  className,
  ...props
}: AIConversationProps) => (
  <StickToBottom
    className={cn(
      "relative flex-1 overflow-y-auto",
      "scrollbar-hide",
      "[&::-webkit-scrollbar]:hidden",
      "[-ms-overflow-style:none]",
      "[scrollbar-width:none]",
      className,
    )}
    resize="smooth"
    initial="smooth"
    role="log"
    {...props}
  />
);

export type AIConversationContentProps = ComponentProps<
  typeof StickToBottom.Content
>;
export const AIConversationContent = ({
  className,
  ...props
}: AIConversationContentProps) => (
  <StickToBottom.Content className={cn("p-4", className)} {...props} />
);

export type AIConversationScrollButtonProps = ComponentProps<typeof Button>;
export const AIConversationScrollButton = ({
  className,
  ...props
}: ComponentProps<typeof Button>) => {
  const { isAtBottom, scrollToBottom } = useStickToBottomContext();

  const handleScrollToBottom = useCallback(() => {
    void scrollToBottom();
  }, [scrollToBottom]);

  return (
    !isAtBottom && (
      <Button
        type="button"
        variant="outline"
        size="icon"
        className={cn(
          "absolute bottom-4 left-[50%] translate-x-[-50%] rounded-full",
          className,
        )}
        onClick={handleScrollToBottom}
        {...props}
      >
        <ArrowDownIcon className="size-4" />
      </Button>
    )
  );
};
