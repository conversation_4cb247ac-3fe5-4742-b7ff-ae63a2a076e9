import { cn } from "~/lib/tailwind/cn";

type SyncingSpinnerIconProps = {
  width?: number;
  height?: number;
  spinning?: boolean;
} & Omit<React.SVGProps<SVGSVGElement>, "width" | "height">;

export default function SyncingSpinnerIcon({
  width = 24,
  height = 24,
  spinning = true,
  className = "",
  ...props
}: SyncingSpinnerIconProps) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={cn(spinning ? "animate-spin" : "", className)}
      {...props}
    >
      <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
    </svg>
  );
}
