"use client";

import { Trash2, Upload } from "lucide-react";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";

import type { TeamDocument } from "@pearl/api-hooks";
import {
  useDeleteTeamDocument,
  useGetTeamDocuments,
  useUploadTeamDocument,
} from "@pearl/api-hooks";

import { Button } from "~/components/ui/Button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "~/components/ui/Dialog";
import { useIsAdmin } from "~/features/auth/hooks/useIsAdmin";
import { formatRelativeDate } from "~/utils/date";
import { extractErrorMessage } from "~/utils/error";
import { removeFileExtension } from "~/utils/file";

const ACCEPTED_FILE_TYPES = {
  "image/png": [".png"],
  "image/jpeg": [".jpg", ".jpeg"],
  "image/avif": [".avif"],
  "application/pdf": [".pdf"],
  "application/msword": [".doc"],
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
    ".docx",
  ],
  "text/plain": [".txt"],
  "application/vnd.ms-excel": [".xls"],
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
    ".xlsx",
  ],
  "application/vnd.ms-powerpoint": [".ppt"],
  "application/vnd.openxmlformats-officedocument.presentationml.presentation": [
    ".pptx",
  ],
};

const UPLOAD_AREA_MIN_HEIGHT = 180;

export function TeamDocuments() {
  const { data: documents = [], isLoading, error } = useGetTeamDocuments();
  const uploadMutation = useUploadTeamDocument();
  const deleteMutation = useDeleteTeamDocument();
  const { isAdmin } = useIsAdmin();

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    document?: TeamDocument;
  }>({
    open: false,
    document: undefined,
  });

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      acceptedFiles.forEach((file) => {
        uploadMutation.mutate(
          { file },
          {
            onSuccess: () => {
              if (acceptedFiles.length === 1) {
                toast.success(`Document "${file.name}" uploaded successfully`);
              } else {
                toast.success(
                  `${acceptedFiles.length} documents uploaded successfully`,
                );
              }
            },
            onError: (error: unknown) => {
              const message = extractErrorMessage(
                error,
                "Failed to upload document. Make sure you are an admin.",
              );
              toast.error(message);
            },
          },
        );
      });
    },
    [uploadMutation],
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: ACCEPTED_FILE_TYPES,
    multiple: true,
  });

  const handleDialogClose = useCallback(() => {
    setDeleteDialog({ open: false, document: undefined });
  }, []);

  const handleDeleteClick = useCallback((document: TeamDocument) => {
    setDeleteDialog({ open: true, document });
  }, []);

  const handleDeleteConfirm = useCallback(() => {
    if (!deleteDialog.document) return;

    deleteMutation.mutate(deleteDialog.document.id, {
      onSuccess: () => {
        const documentTitle = deleteDialog.document?.title || "document";
        toast.success(`Document "${documentTitle}" deleted successfully`);
        handleDialogClose();
      },
      onError: () => {
        toast.error("Failed to delete document. Make sure you are an admin.");
      },
    });
  }, [deleteDialog.document, deleteMutation, handleDialogClose]);

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="space-y-2">
          <div className="h-4 w-full rounded bg-muted" />
          <div className="h-4 w-3/4 rounded bg-muted" />
          <div className="h-4 w-1/2 rounded bg-muted" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-destructive bg-destructive/10 p-4">
        <p className="text-sm text-destructive">
          Failed to load documents. Please try again.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div className="space-y-1">
            <h3 className="text-lg font-semibold">Team documents</h3>
            <p className="text-sm text-muted-foreground">
              Import and manage documents (templates, playbooks, examples).
            </p>
          </div>
        </div>

        {isAdmin && (
          <div className="mt-6">
            <div
              {...getRootProps()}
              className={`flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-dashed p-8 text-center transition ${
                isDragActive
                  ? "border-primary bg-primary/10"
                  : "border-muted-foreground/40 bg-muted/40 hover:border-primary/60 hover:bg-muted/60"
              }`}
              style={{ minHeight: UPLOAD_AREA_MIN_HEIGHT }}
            >
              <input {...getInputProps()} />
              <Upload className="mx-auto mb-2 h-8 w-8 text-muted-foreground" />
              <p className="mb-1 text-sm text-muted-foreground">
                {isDragActive
                  ? "Drop your documents here"
                  : "Drag and drop your documents here"}
              </p>
              <p className="text-xs text-muted-foreground">
                or click to browse files
              </p>
              {uploadMutation.isPending && (
                <div className="mt-2 text-xs text-muted-foreground">
                  Uploading...
                </div>
              )}
            </div>
          </div>
        )}

        {documents.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-sm text-muted-foreground">
              No documents uploaded yet.
            </p>
          </div>
        ) : (
          <div className="-mx-4 sm:-mx-0">
            <table className="min-w-full divide-y divide-border">
              <thead>
                <tr>
                  <th className="py-3.5 pr-3 pl-4 text-left text-sm font-semibold text-foreground sm:pl-0">
                    Title
                  </th>
                  <th className="px-3 py-3.5 text-left text-sm font-semibold text-foreground">
                    Format
                  </th>
                  <th className="px-3 py-3.5 text-left text-sm font-semibold text-foreground">
                    Date uploaded
                  </th>
                  {isAdmin && (
                    <th className="py-3.5 pr-4 pl-3 text-right text-sm font-semibold text-foreground sm:pr-1">
                      <span className="sr-only">Delete</span>
                    </th>
                  )}
                </tr>
              </thead>
              <tbody className="divide-y divide-border">
                {documents.map((doc) => (
                  <tr key={doc.id}>
                    <td className="w-full max-w-0 py-4 pr-3 pl-4 text-sm font-medium text-foreground sm:w-auto sm:max-w-none sm:pl-0">
                      {removeFileExtension(doc.title)}
                    </td>
                    <td className="px-3 py-4 text-sm text-muted-foreground">
                      {doc.format.toUpperCase()}
                    </td>
                    <td className="px-3 py-4 text-sm text-muted-foreground">
                      {formatRelativeDate(doc.dateUploaded)}
                    </td>
                    {isAdmin && (
                      <td className="py-4 pr-4 pl-3 text-right text-sm font-medium sm:pr-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(doc)}
                          disabled={deleteMutation.isPending}
                          className="text-destructive transition hover:bg-destructive/10 hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </td>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onOpenChange={(open) => {
          if (!open) handleDialogClose();
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Document</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete &quot;
              {deleteDialog.document?.title}&quot;? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={handleDialogClose}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteMutation.isPending}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
