"use client";

import React from "react";
import { ThemeProvider } from "next-themes";

import { ApiClientProvider } from "@pearl/api-client";
import { QueryClientProvider } from "@pearl/api-hooks/query-client";

import { AuthProvider } from "~/features/auth/context/AuthContext";
import { apiClient } from "~/services/api/ApiClient";
import { tokenStorage } from "~/services/api/TokenStorage";

export default function Providers({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
        <QueryClientProvider>
          <AuthProvider>{children}</AuthProvider>
        </QueryClientProvider>
      </ApiClientProvider>
    </ThemeProvider>
  );
}
