"use client";

import { usePathname } from "next/navigation";

import {
  type Account,
  type AccountThread,
  type AccountThreadsResponse,
  type Thread,
  type ThreadsResponse,
  useGetAccountThreads,
  useGetThreads,
  useGetUserAccounts,
} from "@pearl/api-hooks";

export interface BreadcrumbItem {
  type: "page" | "link";
  label: string;
  href?: string;
}

export function useBreadcrumbs(): BreadcrumbItem[] {
  const pathname = usePathname();
  const segments = pathname.replace(/^\/+|\/+$/g, "").split("/");

  // Always call hooks (Rules of Hooks requirement)
  const { data: accounts } = useGetUserAccounts();
  const { data: threadsResponse } = useGetThreads();
  const { data: accountThreads } = useGetAccountThreads(
    segments[0] === "account" ? segments[1] : undefined,
  );

  // Home page has no breadcrumbs
  if (pathname === "/home") return [];

  // Static routes - use URL segment and format it nicely
  if (segments.length === 1 && segments[0]) {
    return [
      {
        type: "page",
        label: segments[0]
          .replace(/[-_]/g, " ")
          .replace(/\b\w/g, (c) => c.toUpperCase()),
      },
    ];
  }

  // Account routes
  if (segments[0] === "account") {
    return getAccountBreadcrumbs(segments, accounts, accountThreads);
  }

  // Thread routes
  if (segments[0] === "thread" && segments.length === 2 && segments[1]) {
    return getThreadBreadcrumbs(segments[1], threadsResponse);
  }

  return [];
}

function getAccountBreadcrumbs(
  segments: string[],
  accounts?: Account[],
  accountThreads?: AccountThreadsResponse,
): BreadcrumbItem[] {
  const account = accounts?.find((acc) => acc.crmId === segments[1]);

  // /account/[id] - Just account name
  if (segments.length === 2) {
    return account ? [{ type: "page", label: account.crmName }] : [];
  }

  // /account/[id]/[threadId] - Account name > Thread name
  if (segments.length === 3) {
    const thread = accountThreads?.threads.find(
      (t: AccountThread) => t.id === segments[2],
    );
    const breadcrumbs: BreadcrumbItem[] = [];

    if (account) {
      breadcrumbs.push({
        type: "link",
        label: account.crmName,
        href: `/account/${segments[1]}`,
      });
    }

    if (thread) {
      breadcrumbs.push({
        type: "page",
        label: thread.name || thread.createdAt.slice(2, 19),
      });
    }

    return breadcrumbs;
  }

  return [];
}

function getThreadBreadcrumbs(
  threadId: string,
  threadsResponse?: ThreadsResponse,
): BreadcrumbItem[] {
  const thread = threadsResponse?.threads.find(
    (t: Thread) => t.id === threadId,
  );

  return thread
    ? [
        {
          type: "page",
          label: thread.name || thread.createdAt.slice(2, 19),
        },
      ]
    : [];
}
