import baseConfig from "@pearl/eslint-config/base";
import reactConfig from "@pearl/eslint-config/react";

/** @type {import('typescript-eslint').Config} */
const eslintConfig = [
  {
    ignores: ["**/.next/**", "**/node_modules/**"],
  },
  ...baseConfig,
  ...reactConfig,
  {
    rules: {
      "@typescript-eslint/prefer-promise-reject-errors": "off",
      "@typescript-eslint/ban-ts-comment": "off", // allow use of @ts-expect-error without comment
      "@typescript-eslint/no-unnecessary-condition": "error",
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/no-floating-promises": "error",
      "@typescript-eslint/no-non-null-asserted-optional-chain": "error",
      "@typescript-eslint/require-await": "error",
      "@typescript-eslint/no-redundant-type-constituents": "error",
      "@typescript-eslint/no-unsafe-enum-comparison": "error",
      "@typescript-eslint/restrict-plus-operands": "error",
      "no-case-declarations": "error",
      "no-fallthrough": "error",
      "@typescript-eslint/restrict-template-expressions": "error",
      "@typescript-eslint/no-base-to-string": "error",
      "@typescript-eslint/prefer-for-of": "error",
      "@typescript-eslint/non-nullable-type-assertion-style": "error",
      "@typescript-eslint/consistent-type-definitions": "error",
    },
  },
];

export default eslintConfig;
