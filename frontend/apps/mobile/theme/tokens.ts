import { createTokens } from "tamagui";

export type Spacing = keyof typeof space;

const space = {
  // we keep the value true otherwise it crashes
  $true: 8,
  $none: 0,
  $xxs: 2,
  $xs: 4,
  $sm: 6,
  $md: 8,
  $lg: 12,
  $xl: 16,
  $2xl: 20,
  $3xl: 24,
  $4xl: 32,
  $5xl: 40,
  $6xl: 48,
  $7xl: 64,
  $8xl: 80,
  $9xl: 96,
  $10xl: 128,
  $layout: 20,
  // negative spaces
  "$-xxs": -2,
  "$-xs": -4,
  "$-sm": -6,
  "$-md": -8,
  "$-lg": -12,
  "$-xl": -16,
  "$-2xl": -20,
  "$-3xl": -24,
  "$-4xl": -32,
  "$-5xl": -40,
  "$-6xl": -48,
  "$-7xl": -64,
  "$-8xl": -80,
  "$-9xl": -96,
  "$-10xl": -128,
  $auto: "auto",
};

export const size = {
  $0: 0,
  "$0.25": 2,
  "$0.5": 4,
  "$0.75": 8,
  $1: 20,
  "$1.5": 24,
  $2: 28,
  "$2.5": 32,
  $3: 36,
  "$3.5": 40,
  $4: 44,
  $true: 44,
  "$4.5": 48,
  $5: 52,
  $6: 64,
  $7: 74,
  $8: 84,
  $9: 94,
  $10: 104,
  $11: 124,
  $12: 144,
  $13: 164,
  $14: 184,
  $15: 204,
  $16: 224,
  $17: 224,
  $18: 244,
  $19: 264,
  $20: 284,
  $full: "100%",
  $auto: "auto",
};

export const zIndex = {
  0: 0,
  1: 100,
  2: 200,
  3: 300,
  4: 400,
  5: 500,
};

export const radius = {
  $true: 4,
  $2: 2,
  $4: 4,
  $6: 6,
  $8: 8,
  $10: 10,
  $12: 12,
  $14: 14,
  $16: 16,
  $full: 9999,
};

export const tokens = createTokens({
  radius,
  zIndex,
  space,
  size,
});
