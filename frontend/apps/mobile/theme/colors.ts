export const colors = {
  white: "#FFFFFF",
  black: "rgba(20, 20, 20, 1)",
  black10: "rgba(0, 0, 0, 0.1)",
  black20: "rgba(0, 0, 0, 0.2)",
  transparent: "transparent",
  transparent5: "rgba(255, 255, 255, 0.05)",
  transparent10: "rgba(255, 255, 255, 0.1)",
  transparent30: "rgba(255, 255, 255, 0.3)",
  transparent60: "rgba(255, 255, 255, 0.6)",
  grey: "rgba(224, 224, 224, 1)",
  greyTransparent5: "rgba(224, 224, 224, 0.05)",
  greyTransparent10: "rgba(224, 224, 224, 0.1)",
  greyTransparent60: "rgba(224, 224, 224, 0.6)",
};

export const darkColors = {
  ...colors,
  loginInputBackground: "rgba(224, 224, 224, 0.05)",
  loginInputBorder: "rgba(224, 224, 224, 0.1)",
  loginButtonBackground: "rgba(82, 82, 92, 1)",
  loginButtonBorder: "rgba(224, 224, 224, 0.16)",
  infoText: colors.greyTransparent60,
  infoTextHighlight: colors.grey,
  codeInputBackground: colors.greyTransparent5,
  codeInputBorder: colors.greyTransparent10,
  codeInputFocusedBorder: colors.grey,
  drawerBorder: "rgba(26, 27, 31, 1)",
  chatBackground: "rgba(22, 22, 26, 1)",
  chatBarInputBackground: "rgba(224, 224, 224, 0.1)",
  chatBarInputIconColor: colors.grey,
  chatBarInputIconBackground: "rgba(26, 27, 31, 1)",
  chatBarSuggestionBackground: "rgba(224, 224, 224, 0.1)",
  chatUserMessageBackground: "rgba(224, 224, 224, 0.12)",
  chatPulsingCircle: colors.greyTransparent60,
  chatError: "rgba(255, 59, 48, 0.75)",
};

export const lightColors = {
  ...colors,
  grey: "rgba(38, 38, 42, 1)",
  loginInputBackground: "white",
  loginInputBorder: "rgba(9, 9, 11, 0.15)",
  loginButtonBackground: "rgba(24, 24, 27, 1)",
  loginButtonBorder: "rgba(24, 24, 27, 1)",
  infoText: "rgba(113, 113, 123, 1)",
  infoTextHighlight: "rgba(9, 9, 11, 1)",
  codeInputBackground: colors.white,
  codeInputBorder: "rgba(9, 9, 11, 0.15)",
  codeInputFocusedBorder: colors.black,
  drawerBorder: colors.white,
  chatBackground: "rgba(228, 230, 241, 1)",
  chatBarInputBackground: "rgba(244, 244, 246, 1)",
  chatBarInputIconColor: "rgba(26, 27, 31, 1)",
  chatBarInputIconBackground: "rgba(216, 218, 234, 1)",
  chatBarSuggestionBackground: colors.white,
  chatUserMessageBackground: "rgba(216, 218, 234, 1)",
  chatPulsingCircle: "rgba(20, 20, 20, 0.52)",
  chatError: "rgba(255, 59, 48, 1)",
};
