import { useTheme } from "@hooks/useTheme";
import { Toast, useToastController, useToastState } from "@tamagui/toast";
import { Button, H4, XStack, YStack } from "tamagui";

export function CurrentToast() {
  const { theme } = useTheme();
  const currentToast = useToastState();

  if (!currentToast || currentToast.isHandledNatively) {
    return null;
  }

  return (
    <Toast
      key={currentToast.id}
      duration={currentToast.duration}
      viewportName={currentToast.viewportName}
      enterStyle={{ opacity: 0, scale: 0.5, y: -25 }}
      exitStyle={{ opacity: 0, scale: 1, y: -20 }}
      theme={theme}
      rounded={12}
      animation="quick"
    >
      <YStack items="center" padding={12} gap={12}>
        <Toast.Title fontWeight="bold">{currentToast.title}</Toast.Title>
        {!!currentToast.message && (
          <Toast.Description>{currentToast.message}</Toast.Description>
        )}
      </YStack>
    </Toast>
  );
}

export function ToastControl() {
  const toast = useToastController();

  return (
    <YStack gap={12} items="center">
      <H4>{"Toast demo"}</H4>
      <XStack gap={12} justify="center">
        <Button
          onPress={() => {
            toast.show("Successfully saved!", {
              message: "Ok ok boss",
            });
          }}
        >
          {"Show"}
        </Button>
        <Button
          onPress={() => {
            toast.hide();
          }}
        >
          {"Hide"}
        </Button>
        <Button
          onPress={() => {
            toast.show("Successfully saved!", {
              duration: 1000,
              message: "Don't worry, we've got your data.",
              burntOptions: {
                from: "bottom",
              },
            });
          }}
        >
          {"Native Toast"}
        </Button>
      </XStack>
    </YStack>
  );
}
