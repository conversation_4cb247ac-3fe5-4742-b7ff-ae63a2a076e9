import { ChatSuggestion } from "@components/chat/ChatSuggestion";
import { AnimatedView } from "@components/ui/AnimatedView";
import type { StyleProp, ViewStyle } from "react-native";
import { FlatList } from "react-native";
import { FadeIn, FadeOut } from "react-native-reanimated";

type Suggestion = {
  description: string;
  title: string;
};

type ChatSuggestionsListProps = {
  onSuggestionPress: (suggestion: Suggestion) => void;
  suggestions: Suggestion[];
  contentContainerStyle?: StyleProp<ViewStyle>;
};

export const ChatSuggestionsList = ({
  suggestions,
  onSuggestionPress,
  contentContainerStyle,
}: ChatSuggestionsListProps) => (
  <AnimatedView entering={FadeIn} exiting={FadeOut}>
    <FlatList
      data={suggestions}
      horizontal
      showsHorizontalScrollIndicator={false}
      renderItem={({ item }) => (
        <ChatSuggestion
          title={item.title}
          description={item.description}
          onPress={() => onSuggestionPress(item)}
        />
      )}
      contentContainerStyle={[{ gap: 12 }, contentContainerStyle]}
    />
  </AnimatedView>
);
