import { AnimatedView } from "@components/ui/AnimatedView";
import { Icon } from "@components/ui/Icon";
import { useTheme } from "@hooks/useTheme";
import { hapticFeedback } from "@utils/haptics";
import { LinearGradient } from "expo-linear-gradient";
import { useState } from "react";
import Animated, {
  Extrapolation,
  interpolate,
  KeyboardState,
  runOnJS,
  useAnimatedKeyboard,
  useAnimatedReaction,
  useAnimatedStyle,
} from "react-native-reanimated";
import type { InputProps } from "tamagui";
import { Input, useWindowDimensions, View } from "tamagui";

const ChatBarInputIconButton = ({
  onPress,
  children,
}: {
  children: React.ReactNode;
  onPress?: () => void;
}) => (
  <View
    borderRadius="$full"
    backgroundColor="$chatBarInputIconBackground"
    width={52}
    height={52}
    justifyContent="center"
    alignItems="center"
    onPress={() => {
      hapticFeedback("success");
      onPress?.();
    }}
    pressStyle={{
      scale: 0.9,
    }}
  >
    {children}
  </View>
);

type ChatBarInputProps = InputProps & {
  onSendMessage: () => void;
  isStopButtonVisible?: boolean;
  onStop?: () => void;
  shouldAnimateOnFocus?: boolean;
};

const AnimatedInput = Animated.createAnimatedComponent(Input);

const KEYBOARD_OPENED_BORDER_RADIUS = 12;
const KEYBOARD_CLOSED_BORDER_RADIUS = 9999;
const KEYBOARD_HEIGHT_THRESHOLD = 100;

export const ChatBarInput = ({
  value,
  onSendMessage,
  shouldAnimateOnFocus = true,
  onStop,
  isStopButtonVisible,
  ...props
}: ChatBarInputProps) => {
  const keyboard = useAnimatedKeyboard();
  const { width: windowWidth } = useWindowDimensions();
  const { isLightTheme } = useTheme();

  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false);

  const hasValue = value?.length && value.length > 0;

  // Handle keyboard opening and closing
  // LinearGradient does not support animated border radius
  useAnimatedReaction(
    () => ({
      state: keyboard.state.value,
      height: keyboard.height.value,
    }),
    ({ state, height }) => {
      if (!shouldAnimateOnFocus || height < KEYBOARD_HEIGHT_THRESHOLD) {
        return;
      }
      runOnJS(setIsKeyboardOpen)(
        [KeyboardState.OPENING, KeyboardState.OPEN].includes(state),
      );
    },
  );

  const animatedStyle = useAnimatedStyle(() => {
    if (!shouldAnimateOnFocus) {
      return {
        width: windowWidth - 60,
      };
    }

    const width = interpolate(
      keyboard.height.value,
      [0, KEYBOARD_HEIGHT_THRESHOLD],
      [windowWidth - 60, windowWidth],
      Extrapolation.CLAMP,
    );

    return {
      width,
    };
  });

  const animatedBorderRadiusStyle = useAnimatedStyle(() => {
    if (!shouldAnimateOnFocus) {
      return {
        borderTopLeftRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
        borderBottomLeftRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
        borderTopRightRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
        borderBottomRightRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
      };
    }

    return {
      borderTopLeftRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, KEYBOARD_OPENED_BORDER_RADIUS],
        Extrapolation.CLAMP,
      ),
      borderBottomLeftRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, 0],
        Extrapolation.CLAMP,
      ),
      borderTopRightRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, KEYBOARD_OPENED_BORDER_RADIUS],
        Extrapolation.CLAMP,
      ),
      borderBottomRightRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, 0],
        Extrapolation.CLAMP,
      ),
    };
  });

  const animatedInputBorderRadiusStyle = useAnimatedStyle(() => {
    if (!shouldAnimateOnFocus) {
      return {
        borderTopLeftRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
        borderBottomLeftRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
      };
    }

    return {
      borderTopLeftRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, KEYBOARD_OPENED_BORDER_RADIUS],
        Extrapolation.CLAMP,
      ),
      borderBottomLeftRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, 0],
        Extrapolation.CLAMP,
      ),
    };
  });

  const animatedRightContainerBorderRadiusStyle = useAnimatedStyle(() => {
    if (!shouldAnimateOnFocus) {
      return {
        borderTopRightRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
        borderBottomRightRadius: KEYBOARD_CLOSED_BORDER_RADIUS,
      };
    }

    return {
      borderTopRightRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, KEYBOARD_OPENED_BORDER_RADIUS],
        Extrapolation.CLAMP,
      ),
      borderBottomRightRadius: interpolate(
        keyboard.height.value,
        [0, KEYBOARD_HEIGHT_THRESHOLD],
        [KEYBOARD_CLOSED_BORDER_RADIUS, 0],
        Extrapolation.CLAMP,
      ),
    };
  });

  return (
    <AnimatedView style={animatedStyle} alignSelf="center">
      <LinearGradient
        colors={
          isLightTheme && (!shouldAnimateOnFocus || !isKeyboardOpen)
            ? // Hack to hide the gradient when the theme is light
              ["transparent", "transparent"]
            : ["rgba(151, 160, 211, 1)", "rgba(255, 172, 134, 1)"]
        }
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{
          height:
            isLightTheme && (!shouldAnimateOnFocus || !isKeyboardOpen)
              ? 68
              : 69,
          justifyContent: "flex-end",
          borderTopLeftRadius:
            shouldAnimateOnFocus && isKeyboardOpen
              ? KEYBOARD_OPENED_BORDER_RADIUS
              : KEYBOARD_CLOSED_BORDER_RADIUS,
          borderTopRightRadius:
            shouldAnimateOnFocus && isKeyboardOpen
              ? KEYBOARD_OPENED_BORDER_RADIUS
              : KEYBOARD_CLOSED_BORDER_RADIUS,
          borderBottomLeftRadius:
            shouldAnimateOnFocus && isKeyboardOpen
              ? 0
              : KEYBOARD_CLOSED_BORDER_RADIUS,
          borderBottomRightRadius:
            shouldAnimateOnFocus && isKeyboardOpen
              ? 0
              : KEYBOARD_CLOSED_BORDER_RADIUS,
        }}
      >
        <AnimatedView
          flexDirection="row"
          style={[
            animatedBorderRadiusStyle,
            {
              boxShadow: [
                {
                  offsetX: 0,
                  offsetY: 20,
                  blurRadius: "44px",
                  spreadDistance: "-10px",
                  color: "rgba(0, 0, 0, 0.2)",
                },
                {
                  offsetX: 0,
                  offsetY: 2,
                  blurRadius: "2px",
                  spreadDistance: "1px",
                  color: "rgba(0, 0, 0, 0.04)",
                },
              ],
            },
          ]}
          backgroundColor="$chatBackground"
          borderWidth={
            isLightTheme && (!shouldAnimateOnFocus || !isKeyboardOpen) ? 1 : 0
          }
          borderColor={
            isLightTheme && (!shouldAnimateOnFocus || !isKeyboardOpen)
              ? "$white"
              : undefined
          }
        >
          <AnimatedView
            flexDirection="row"
            height={68}
            flex={1}
            backgroundColor="$chatBarInputBackground"
            style={animatedBorderRadiusStyle}
            overflow="hidden"
          >
            <AnimatedInput
              height={68}
              style={animatedInputBorderRadiusStyle}
              borderWidth={0}
              placeholderTextColor="$infoText"
              backgroundColor="$chatBarInputBackground"
              padding="$2xl"
              flex={1}
              keyboardAppearance={isLightTheme ? "light" : "dark"}
              value={value}
              {...props}
            />
            <View
              backgroundColor="$chatBarInputBackground"
              style={animatedRightContainerBorderRadiusStyle}
              width={62}
              justifyContent="center"
              alignItems="center"
            >
              {hasValue && (
                <ChatBarInputIconButton onPress={onSendMessage}>
                  <Icon
                    name="ArrowUp"
                    size={28}
                    color="$chatBarInputIconColor"
                  />
                </ChatBarInputIconButton>
              )}
              {!hasValue && isStopButtonVisible && (
                <ChatBarInputIconButton onPress={onStop}>
                  <Icon
                    name="StopCircle"
                    size={28}
                    color="$chatBarInputIconColor"
                  />
                </ChatBarInputIconButton>
              )}
            </View>
          </AnimatedView>
        </AnimatedView>
      </LinearGradient>
    </AnimatedView>
  );
};
