import type { UIMessage } from "ai";
import Markdown from "react-native-markdown-display";
// TODO: Double check this
// eslint-disable-next-line @typescript-eslint/no-restricted-imports
import { Text, useTheme as useTamaguiTheme } from "tamagui";

type AssistantMessageProps = {
  message: UIMessage;
};

export const AssistantMessage = ({ message }: AssistantMessageProps) => {
  const colors = useTamaguiTheme();

  return (
    <Text
      fontFamily="$regular"
      fontSize={16}
      lineHeight={24}
      color="$grey"
      letterSpacing={-0.16}
      marginRight="$2xl"
    >
      <Markdown
        style={{
          // TODO: add markdown styles when we have a design system
          body: {
            fontSize: 16,
            lineHeight: 24,
            color: colors.grey.val,
          },
        }}
      >
        {message.content}
      </Markdown>
    </Text>
  );
};
