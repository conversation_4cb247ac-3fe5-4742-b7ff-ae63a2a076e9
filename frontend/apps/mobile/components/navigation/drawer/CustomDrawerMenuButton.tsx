import { Icon } from "@components/ui/Icon";
import type { DrawerNavigationProp } from "@react-navigation/drawer";
import type { HeaderButtonProps } from "@react-navigation/elements";
import { HeaderButton } from "@react-navigation/elements";
import type { ParamListBase } from "@react-navigation/native";
import { DrawerActions, useNavigation } from "@react-navigation/native";

export const CustomDrawerMenuButton = (props: Partial<HeaderButtonProps>) => {
  const navigation = useNavigation<DrawerNavigationProp<ParamListBase>>();

  return (
    <HeaderButton
      {...props}
      onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
    >
      <Icon name="Menu" size={32} />
    </HeaderButton>
  );
};
