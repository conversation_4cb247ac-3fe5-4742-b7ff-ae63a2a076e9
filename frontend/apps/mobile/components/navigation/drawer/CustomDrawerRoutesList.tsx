import { useTheme } from "@hooks/useTheme";
import type { DrawerContentComponentProps } from "@react-navigation/drawer";
import { CommonActions, DrawerActions } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { Text, View, XStack } from "tamagui";

type CustomDrawerItemListProps = DrawerContentComponentProps;

export const CustomDrawerRoutesList = ({
  state,
  navigation,
  descriptors,
}: CustomDrawerItemListProps) => {
  const { isDarkTheme } = useTheme();

  const routes = state.routes.filter(
    (route) => !["_sitemap", "+not-found", "chat/index"].includes(route.name),
  );

  const linearGradientDarkColors = ["#272931", "#1B1C20"] as const;
  const linearGradientLightColors = ["#D8DAEA", "#F0EFF6"] as const;

  const getGradientColors = (
    focused: boolean,
    isDark: boolean,
  ): readonly [string, string] => {
    if (!focused) {
      return ["transparent", "transparent"] as const;
    }

    return isDark ? linearGradientDarkColors : linearGradientLightColors;
  };

  return routes.map((route, i) => {
    const focused = i === state.index;

    const onPress = () => {
      const event = navigation.emit({
        type: "drawerItemPress",
        target: route.key,
        canPreventDefault: true,
      });

      if (!event.defaultPrevented) {
        navigation.dispatch({
          ...(focused
            ? DrawerActions.closeDrawer()
            : CommonActions.navigate(route)),
          target: state.key,
        });
      }
    };

    const options = descriptors[route.key]?.options ?? {};
    const { title, drawerLabel, drawerIcon, drawerItemStyle } = options;

    let label;

    if (drawerLabel !== undefined) {
      label = drawerLabel;
    } else if (title !== undefined) {
      label = title;
    } else {
      label = route.name;
    }

    const iconNode = drawerIcon
      ? drawerIcon({ size: 18, focused, color: "#E0E0E0" })
      : null;

    return (
      <View
        onPress={onPress}
        key={route.key}
        pressStyle={{
          scale: 0.98,
        }}
      >
        <LinearGradient
          colors={getGradientColors(focused, isDarkTheme)}
          style={{ height: 36, width: "100%", borderRadius: 8 }}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
        >
          <XStack
            alignItems="center"
            gap="$lg"
            paddingVertical="$md"
            paddingHorizontal={10}
            style={drawerItemStyle}
          >
            {iconNode}
            {typeof label === "string" ? (
              <Text numberOfLines={1} fontSize={14} lineHeight={20}>
                {label}
              </Text>
            ) : (
              label({ color: "#E0E0E0", focused })
            )}
          </XStack>
        </LinearGradient>
      </View>
    );
  });
};
