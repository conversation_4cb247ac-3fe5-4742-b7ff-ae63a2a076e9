import { Background } from "@components/layout/Background";
import type { AnimatedViewProps } from "@components/ui/AnimatedView";
import { AnimatedView } from "@components/ui/AnimatedView";
import { useAssets } from "@hooks/useAssets";
import { useGetDefaultHeaderHeight } from "@hooks/useGetDefaultHeaderHeight";
import { HeaderShownContext } from "@react-navigation/elements";
import type { Spacing } from "@theme/tokens";
import type { ImageProps } from "expo-image";
import { useContext } from "react";
import type { ViewStyle } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import type { GetThemeValueForKey } from "tamagui";
import { getTokens, Overlay, Spinner } from "tamagui";

export interface LayoutProps
  extends Omit<AnimatedViewProps, "backgroundColor"> {
  backgroundColor?: GetThemeValueForKey<"color">;
  backgroundImageSource?: ImageProps["source"];
  isLoading?: boolean;
  isModal?: boolean;
  withBackground?: boolean;
  withDefaultPaddingTop?: boolean;
  withEdgeBottom?: boolean;
  withEdgeTop?: boolean;
  withHorizontalPadding?: boolean;
}

export const Layout = ({
  backgroundColor = "$background",
  withBackground = true,
  backgroundImageSource,
  withHorizontalPadding = true,
  withEdgeTop,
  withEdgeBottom = true,
  withDefaultPaddingTop = true,
  children,
  isLoading,
  isModal,
  style,
  ...props
}: LayoutProps) => {
  const { backgrounds } = useAssets();
  const insets = useSafeAreaInsets();
  const { defaultHeaderHeight } = useGetDefaultHeaderHeight(isModal);
  const isParentHeaderShown = useContext(HeaderShownContext);

  // Fix for the SafeAreaView issue
  // https://github.com/th3rdwave/react-native-safe-area-context/issues/226
  // Refactored to avoid nested ternary expressions for clarity and lint compliance
  let paddingTop: number;
  if (isParentHeaderShown) {
    paddingTop = defaultHeaderHeight;
  } else if (withEdgeTop === true || withEdgeTop === undefined) {
    paddingTop = insets.top;
  } else {
    paddingTop = 0;
  }

  const safeAreaPaddings: ViewStyle = {
    paddingTop,
    paddingBottom: withEdgeBottom ? insets.bottom : 0,
    // TODO: replace with getToken('$layout')
    paddingHorizontal: withHorizontalPadding ? 20 : 0,
  };

  const stylePaddingTop =
    getTokens().space[
      // TODO: check if this is correct
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      (props.paddingTop as Spacing) || (withDefaultPaddingTop ? "$md" : "$none")
    ].val || 0;

  const backgroundImage = backgroundImageSource
    ? backgroundImageSource
    : backgrounds.default;

  return (
    <>
      <AnimatedView
        flex={1}
        backgroundColor={backgroundColor}
        style={[
          safeAreaPaddings,
          {
            paddingTop:
              (safeAreaPaddings.paddingTop as number) + stylePaddingTop || 0,
          },
          style,
        ]}
        {...props}
      >
        {withBackground ? (
          <Background backgroundImageSource={backgroundImage} />
        ) : null}
        {children}
      </AnimatedView>
      {isLoading ? (
        <Overlay
          backgroundColor="$transparent30"
          justifyContent="center"
          alignItems="center"
        >
          <Spinner size="large" color="$grey" />
        </Overlay>
      ) : null}
    </>
  );
};
