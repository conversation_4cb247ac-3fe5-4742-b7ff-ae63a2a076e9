import type { ConfigContext, ExpoConfig } from "@expo/config";

import packageJson from "./package.json";

export const APP_FLAVOR = process.env.EXPO_PUBLIC_APP_FLAVOR;

const configureApp = ({
  config,
  name,
  packageName,
}: ConfigContext & {
  name: string;
  packageName: string;
}): ExpoConfig => {
  return {
    ...config,
    name,
    slug: "pearl",
    owner: "heypearl",
    version: packageJson.version,
    orientation: "portrait",
    scheme: "pearl",
    icon: "./assets/images/icons/<EMAIL>",
    userInterfaceStyle: "automatic",
    assetBundlePatterns: ["**/*"],
    ios: {
      bundleIdentifier: packageName,
      supportsTablet: false,
      icon: "./assets/images/icons/ios-dark.png",
      config: {
        usesNonExemptEncryption: false,
      },
      infoPlist: {
        CFBundleAllowMixedLocalizations: true,
      },
      associatedDomains: [
        "applinks:heypearl.ai",
        "applinks:app.heypearl.ai",
        "applinks:app-staging.heypearl.ai",
      ],
    },
    android: {
      package: packageName,
      adaptiveIcon: {
        foregroundImage: "./assets/images/icons/adaptive-icon.png",
        backgroundColor: "#e4e6f1",
      },
      edgeToEdgeEnabled: true,
      intentFilters: [
        {
          action: "VIEW",
          autoVerify: true,
          data: [
            {
              scheme: "https",
              host: "*.heypearl.ai",
              pathPrefix: "/*",
            },
          ],
          category: ["BROWSABLE", "DEFAULT"],
        },
      ],
    },
    locales: {
      fr: "./locales/fr/expo.json",
      en: "./locales/en/expo.json",
    },
    plugins: [
      "expo-router",
      "expo-font",
      [
        "expo-build-properties",
        {
          ios: {
            newArchEnabled: true,
          },
          android: {
            newArchEnabled: true,
          },
        },
      ],
      "expo-web-browser",
      [
        "expo-audio",
        {
          microphonePermission:
            "Allow $(PRODUCT_NAME) to access your microphone.",
        },
      ],
      "expo-localization",
      [
        "expo-splash-screen",
        {
          backgroundColor: "#e4e6f1",
          image: "./assets/images/splash/splash-icon-light.png",
          dark: {
            image: "./assets/images/splash/splash-icon-dark.png",
            backgroundColor: "#202020",
          },
          imageWidth: 150,
        },
      ],
    ],
    updates: {
      url: "https://u.expo.dev/6c599539-4db4-4410-bcd9-a312dbb73e8d",
    },
    runtimeVersion: {
      policy: "appVersion",
    },
    experiments: {
      typedRoutes: true,
    },
    extra: {
      router: {},
      eas: {
        projectId: "6c599539-4db4-4410-bcd9-a312dbb73e8d",
      },
    },
  };
};

export default function AppConfig(ctx: ConfigContext) {
  const { config, projectRoot, staticConfigPath, packageJsonPath } = ctx;
  switch (APP_FLAVOR) {
    case "development":
      return configureApp({
        config,
        name: "Pearl Dev",
        packageName: "com.heypearl.app.dev",
        projectRoot,
        staticConfigPath,
        packageJsonPath,
      });
    case "preview":
      return configureApp({
        config,
        name: "Pearl Preview",
        packageName: "com.heypearl.app.preview",
        projectRoot,
        staticConfigPath,
        packageJsonPath,
      });
    case "production":
      return configureApp({
        config,
        name: "Pearl",
        packageName: "com.heypearl.app",
        projectRoot,
        staticConfigPath,
        packageJsonPath,
      });
    default:
      throw new Error(`Invalid app flavor: ${APP_FLAVOR}`);
  }
}
