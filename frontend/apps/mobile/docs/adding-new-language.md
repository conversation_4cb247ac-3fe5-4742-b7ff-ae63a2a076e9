# Adding a New Language to the Mobile App

This guide walks you through the process of adding a new language to the Pearl mobile application. The mobile app uses i18next for internationalization with TypeScript support.

## Overview

The mobile app's localization system consists of:

- **Shared locale files** in `packages/locales/` (used by both web and mobile)
- **i18n configuration** in `apps/mobile/utils/i18n.ts`
- **Language detection** based on device locale
- **Fallback support** to English if unsupported language is detected

## Current Supported Languages

- English (`en`) - Default/Fallback language
- French (`fr`)

## Step-by-Step Guide

### 1. Create Translation Files

Create a new directory for your language in the shared locales package:

```bash
mkdir packages/locales/[language-code]
```

For example, for Spanish:

```bash
mkdir packages/locales/es
```

### 2. Add Translation JSON Files

Inside your new language directory, create a JSON file for each "resource" (components, screens, etc.):

- `components.json`

Contains translations for reusable UI components.

- `screens.json`

Contains translations for screen-specific content.

### 3. Update i18n Configuration

Edit `apps/mobile/utils/i18n.ts` to include your new language:

#### Add Language Import

Add import statements for your new language files at the top:

```typescript
import esComponents from "@/locales/es/components.json";
import esScreens from "@/locales/es/screens.json";
```

#### Update Languages Type

Add your language code to the `Languages` constant:

```typescript
export const Languages = {
  en: "en",
  fr: "fr",
  es: "es", // Add your new language
} as const;
```

#### Update Supported Languages Array

Add your language code to the supported languages array:

```typescript
export const SUPPORTED_LANGUAGES = ["en", "fr", "es"];
```

#### Update Local Resources

Add your language resources to the `localResources` object:

```typescript
export const localResources = {
  fr: {
    screens: frScreens,
    components: frComponents,
  },
  en: {
    screens: enScreens,
    components: enComponents,
  },
  es: {
    // Add your new language
    screens: esScreens,
    components: esComponents,
  },
} as const;
```

### 4. Test Your Implementation

1. **Build the app** to ensure there are no TypeScript errors
2. **Change your device language** to the new language (if supported by your device)
3. **Test the app** to verify translations are loading correctly
4. **Test fallback behavior** by setting an unsupported device language

### 5. Validation Checklist

- [ ] Translation JSON files created with complete translations
- [ ] No missing translation keys compared to English version
- [ ] Import statements added to `i18n.ts`
- [ ] Language code added to `Languages` constant
- [ ] Language code added to `SUPPORTED_LANGUAGES` array
- [ ] Language resources added to `localResources` object
- [ ] App builds without TypeScript errors
- [ ] Translations display correctly in the app
- [ ] Fallback to English works for missing keys

## Translation Guidelines

### File Structure

- **`components.json`**: Reusable UI component translations
- **`screens.json`**: Screen-specific content translations

### Naming Conventions

- Use lowercase language codes (ISO 639-1)
- Use nested objects for logical grouping
- Use descriptive keys that indicate context

### Translation Best Practices

- Keep translations concise and natural
- Consider cultural context, not just literal translation
- Test translations with actual content and UI constraints
- Maintain consistency in terminology across the app

## Troubleshooting

### Common Issues

**Build Errors**: Ensure all import paths are correct and files exist
**Missing Translations**: Check that all keys from English files are present
**Language Not Detected**: Verify language code is in `SUPPORTED_LANGUAGES` array
**TypeScript Errors**: Ensure `localResources` object structure matches existing pattern

### Getting Help

If you encounter issues:

1. Check the console for specific error messages
2. Compare your implementation with existing language files
3. Verify that device language detection is working correctly
4. Test with a clean build to ensure cached translations are cleared

## Technical Details

### Language Detection Flow

1. Device locale is detected using `expo-localization`
2. Language code is extracted from device locale
3. If supported, that language is used; otherwise falls back to English
4. Day.js locale is also set to match the selected language

### File Organization

```
packages/locales/
├── en/
│   ├── components.json
│   └── screens.json
├── fr/
│   ├── components.json
│   └── screens.json
└── [new-language]/
    ├── components.json
    └── screens.json
```

This shared package structure allows both web and mobile apps to use the same translation files, ensuring consistency across platforms.
