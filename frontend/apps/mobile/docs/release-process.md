# 🚅 Release process

## Versionning

The versionning is done in the `package.json` file.

Also, we use remote versioning.

See [App Versions](https://docs.expo.dev/build-reference/app-versions/)

## Process

1. Bump the build version in package.json

2. Commit, push the changes and create a tag following this pattern name `v[VERSION]-[BUILD_NUMBER]`

3. Create a release using `gh release create "[VERSION]-[BUILD_NUMBER]" --generate-notes`

4. Launch the [Create release](.github/workflows/create-release.yml) workflow.

5. Announce the new release to the team

## OTA Update

This process is used to update the app on the device without the need to download the new version from the store.

- `eas update --branch production --message "New update / Commit: [COMMIT_HASH]"`

## Notes

- The bump and release process will be automated in the future using a GitHub action.
- OTA Update will be automated in the future using a GitHub action.
