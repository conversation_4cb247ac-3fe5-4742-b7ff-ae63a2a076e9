import { Link, Stack } from "expo-router";
import { StyleSheet } from "react-native";
import { Text, View } from "tamagui";

export default function NotFoundScreen() {
  return (
    <>
      <Stack.Screen options={{ title: "Oops!" }} />
      <View m={10}>
        <Text>{"This screen doesn't exist."}</Text>
        <Link href="/home" style={styles.link}>
          <Text style={styles.linkText}>{"Go to home screen!"}</Text>
        </Link>
      </View>
    </>
  );
}

const styles = StyleSheet.create({
  link: {
    marginTop: 15,
    paddingVertical: 15,
  },
  // eslint-disable-next-line react-native/no-color-literals
  linkText: {
    fontSize: 14,
    // TODO: Add a theme for this
    color: "#2e78b7",
  },
});
