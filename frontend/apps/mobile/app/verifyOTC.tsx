import { KeyboardAwareScrollBox } from "@components/layout/KeyboardAwareScrollBox";
import { Layout } from "@components/layout/Layout";
import { CodeInput } from "@components/login/CodeInput";
import { LoginButton } from "@components/login/LoginButton";
import { Image } from "@components/ui/Image";
import { useAssets } from "@hooks/useAssets";
import { useTheme } from "@hooks/useTheme";
import { useOTCTokenAtom } from "@state/auth";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useCallback, useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import type {
  NativeSyntheticEvent,
  TextInput,
  TextInputSubmitEditingEventData,
} from "react-native";
import { Keyboard } from "react-native";
import { Button, Heading, Text, View } from "tamagui";

import { useAuth } from "@pearl/api-client";

const APP_ICON_SIZE = 42;

export default function VerifyOTC() {
  const router = useRouter();
  const { t } = useTranslation(["screens"]);
  const { theme } = useTheme();
  const { icons } = useAssets();
  const { verifyOtcCode } = useAuth();

  const { email } = useLocalSearchParams<{ email: string }>();

  const codeFieldRef = useRef<TextInput>(null);

  const [token] = useOTCTokenAtom();

  const [code, setCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const isCodeValid = code.length === 6;

  useEffect(() => {
    setTimeout(() => {
      codeFieldRef.current?.focus();
    }, 750);
  }, []);

  useEffect(() => {
    if (isCodeValid) {
      Keyboard.dismiss();
    }
  }, [isCodeValid]);

  const onSubmitCode = useCallback(
    async (newCode: string) => {
      try {
        setIsLoading(true);
        await verifyOtcCode(token, newCode);
        router.dismissAll();
        router.replace("/(app)");
      } catch (error) {
        console.error("Error verifying OTC code:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [verifyOtcCode, token, router],
  );

  const onSubmitEditing = useCallback(
    (e: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => {
      try {
        onSubmitCode(e.nativeEvent.text);
      } catch (error) {
        console.error("Error submitting email:", error);
      }
    },
    [onSubmitCode],
  );

  return (
    <Layout isLoading={isLoading}>
      <KeyboardAwareScrollBox
        contentContainerStyle={{ flex: 1, justifyContent: "center" }}
      >
        <Image
          //TODO: fix dark app icon
          source={icons.appIcon}
          width={APP_ICON_SIZE}
          height={APP_ICON_SIZE}
          alignSelf="center"
        />
        <Heading
          fontSize={20}
          textAlign="center"
          marginTop="$3xl"
          marginBottom="$5xl"
        >
          {t("screens:verifyOTC.title")}
        </Heading>
        <Text
          fontFamily="$regular"
          textAlign="center"
          color="$infoText"
          lineHeight={24}
        >
          {t("screens:verifyOTC.sent_to")}{" "}
          <Text
            textAlign="center"
            color="$infoTextHighlight"
            textDecorationLine="underline"
          >
            {email}
            {"."}
          </Text>{" "}
          {t("screens:verifyOTC.enter_code")}
        </Text>
        <View
          paddingHorizontal="$4xl"
          alignItems="center"
          marginVertical="$2xl"
        >
          <CodeInput
            ref={codeFieldRef}
            value={code}
            setValue={setCode}
            autoFocus
            textContentType="oneTimeCode"
            cellCount={6}
            keyboardAppearance={theme}
            returnKeyType="send"
            onSubmitEditing={onSubmitEditing}
            keyboardType="number-pad"
            autoComplete="one-time-code"
          />
        </View>
        <LoginButton
          onPress={() => onSubmitCode(code)}
          marginBottom="$2xl"
          disabled={!isCodeValid || isLoading}
        >
          <Button.Text>{t("screens:verifyOTC.cta")}</Button.Text>
        </LoginButton>
        <Text
          fontFamily="$regular"
          textAlign="center"
          color="$infoText"
          lineHeight={24}
          onPress={() => router.back()}
          pressStyle={{
            scale: 0.98,
          }}
        >
          {t("screens:verifyOTC.cannot_receive_code")}{" "}
          <Text color="$infoTextHighlight" textDecorationLine="underline">
            {t("screens:verifyOTC.reenter_email")}
          </Text>
        </Text>
      </KeyboardAwareScrollBox>
    </Layout>
  );
}
