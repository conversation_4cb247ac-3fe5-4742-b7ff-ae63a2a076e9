import { Layout } from "@components/layout/Layout";
import { Image } from "@components/ui/Image";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { Button, Heading, Text, YStack } from "tamagui";

import { useAssets } from "@/hooks/useAssets";

export default function ErrorScreen() {
  const router = useRouter();
  const { t } = useTranslation(["screens"]);
  const { icons } = useAssets();

  return (
    <Layout justifyContent="center" alignItems="center">
      <Image
        source={icons.appIcon}
        width={120}
        aspectRatio={728 / 512}
        alignSelf="center"
        marginBottom="$lg"
      />

      <Heading
        fontSize={28}
        fontWeight="700"
        textAlign="center"
        marginBottom="$md"
        color="$grey"
      >
        {t("screens:error.title")}
      </Heading>

      <YStack gap="$md" alignItems="center" marginBottom="$2xl">
        <Text
          fontSize={16}
          lineHeight={24}
          textAlign="center"
          color="$infoText"
          maxWidth={400}
        >
          {t("screens:error.description")}
        </Text>
      </YStack>

      <Button
        backgroundColor="$loginButtonBackground"
        borderColor="$loginButtonBorder"
        borderWidth={1}
        height={48}
        borderRadius={8}
        paddingHorizontal="$2xl"
        onPress={() => router.dismissTo("/home")}
        pressStyle={{
          scale: 0.98,
        }}
        width="$full"
      >
        <Button.Text color="$white" fontSize={16} fontWeight="500">
          {t("screens:error.cta")}
        </Button.Text>
      </Button>
    </Layout>
  );
}
