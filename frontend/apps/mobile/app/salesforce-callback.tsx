import { useLocalSearchParams, useRouter } from "expo-router";
import { useEffect } from "react";

import { useGetUserCRM, useSalesforceCallback } from "@pearl/api-hooks";

import { Layout } from "@/components/layout/Layout";
import {
  SalesforceErrorStep,
  SalesforceLoadingStep,
  SalesforceSuccessStep,
} from "@/components/salesforce";
import { usePreventBack } from "@/hooks/usePreventBack";

export default function SalesforceCallbackScreen() {
  const router = useRouter();

  const { refetch: refreshUserCRM } = useGetUserCRM();

  const { code, state } = useLocalSearchParams<{
    code: string;
    state: string;
  }>();

  usePreventBack();

  const {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  } = useSalesforceCallback();

  useEffect(() => {
    if (code && state) {
      processCallback(code, state);
    }
  }, [processCallback, code, state]);

  useEffect(() => {
    (async () => {
      if (isSuccess) {
        await refreshUserCRM();
        const timer = setTimeout(() => {
          router.push("/home");
        }, 3000);

        return () => clearTimeout(timer);
      }

      if (isError) {
        const timer = setTimeout(() => {
          router.push("/onboarding");
        }, 3000);

        return () => clearTimeout(timer);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess, isError]);

  let content = null;

  if (isLoading) {
    content = <SalesforceLoadingStep stage="connecting" />;
  } else if (isImporting) {
    content = <SalesforceLoadingStep stage="importing" />;
  } else if (isSuccess) {
    content = <SalesforceSuccessStep />;
  } else if (isError) {
    content = <SalesforceErrorStep errorMessage={errorMessage} />;
  }

  return (
    <Layout justifyContent="center" alignItems="center" gap="$2xl">
      {content}
    </Layout>
  );
}
