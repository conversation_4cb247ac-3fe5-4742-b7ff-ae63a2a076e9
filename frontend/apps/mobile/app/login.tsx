import { KeyboardAwareScrollBox } from "@components/layout/KeyboardAwareScrollBox";
import { Layout } from "@components/layout/Layout";
import { LoginButton } from "@components/login/LoginButton";
import { LoginInput } from "@components/login/LoginInput";
import { Image } from "@components/ui/Image";
import { useAssets } from "@hooks/useAssets";
import { useOTCTokenAtom } from "@state/auth";
import { openURL } from "expo-linking";
import { useRouter } from "expo-router";
import { useCallback, useState } from "react";
import { useTranslation } from "react-i18next";
import type {
  NativeSyntheticEvent,
  TextInputSubmitEditingEventData,
} from "react-native";
import { Button, Heading, Text } from "tamagui";

import { useAuth } from "@pearl/api-client";

const APP_ICON_SIZE = 42;

export default function Login() {
  const router = useRouter();
  const { t } = useTranslation(["screens"]);
  const { requestOtc } = useAuth();

  const { icons } = useAssets();

  const [, setOTCToken] = useOTCTokenAtom();

  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const onSubmitEmail = useCallback(
    async (newEmail: string) => {
      try {
        setIsLoading(true);
        const response = await requestOtc(newEmail);

        setOTCToken(response.token);

        router.push(`/verifyOTC?email=${newEmail}`);
      } catch (error) {
        console.error("Error submitting email:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [requestOtc, router, setOTCToken],
  );

  const onSubmitEditing = useCallback(
    (e: NativeSyntheticEvent<TextInputSubmitEditingEventData>) => {
      try {
        onSubmitEmail(e.nativeEvent.text);
      } catch (error) {
        console.error("Error submitting email:", error);
      }
    },
    [onSubmitEmail],
  );

  return (
    <Layout isLoading={isLoading}>
      <KeyboardAwareScrollBox
        contentContainerStyle={{ flex: 1, justifyContent: "center" }}
      >
        <Image
          source={icons.appIcon}
          width={APP_ICON_SIZE}
          height={APP_ICON_SIZE}
          alignSelf="center"
        />
        <Heading
          fontSize={20}
          textAlign="center"
          marginTop="$3xl"
          marginBottom="$5xl"
        >
          {t("screens:login.title")}
        </Heading>
        <Text marginBottom="$xl">{"Email"}</Text>
        <LoginInput
          value={email}
          onChangeText={setEmail}
          returnKeyType="send"
          onSubmitEditing={onSubmitEditing}
          marginBottom="$2xl"
          autoCapitalize="none"
          autoComplete="email"
          keyboardType="email-address"
          textContentType="emailAddress"
        />
        <LoginButton onPress={() => onSubmitEmail(email)} marginBottom="$5xl">
          <Button.Text>{t("screens:login.cta")}</Button.Text>
        </LoginButton>
        <Text
          fontFamily="$regular"
          textAlign="center"
          color="$infoText"
          onPress={() => openURL("mailto:<EMAIL>")}
          pressStyle={{
            scale: 0.98,
          }}
        >
          {t("screens:login.no_account")}{" "}
          <Text
            textAlign="center"
            color="$infoTextHighlight"
            textDecorationLine="underline"
          >
            {t("screens:login.contact_us")}
          </Text>
        </Text>
      </KeyboardAwareScrollBox>
    </Layout>
  );
}
