declare module "*.svg" {
  import type { FC } from "react";
  import type { StyleProp, ViewStyle } from "react-native";
  import type { SvgProps } from "react-native-svg";

  interface SvgStyle extends StyleProp<ViewStyle> {
    color?: string;
  }

  interface SvgViewProps extends SvgProps {
    size?: number;
    style?: SvgStyle;
  }

  const content: FC<SvgViewProps>;

  export default content;
}

declare module "*.jpg";
declare module "*.jpeg";
declare module "*.png";
