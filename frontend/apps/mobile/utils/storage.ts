import { MMKV } from "react-native-mmkv";

export type Key =
  // GENERAL
  | "theme"
  // API
  | "accessToken"
  | "refreshToken";

const secureStorage = new MMKV({
  id: `pearl-secure-storage`,
  encryptionKey: "pearl-secure-storage-key",
});

const getString = (key: Key) => secureStorage.getString(key);

const getBoolean = (key: Key) => secureStorage.getBoolean(key);

const set = (key: Key, value: string | number | boolean) =>
  secureStorage.set(key, value);

const del = (key: Key) => secureStorage.delete(key);

const clear = () => {
  const secureStorageKeys = secureStorage.getAllKeys();
  secureStorageKeys.forEach((key) => secureStorage.delete(key));
};

export const Storage = {
  getString,
  getBoolean,
  set,
  del,
  clear,
};
