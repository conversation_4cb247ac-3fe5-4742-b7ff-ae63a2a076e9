import * as ExpoHaptics from "expo-haptics";

export type HapticsStyle =
  | "selection"
  | "success"
  | "error"
  | "warning"
  | "impact-medium"
  | "impact-light"
  | "impact-heavy";

export const hapticFeedback = (style: HapticsStyle = "impact-light"): void => {
  switch (style) {
    case "selection":
      ExpoHaptics.selectionAsync();
      break;
    case "success":
      ExpoHaptics.notificationAsync(
        ExpoHaptics.NotificationFeedbackType.Success,
      );
      break;
    case "warning":
      ExpoHaptics.notificationAsync(
        ExpoHaptics.NotificationFeedbackType.Warning,
      );
      break;
    case "error":
      ExpoHaptics.notificationAsync(ExpoHaptics.NotificationFeedbackType.Error);
      break;
    case "impact-medium":
      ExpoHaptics.impactAsync(ExpoHaptics.ImpactFeedbackStyle.Medium);
      break;
    case "impact-light":
      ExpoHaptics.impactAsync(ExpoHaptics.ImpactFeedbackStyle.Light);
      break;
    case "impact-heavy":
      ExpoHaptics.impactAsync(ExpoHaptics.ImpactFeedbackStyle.Heavy);
      break;
  }
};
