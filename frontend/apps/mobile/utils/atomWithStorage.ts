import type { Key } from "@utils/storage";
import { Storage } from "@utils/storage";
import {
  atomWithStorage as JotaiAtomWithStorage,
  createJSONStorage,
} from "jotai/utils";

const getItem = <T>(key: string): T | null => {
  const value = Storage.getString(key as Key);

  return value ? JSON.parse(value) : null;
};

const setItem = <T>(key: string, value: T) => {
  Storage.set(key as Key, JSON.stringify(value));
};

const removeItem = (key: string) => {
  Storage.del(key as Key);
};

export const atomWithStorage = <T>(key: Key, initialValue: T) =>
  JotaiAtomWithStorage<T>(
    key,
    initialValue,
    createJSONStorage<T>(() => ({
      getItem,
      setItem,
      removeItem,
      clearAll: Storage.clear,
    })),
    { getOnInit: true },
  );
