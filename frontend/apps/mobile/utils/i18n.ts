import dayjs from "dayjs";
import * as Localization from "expo-localization";
import i18n from "i18next";
import { initReactI18next } from "react-i18next";

import resources from "@pearl/locales";

export type Language = (typeof Languages)[keyof typeof Languages];

export const Languages = {
  en: "en",
  fr: "fr",
} as const;

if ("__setDefaultTimeZone" in Intl.DateTimeFormat) {
  const timeZone = Localization.getCalendars()[0]?.timeZone;

  // TODO: Fix this
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  Intl.DateTimeFormat.__setDefaultTimeZone(timeZone);
}

export const deviceLocaleLanguageCode =
  Localization.getLocales()[0]?.languageCode || "";

export const DEVICE_LOCALE_TAG = Localization.getLocales()[0]?.languageTag;

export const SUPPORTED_LANGUAGES = ["en", "fr"];

export const FALLBACK_LANGUAGE: Language = "en";

export const DEVICE_LANGUAGE = SUPPORTED_LANGUAGES.includes(
  deviceLocaleLanguageCode,
)
  ? deviceLocaleLanguageCode
  : FALLBACK_LANGUAGE;

const languageDetector = {
  type: "languageDetector",
  async: true,

  init: () => {},
  detect: (callback: (language: string) => void) => {
    dayjs.locale(DEVICE_LANGUAGE);

    return callback(DEVICE_LANGUAGE);
  },
} as const;

export const defaultNS = "screens";

i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    resources,
    defaultNS,
    ns: Object.keys(resources.en),
    fallbackLng: FALLBACK_LANGUAGE,
    supportedLngs: SUPPORTED_LANGUAGES,
    react: {
      useSuspense: false,
    },
  });

export default i18n;
