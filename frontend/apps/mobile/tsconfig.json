{"extends": "@pearl/tsconfig/tsconfig.react-native.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./*"], "@app/*": ["./app/*"], "@assets/*": ["./assets/*"], "@components/*": ["./components/*"], "@constants/*": ["./constants/*"], "@hooks/*": ["./hooks/*"], "@services/*": ["./services/*"], "@store/*": ["./store/*"], "@theme/*": ["./theme/*"], "@utils/*": ["./utils/*"], "@state/*": ["./state/*"]}}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules"]}