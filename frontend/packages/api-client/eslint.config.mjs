import baseConfig from "@pearl/eslint-config/base";

/** @type {import('typescript-eslint').Config} */
export default [
  {
    ignores: ["**/node_modules/**", "dist/**"],
  },
  ...baseConfig,
  {
    rules: {
      "@typescript-eslint/no-unnecessary-condition": "error",
      "@typescript-eslint/no-non-null-assertion": "error",
      "@typescript-eslint/no-base-to-string": "error",
      "@typescript-eslint/restrict-plus-operands": "error",
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-redundant-type-constituents": "error",
      "@typescript-eslint/no-unsafe-enum-comparison": "error",
      "@typescript-eslint/no-unnecessary-type-assertion": "error",
    },
  },
];
