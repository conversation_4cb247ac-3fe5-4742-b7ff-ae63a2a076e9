import type { TokenResponse } from "../types/TokenResponse.type";
import type { TokenStorageAdapter } from "../types/TokenStorageAdapter.type";

export class ApiClient {
  baseUrl: string;
  private tokenStorage: TokenStorageAdapter;
  private refreshPromise: Promise<null> | null = null;
  private fetcher: (url: string, init?: RequestInit) => Promise<Response>;

  constructor(
    baseUrl: string,
    tokenStorage: TokenStorageAdapter,
    fetcher?: (url: string, init?: RequestInit) => Promise<Response>,
  ) {
    this.baseUrl = baseUrl;
    this.tokenStorage = tokenStorage;
    this.fetcher = fetcher || ((...args) => fetch(...args));
  }

  // Token refresh logic
  async refreshAccessToken(): Promise<null> {
    // Ensure only one refresh request happens at a time
    if (!this.refreshPromise) {
      this.refreshPromise = (async (): Promise<null> => {
        try {
          const refreshToken = this.tokenStorage.getRefreshToken();
          if (!refreshToken) {
            throw new Error("No refresh token available");
          }

          const response = await this.fetcher(`${this.baseUrl}/auth/refresh`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ refresh_token: refreshToken }),
          });

          if (!response.ok) {
            throw new Error("Failed to refresh token");
          }

          const data: TokenResponse = await response.json();
          this.tokenStorage.saveTokens(data.access_token, data.refresh_token);
          return null;
        } catch (error) {
          // Clear tokens on refresh failure
          this.tokenStorage.clearTokens();
          throw error;
        } finally {
          this.refreshPromise = null;
        }
      })();
    }

    return this.refreshPromise;
  }

  async authenticatedFetch(
    input: RequestInfo | URL,
    options: RequestInit = {},
  ): Promise<Response> {
    // TODO: Check case where input is a URL object !
    // eslint-disable-next-line @typescript-eslint/no-base-to-string
    const endpoint = typeof input === "string" ? input : input.toString();
    const url = `${this.baseUrl}${endpoint}`;

    // Add authorization header if we have a token
    const accessToken = this.tokenStorage.getAccessToken();
    if (accessToken) {
      options.headers = {
        ...options.headers,
        Authorization: `Bearer ${accessToken}`,
      };
    }

    let response = await this.fetcher(url, options);

    // Handle 401 Unauthorized - Token expired
    if (response.status === 401) {
      try {
        // Wait for token refresh
        await this.refreshAccessToken();

        // Retry the request with new token
        const newAccessToken = this.tokenStorage.getAccessToken();
        options.headers = {
          ...options.headers,
          Authorization: `Bearer ${newAccessToken}`,
        };
        response = await this.fetcher(url, options);
      } catch {
        throw new Error("Authentication failed. Please log in again.");
      }
    }

    // Handle non-OK responses
    if (!response.ok) {
      let errorData = null;
      try {
        errorData = await response.json();
      } catch {
        // Not JSON, ignore
      }

      const error = new Error(
        `API error: ${response.status} ${response.statusText}`,
      );
      (error as unknown as { data?: unknown }).data = errorData;
      throw error;
    }

    return response;
  }

  // Base request method
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<T> {
    const response = await this.authenticatedFetch(endpoint, options);

    // Parse JSON if content exists
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      if (response.status === 204) {
        // No content response
        return null as unknown as T;
      }
      return response.json() as Promise<T>;
    }

    return response as unknown as T;
  }

  // HTTP methods
  async get<T>(
    endpoint: string,
    params: Record<string, string> = {},
  ): Promise<T> {
    const queryString = new URLSearchParams(params).toString();
    const url = queryString ? `${endpoint}?${queryString}` : endpoint;

    return this.request<T>(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
  }

  async post<T>(
    endpoint: string,
    data: Record<string, unknown> = {},
    signal?: AbortSignal,
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
      signal,
    });
  }

  async put<T>(
    endpoint: string,
    data: Record<string, unknown> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
  }

  async patch<T>(
    endpoint: string,
    data: Record<string, unknown> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
  }

  async delete<T>(
    endpoint: string,
    data: Record<string, unknown> = {},
  ): Promise<T> {
    const options: RequestInit = {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    };

    // Add body if data is provided
    if (Object.keys(data).length > 0) {
      options.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, options);
  }

  async uploadFile<T>(endpoint: string, file: File): Promise<T> {
    const formData = new FormData();
    formData.append("file", file);

    const response = await this.authenticatedFetch(endpoint, {
      method: "POST",
      body: formData,
    });

    // Parse JSON if content exists
    const contentType = response.headers.get("content-type");
    if (contentType && contentType.includes("application/json")) {
      if (response.status === 204) {
        // No content response
        return null as unknown as T;
      }
      return response.json() as Promise<T>;
    }

    return response as unknown as T;
  }
}
