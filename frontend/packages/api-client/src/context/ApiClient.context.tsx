import type { ReactNode } from "react";
import { createContext, useContext } from "react";

import type { ApiClient } from "../client/ApiClient";
import type { TokenStorageAdapter } from "../types/TokenStorageAdapter.type";

interface ApiClientContextValue {
  apiClient: ApiClient;
  tokenStorage: TokenStorageAdapter;
}

const ApiClientContext = createContext<ApiClientContextValue | null>(null);

export function ApiClientProvider({
  apiClient,
  tokenStorage,
  children,
}: {
  apiClient: ApiClient;
  tokenStorage: TokenStorageAdapter;
  children: ReactNode;
}) {
  return (
    <ApiClientContext.Provider value={{ apiClient, tokenStorage }}>
      {children}
    </ApiClientContext.Provider>
  );
}

export const useApiClient = (): ApiClientContextValue => {
  const context = useContext(ApiClientContext);

  if (!context) {
    throw new Error("useApiClient must be used within an ApiClientProvider");
  }

  return context;
};
