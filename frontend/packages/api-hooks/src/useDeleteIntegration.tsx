import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export const useDeleteIntegration = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (integrationId: string) =>
      apiClient.delete(`/workspace/${integrationId}/connection`),

    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ["integrations"] }),
  });
};
