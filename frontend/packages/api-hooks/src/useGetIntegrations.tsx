import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export interface Integration {
  id: string;
  name: string;
  description: string;
  integrationType: string;
  source: string;
  isActive: boolean;
}

export type Integrations = {
  activeIntegrations: Integration[];
  availableIntegrations: Integration[];
};

export const useGetIntegrations = () => {
  const { apiClient } = useApiClient();

  return useQuery<Integrations>({
    queryKey: ["integrations"],
    queryFn: () => apiClient.get<Integrations>(`/workspace/integrations`),
  });
};
