import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export const useDeleteTeamDocument = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (documentId: string) =>
      apiClient.delete(`/workspace/admin/team-documents/${documentId}`),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: ["teamDocuments"] });
    },
  });
};
