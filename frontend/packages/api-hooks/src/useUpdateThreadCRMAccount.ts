import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

interface UpdateThreadCRMAccountParams {
  threadId: string;
  crm_account_id: string;
}

export const useUpdateThreadCRMAccount = () => {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ threadId, crm_account_id }: UpdateThreadCRMAccountParams) =>
      apiClient.put(`/agent/threads/${threadId}/crm_account_id`, {
        crm_account_id,
      }),
    onSuccess: async (_, { threadId }) => {
      await queryClient.invalidateQueries({ queryKey: ["threads"] });
      await queryClient.invalidateQueries({ queryKey: ["thread", threadId] });
    },
    onError: (error) =>
      console.error("Failed to update thread CRM account:", error),
  });
};
