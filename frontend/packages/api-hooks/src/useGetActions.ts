import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type Action = {
  title: string;
  description: string;
  prompt: string;
};

export const useGetActions = (crm_account_id?: string) => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["actions", crm_account_id],
    queryFn: async () => {
      const url = crm_account_id
        ? `/agent/actions?crm_account_id=${crm_account_id}`
        : `/agent/actions`;
      const response = await apiClient.get<Action[]>(url);
      return response;
    },
  });
};
