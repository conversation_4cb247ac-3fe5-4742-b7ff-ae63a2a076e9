import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export function useSyncUserAccounts() {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => apiClient.get("/workspace/sync_accounts"),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ["userAccounts"] }),
  });
}
