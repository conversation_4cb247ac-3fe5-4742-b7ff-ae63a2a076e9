import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type Metrics = {
  quota: number;
  closedWon: number;
  pipeline: number;
  forecast: number;
};

export function useGetMetrics() {
  const { apiClient } = useApiClient();

  return useQuery<Metrics>({
    queryKey: ["metrics"],
    queryFn: () => apiClient.get<Metrics>("/workspace/metrics"),
  });
}
