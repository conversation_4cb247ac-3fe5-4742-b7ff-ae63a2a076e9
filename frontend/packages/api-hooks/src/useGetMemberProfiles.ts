import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

import type { MemberProfile } from "./useGetMemberProfileMe";

type MemberProfiles = {
  members: MemberProfile[];
};

export const useGetMemberProfiles = () => {
  const { apiClient } = useApiClient();

  return useQuery<MemberProfiles>({
    queryKey: ["memberProfiles"],
    queryFn: () =>
      apiClient.get<MemberProfiles>(`/workspace/admin/member_profiles`),
  });
};
