import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type Summary = {
  crmAccountId: string;
  summary: string;
};

export const useGetSummary = (accountId?: string) => {
  const { apiClient } = useApiClient();

  return useQuery<Summary>({
    queryKey: ["summary", accountId],
    queryFn: async () => {
      const response = await apiClient.get<Summary>(
        `/agent/accounts/${accountId}/summary`,
      );
      return response;
    },
    enabled: !!accountId,
  });
};
