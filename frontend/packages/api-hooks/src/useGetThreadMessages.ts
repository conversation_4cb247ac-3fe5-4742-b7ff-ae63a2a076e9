import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@pearl/api-client";

export type ThreadMessage = {
  role: "user" | "assistant";
  content: string;
  id: string;
};

export type ThreadMessageResponse = {
  messages: ThreadMessage[];
};

export const useGetThreadMessages = (threadId: string | null | undefined) => {
  const { apiClient } = useApiClient();

  return useQuery<ThreadMessageResponse>({
    queryKey: ["threadMessages", threadId],
    queryFn: () =>
      apiClient.get<ThreadMessageResponse>(
        `/agent/threads/${threadId}/messages`,
      ),
    enabled: !!threadId,
  });
};
