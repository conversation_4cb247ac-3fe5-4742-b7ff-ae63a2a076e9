import { useCallback, useRef, useState } from "react";

import { useApiClient } from "@pearl/api-client";

export const useHubspotCallback = () => {
  const { apiClient } = useApiClient();
  const [isLoading, setIsLoading] = useState(true);
  const [isImporting, setIsImporting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const hasProcessed = useRef(false);

  const processCallback = useCallback(
    async (code: string, state: string) => {
      if (hasProcessed.current || !code || !state) {
        return;
      }

      hasProcessed.current = true;

      try {
        const encodedCode = encodeURIComponent(code);
        const encodedState = encodeURIComponent(state);

        // Step 1: Process OAuth callback
        await apiClient.get(
          `/workspace/hubspot/callback?code=${encodedCode}&state=${encodedState}`,
        );

        // Wait to show the connecting step
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Step 2: Change to importing accounts step
        setIsLoading(false);
        setIsImporting(true);

        // Step 3: Sync accounts
        await apiClient.get("/workspace/sync_accounts");

        // Wait to show the importing step
        await new Promise((resolve) => setTimeout(resolve, 2000));

        setIsImporting(false);
        setIsSuccess(true);
      } catch (error) {
        console.log(`🚀 ~ HubSpot callback error:`, error);

        // Wait a bit so it doesn't flash too quickly
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setIsLoading(false);
        setIsImporting(false);
        setIsError(true);
        setErrorMessage(
          error instanceof Error ? error.message : "API call failed",
        );
      }
    },
    [apiClient],
  );

  return {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  };
};
