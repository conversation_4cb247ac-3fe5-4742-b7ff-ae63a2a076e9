{"name": "@pearl/locales", "version": "0.0.1", "private": true, "type": "module", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc --watch", "lint": "eslint src/ --cache --cache-location .cache/.eslintcache", "lint:fix": "eslint src/ --fix --cache --cache-location .cache/.eslintcache", "format": "prettier . --check --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore", "format:fix": "prettier . --write --cache --cache-location .cache/.prettiercache --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}}, "devDependencies": {"@pearl/eslint-config": "workspace:*", "@pearl/prettier-config": "workspace:*", "@pearl/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@pearl/prettier-config"}