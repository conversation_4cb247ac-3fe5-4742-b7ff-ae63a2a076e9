# Multi-stage Dockerfile for Pearl Frontend
FROM node:22-alpine as base

# Install pnpm
RUN corepack enable && corepack prepare pnpm@10.11.1 --activate

# Set working directory
WORKDIR /app

# Development stage
FROM base as development

# Copy all files first (simpler approach)
COPY . .

# Install all dependencies (including dev dependencies) with auto-confirmation
RUN pnpm install

EXPOSE 3000

# Change to the web app directory and run dev
WORKDIR /app/apps/web
CMD ["pnpm", "dev"]

# Build stage
FROM base as builder

# Copy all files
COPY . .

# Install all dependencies
RUN pnpm install

# Build the application
RUN pnpm build

# Production stage
FROM node:22-alpine as production

# Install pnpm
RUN corepack enable && corepack prepare pnpm@10.11.1 --activate

WORKDIR /app

# Copy package files and install production dependencies
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY . .
RUN pnpm install --prod

# Copy built application from builder stage
COPY --from=builder /app/apps/web/.next ./apps/web/.next
COPY --from=builder /app/apps/web/public ./apps/web/public

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Change ownership of the app directory
RUN chown -R nextjs:nodejs /app
USER nextjs

EXPOSE 3000

CMD ["pnpm", "start"]
