# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
docs/
*.md

# IDE and editor files
.idea/
.vscode/
.cursor/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Dependencies
node_modules/
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Testing
coverage/
.nyc_output

# Next.js
.next/
out/
next-env.d.ts

# Production
/build
dist/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Runtime data
.cache
.turbo

# Environment variables
.env*

# Vercel
.vercel

# TypeScript
*.tsbuildinfo

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Mobile specific (not needed for web)
apps/mobile/
android/
ios/
.expo/

# Claude
CLAUDE.md
