# Pearl Application

Pearl is a modern full-stack application with a FastAPI backend and Next.js frontend, designed for AI/ML workflows with vector database support.

## ⚡ Quick Reference

```bash
# Start development (choose one)
docker-compose up        # Using Docker
podman-compose up        # Using Podman

# Seed
make seed
make seed-clear # Clear the database

# View logs
docker-compose logs -f backend

# Run tests
docker-compose exec backend uv run pytest

# Stop services
docker-compose down
```

## 🚀 Quick Start

### Prerequisites
- **Docker** with Docker Compose OR **Podman** with podman-compose

### 1. Initial Setup
```bash
# Copy environment template
cp .env.example .env

# IMPORTANT: Edit .env with your configuration
# At minimum, set your API keys and database credentials
```

### 2. Start Development Environment
```bash
# Using Docker Compose
docker-compose up

# OR using Podman Compose
podman-compose up
```

### 3. Access Services
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: localhost:5432

## 📁 Project Structure

```
pearl/
├── docker-compose.yml          # Development environment
├── .env.example               # Environment template
├── scripts/
│   └── init-db.sql           # Database initialization
├── backend/                   # FastAPI backend
│   ├── Dockerfile            # Backend container
│   ├── uv.lock              # Python dependencies (uv)
│   ├── pyproject.toml        # Python project config
│   └── app/                  # Application code
└── frontend/                  # Next.js frontend
    ├── Dockerfile            # Frontend container
    ├── package.json          # Node.js dependencies
    ├── apps/web/             # Main web application
    └── packages/             # Shared packages
```

## 🏗 Architecture

### Services
- **Database**: PostgreSQL 16 with pgvector extension for vector operations
- **Backend**: FastAPI with uv package manager (fast Python dependencies)
- **Frontend**: Next.js with PNPM monorepo structure

### Key Features
- **Vector Database**: pgvector extension for AI/ML embeddings
- **Hot Reload**: Development environment with live code updates
- **Multi-stage Builds**: Optimized Docker containers
- **Modern Tooling**: uv for Python, PNPM for Node.js

## 🛠 Development Commands

Replace `docker-compose` with `podman-compose` if using Podman.

### Core Development
```bash
docker-compose up                    # Start development environment
docker-compose up --build           # Start with rebuild
docker-compose up -d                # Start in background
docker-compose down                  # Stop all services
docker-compose restart              # Restart all services
```

### Logging and Debugging
```bash
docker-compose logs -f               # Show all logs
docker-compose logs -f backend       # Show backend logs only
docker-compose logs -f frontend      # Show frontend logs only
docker-compose logs -f db            # Show database logs only
docker-compose ps                    # Show service status
```

### Container Interaction
```bash
docker-compose exec backend /bin/bash    # Open shell in backend container
docker-compose exec frontend /bin/sh     # Open shell in frontend container
docker-compose exec db /bin/bash         # Open shell in database container
```

### Development Tasks
```bash
docker-compose exec backend uv run pytest                    # Run backend tests
docker-compose exec backend uv run alembic upgrade head      # Run database migrations
docker-compose build                                         # Build all images
docker-compose down -v --rmi all                            # Clean up everything
```

### Database Operations
```bash
docker-compose exec db psql -U pearl_user -d pearl          # Connect to database
```

## 🔧 Environment Configuration

### Required Environment Variables
**IMPORTANT**: The docker-compose.yml file uses environment variables from your `.env` file.
Edit `.env` file with your configuration:

```bash
# Database
POSTGRES_USER=pearl_user
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=pearl

# Backend API Keys
OPENAI_API_KEY=your_openai_key
GEMINI_API_KEY=your_gemini_key
MISTRAL_API_KEY=your_mistral_key
LINKUP_API_KEY=your_linkup_key

# Authentication
AUTH_JWT_SECRET_KEY=your-secure-jwt-secret-key
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Frontend
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000

# External Integrations
SALESFORCE_REDIRECT_URI=http://localhost:3000/onboarding/salesforce/callback
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key
LANGFUSE_SECRET_KEY=your_langfuse_secret_key
```

## 🧪 Development Workflow

### 1. Start Development
```bash
docker-compose up
# OR
podman-compose up
```

### 2. Make Changes
- Edit backend code in `backend/app/`
- Edit frontend code in `frontend/apps/web/`
- Changes are automatically reloaded

### 3. Run Tests
```bash
docker-compose exec backend uv run pytest
```

### 4. Debug Issues
```bash
docker-compose logs -f backend           # Check backend logs
docker-compose exec backend /bin/bash    # Open backend shell for debugging
```

### 5. Database Operations
```bash
docker-compose exec backend uv run alembic upgrade head    # Run database migrations
docker-compose exec db psql -U pearl_user -d pearl        # Connect to database with psql
```

## 🐳 Container Runtime Options

Pearl supports both Docker and Podman:

### Using Docker
```bash
# Start services
docker-compose up

# View logs
docker-compose logs -f backend

# Run commands in containers
docker-compose exec backend uv run pytest
docker-compose exec backend uv run alembic upgrade head
docker-compose exec backend /bin/bash

# Stop services
docker-compose down
```

### Using Podman
```bash
# Start services
podman-compose up

# View logs
podman-compose logs -f backend

# Run commands in containers
podman-compose exec backend uv run pytest
podman-compose exec backend uv run alembic upgrade head
podman-compose exec backend /bin/bash

# Stop services
podman-compose down
```

## 🛠 Technology Stack

### Backend
- **FastAPI** - Modern Python web framework
- **uv** - Fast Python package manager
- **PostgreSQL** - Primary database
- **pgvector** - Vector database extension for AI/ML
- **Alembic** - Database migrations
- **SQLAlchemy** - ORM with async support

### Frontend
- **Next.js** - React framework
- **PNPM** - Fast package manager
- **Turbo** - Monorepo build system
- **TypeScript** - Type safety

### Infrastructure
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration

## 🚨 Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Change ports in .env file
   BACKEND_PORT=8001
   FRONTEND_PORT=3001
   ```

2. **Database connection issues**
   ```bash
   docker-compose ps              # Check service status
   docker-compose logs -f db      # Check database logs
   ```

3. **Build failures**
   ```bash
   docker-compose down -v --rmi all    # Clean up everything
   docker-compose up --build           # Rebuild from scratch
   ```

4. **Container runtime issues**
   ```bash
   # If using Podman, replace docker-compose with podman-compose
   podman-compose up --build
   ```

5. **Cache directory issues**
   ```bash
   # If you see cache folders in your project, remove them
   rm -rf frontend/.pnpm-store
   rm -rf frontend/node-compile-cache
   # These are already in .gitignore and shouldn't be committed
   ```

### Reset Everything
```bash
docker-compose down -v           # Stop and remove volumes
docker-compose up --build        # Rebuild and restart
```

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `docker-compose exec backend uv run pytest`
5. Run linting: `docker-compose exec backend uv run ruff check`
6. Submit a pull request

### Code Style
- **Backend**: Uses Ruff for linting and formatting
- **Frontend**: Uses ESLint and Prettier
- **Commits**: Use conventional commit messages

## 📚 Additional Resources

### Backend Development
- **FastAPI Documentation**: https://fastapi.tiangolo.com/
- **uv Documentation**: https://docs.astral.sh/uv/
- **pgvector Documentation**: https://github.com/pgvector/pgvector

### Frontend Development
- **Next.js Documentation**: https://nextjs.org/docs
- **PNPM Documentation**: https://pnpm.io/

### Database
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/
- **Alembic Documentation**: https://alembic.sqlalchemy.org/

---

**Pearl Application** - Modern full-stack development with AI/ML capabilities
