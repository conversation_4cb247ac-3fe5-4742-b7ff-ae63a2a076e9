# Pearl Application Environment Variables
# Copy this file to .env and update the values as needed

# Database Configuration
POSTGRES_USER=pearl_user
POSTGRES_PASSWORD=pearl_pass
POSTGRES_DB=pearl
DB_PORT=5432

# Application Ports
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Backend Environment
# Development: SSL disabled for local Docker PostgreSQL
DATABASE_URL=******************************************/pearl?sslmode=disable
ASYNC_DATABASE_URL=postgresql+asyncpg://pearl_user:pearl_pass@db:5432/pearl

# Production: Use SSL for remote databases
# DATABASE_URL=*************************************/pearl?sslmode=require
# ASYNC_DATABASE_URL=postgresql+asyncpg://user:pass@prod-host:5432/pearl?ssl=require
SECRET_KEY=your-secure-secret-key-here
SESSION_SECRET_KEY=your-secure-session-secret-key-here

# Authentication settings
AUTH_JWT_SECRET_KEY=your-secure-jwt-secret-key-here
AUTH_JWT_ALGORITHM=HS256
AUTH_ACCESS_TOKEN_EXPIRE_MINUTES=30
AUTH_REFRESH_TOKEN_EXPIRE_DAYS=7
AUTH_MAX_REFRESH_TOKENS=5
AUTH_ONE_TIME_CREDENTIALS_EXPIRE_MINUTES=10

# Frontend Environment
NODE_ENV=development
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_PEARL_API_URL=http://localhost:8000

# External API Keys
OPENAI_API_KEY=
GEMINI_API_KEY=
LINKUP_API_KEY=
MISTRAL_API_KEY=

# Salesforce Integration
SALESFORCE_REDIRECT_URI=http://localhost:3000/onboarding/salesforce/callback
SALESFORCE_AUTH_URL=https://login.salesforce.com/services/oauth2/authorize
SALESFORCE_TOKEN_URL=https://login.salesforce.com/services/oauth2/token

# Email Configuration
MAILER_EMAIL_CLIENT=local
MAILER_CUSTOMERIO_API_KEY=xxx

# Langfuse Configuration
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=

# Google OAuth Integration
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_CLIENT_SECRET=

# Google Calendar Integration
GOOGLE_CALENDAR_AUTH_URL=
GOOGLE_CALENDAR_TOKEN_URL=
GOOGLE_CALENDAR_REDIRECT_URI=
