# Pearl Development Commands
.PHONY: help seed seed-clear seed-skip seed-clear-skip up down logs

# Default target
help:
	@echo "Pearl Development Commands:"
	@echo ""
	@echo "Database Seeding:"
	@echo "  seed              - Run database seeder with user prompting"
	@echo "  seed-clear        - Clear database and seed with user prompting"  
	@echo "  seed-skip         - Run database seeder without user prompting"
	@echo "  seed-clear-skip   - Clear database and seed without user prompting"
	@echo ""
	@echo "Docker Management:"
	@echo "  up                - Start all services"
	@echo "  down              - Stop all services"
	@echo "  logs              - View logs from all services"
	@echo "  logs-backend      - View backend logs only"
	@echo ""

# Database seeding commands
seed:
	@echo "🌱 Running database seeder with user prompting..."
	docker compose exec -it backend uv run python scripts/seed.py

seed-clear:
	@echo "🗑️ Clearing database and seeding with user prompting..."
	docker compose exec -it backend uv run python scripts/seed.py --clear

seed-skip:
	@echo "🌱 Running database seeder (skipping user creation)..."
	docker compose exec -it backend uv run python scripts/seed.py --skip-user

seed-clear-skip:
	@echo "🗑️ Clearing database and seeding (skipping user creation)..."
	docker compose exec -it backend uv run python scripts/seed.py --clear --skip-user

# Docker management commands
up:
	docker compose up -d

down:
	docker compose down

logs:
	docker compose logs -f

logs-backend:
	docker compose logs -f backend